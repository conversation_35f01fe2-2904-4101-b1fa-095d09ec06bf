import { defineConfig } from "vite";
import path from "path";
import react from "@vitejs/plugin-react";
import { createHtmlPlugin } from "vite-plugin-html";
import { config } from "dotenv";
import legacy from "@vitejs/plugin-legacy";

// 加载 .env 文件
config({ path: ".env", override: true });

// 加载 .env.local 文件
// config({ path: ".env.local", override: true });

console.log(process.env.VITE_BASE_URL);

export default defineConfig({
  server: {
    host: true,
    proxy: {
      "/api": {
        target: process.env.VITE_BASE_URL,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\//, ""),
      },
    },
  },
  plugins: [
    legacy({
      modernPolyfills: true,
      renderLegacyChunks: false,
      modernTargets: ["defaults", "iOS>=15"],
    }),
    react(),
    createHtmlPlugin({
      minify: true,
      inject: {
        data: {
          title: "足趣社区",
          scripts: ["https://res.openinstall.com/openinstall-lxn4bd.js"],
        },
      },
    }),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});
