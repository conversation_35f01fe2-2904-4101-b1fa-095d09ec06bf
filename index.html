<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, viewport-fit=cover, user-scalable=no"
    />
    <title><%- title %></title>
  </head>
  <body>
    <div id="root"></div>
    <% for (const script of scripts) { %>
    <script src="<%= script %>"></script>
    <% } %>
    <script>
      var customViewportCorrectionVariable = "vh";

      function setViewportProperty(doc) {
        var prevClientHeight;
        var customVar = "--" + (customViewportCorrectionVariable || "vh");
        function handleResize() {
          var clientHeight = doc.clientHeight;
          if (clientHeight === prevClientHeight) return;
          requestAnimationFrame(function updateViewportHeight() {
            doc.style.setProperty(customVar, clientHeight * 0.01 + "px");
            prevClientHeight = clientHeight;
          });
        }
        handleResize();
        return handleResize;
      }
      window.addEventListener(
        "resize",
        setViewportProperty(document.documentElement)
      );
    </script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
