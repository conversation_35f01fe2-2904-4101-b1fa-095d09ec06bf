import {
  forwardRef,
  useCallback,
  useEffect,
  useImperative<PERSON>andle,
  useRef,
  useState,
} from "react";
import { UserSearchModal } from "./work/user-search-modal";
import { TopicSearchModal } from "./work/topic-search-modal";
import { useModal } from "@/hooks/use-modal";
import { User } from "@/type/user";
import { cn } from "@/lib/utils";

// 获取光标位置
const getCursorIndex = () => {
  const selection = window.getSelection();
  return selection?.focusOffset;
};

// 获取节点
const getRangeNode = () => {
  const selection = window.getSelection();
  return selection?.focusNode;
};

// 是否展示 @
const showAt = (node: Node | null | undefined) => {
  if (!node || node.nodeType !== Node.TEXT_NODE) return false;

  const content = node.textContent || "";
  const regx = /@([^@\s]*)$/;

  const cursorIndex = getCursorIndex() || 0;

  if (cursorIndex > 0 && content[cursorIndex - 1] !== "@") return false;

  const match = regx.exec(content.slice(0, getCursorIndex()));
  return match && match.length === 2;
};

// 是否展示 #（话题）
const showHash = (node: Node | null | undefined) => {
  if (!node || node.nodeType !== Node.TEXT_NODE) return false;

  const content = node.textContent || "";
  const regx = /#([^#\s]*)$/;
  const cursorIndex = getCursorIndex() || 0;

  if (cursorIndex > 0 && content[cursorIndex - 1] !== "#") return false;

  const match = regx.exec(content.slice(0, getCursorIndex()));
  return match && match.length === 2;
};

const replaceString = (raw: string, replacer: string, symbol = "@") => {
  return symbol === "@"
    ? raw.replace(/@([^@\s]*)$/, replacer)
    : raw.replace(/#([^#\s]*)$/, replacer);
};

const createAtSpan = (user: User) => {
  const span = document.createElement("span");
  span.className = "at-mention text-[#6712ff]";
  span.textContent = `@${user.nickname} `;
  span.contentEditable = "false";
  span.setAttribute("data-user-id", user.id!);
  span.setAttribute("data-user-name", user.nickname!);

  return span;
};

// 创建话题标签
const createTopicSpan = (topic: string) => {
  const span = document.createElement("span");
  span.className = "topic-tag text-[#6712ff]";
  span.textContent = `#${topic}#`;
  span.contentEditable = "false";
  span.setAttribute("data-topic-name", topic);

  return span;
};

const replaceAtUser = (
  user: User,
  cursorIndex: number,
  node: Node | null | undefined,
  editorElement?: HTMLDivElement | null
) => {
  if (node) {
    const content = node?.textContent || "";
    const preSlice = replaceString(content.slice(0, cursorIndex), "");
    const restSlice = content.slice(cursorIndex);
    const parentNode = node?.parentNode!;
    const nextNode = node?.nextSibling;
    const previousTextNode = new Text(preSlice);
    const nextTextNode = new Text(restSlice);
    const atButton = createAtSpan(user);

    if (parentNode) {
      parentNode.removeChild(node);
    }

    if (nextNode) {
      parentNode.insertBefore(previousTextNode, nextNode);
      parentNode.insertBefore(atButton, nextNode);
      parentNode.insertBefore(nextTextNode, nextNode);
    } else {
      parentNode.appendChild(previousTextNode);
      parentNode.appendChild(atButton);
      parentNode.appendChild(nextTextNode);
    }
    const range = new Range();
    range.setStart(nextTextNode, 0);
    range.setEnd(nextTextNode, 0);
    const selection = window.getSelection();
    selection?.removeAllRanges();
    selection?.addRange(range);
  } else if (editorElement) {
    // 处理 node 为 undefined 的情况
    const atButton = createAtSpan(user);
    const textNode = new Text(" ");

    // 清空编辑器内容并添加艾特标签
    editorElement.innerHTML = "";
    editorElement.appendChild(atButton);
    editorElement.appendChild(textNode);

    // 设置光标位置到艾特标签后面
    const range = new Range();
    range.setStart(textNode, 1);
    range.setEnd(textNode, 1);
    const selection = window.getSelection();
    selection?.removeAllRanges();
    selection?.addRange(range);
  }
};

// 替换话题标签
const replaceTopic = (
  topic: string,
  cursorIndex: number,
  node: Node | null | undefined,
  editorElement?: HTMLDivElement | null
) => {
  if (node) {
    const content = node?.textContent || "";
    const preSlice = replaceString(content.slice(0, cursorIndex), "", "#");
    const restSlice = content.slice(cursorIndex);
    const parentNode = node?.parentNode!;
    const nextNode = node?.nextSibling;
    const previousTextNode = new Text(preSlice);
    const nextTextNode = new Text(restSlice);
    const topicTag = createTopicSpan(topic);

    if (parentNode) {
      parentNode.removeChild(node);
    }

    if (nextNode) {
      parentNode.insertBefore(previousTextNode, nextNode);
      parentNode.insertBefore(topicTag, nextNode);
      parentNode.insertBefore(nextTextNode, nextNode);
    } else {
      parentNode.appendChild(previousTextNode);
      parentNode.appendChild(topicTag);
      parentNode.appendChild(nextTextNode);
    }
    const range = new Range();
    range.setStart(nextTextNode, 0);
    range.setEnd(nextTextNode, 0);
    const selection = window.getSelection();
    selection?.removeAllRanges();
    selection?.addRange(range);
  } else if (editorElement) {
    // 处理 node 为 undefined 的情况
    const topicTag = createTopicSpan(topic);
    const textNode = new Text(" ");

    // 清空编辑器内容并添加话题标签
    editorElement.innerHTML = "";
    editorElement.appendChild(topicTag);
    editorElement.appendChild(textNode);

    // 设置光标位置到话题标签后面
    const range = new Range();
    range.setStart(textNode, 1);
    range.setEnd(textNode, 1);
    const selection = window.getSelection();
    selection?.removeAllRanges();
    selection?.addRange(range);
  }
};

export interface EditorRef {
  clear: () => void;
  showAt: () => void;
  showTopic: () => void;
}

interface Props {
  placeholder?: string;
  wrapperClassName?: string;
  className?: string;
  canTopic?: boolean;
  showCount?: boolean;
  maxLength?: number;
  onFocus?: () => void;
  onChangeValue?: (value: string) => void;
  onChangeAtUsers?: (list: Record<string, string>) => void;
  onChangeTopics?: (list: Record<string, string>) => void;
}

export const Editor = forwardRef<EditorRef, Props>((props, ref) => {
  const {
    placeholder = "请输入",
    wrapperClassName,
    className,
    canTopic = true,
    showCount,
    maxLength,
    onFocus,
    onChangeValue,
    onChangeAtUsers,
    onChangeTopics,
  } = props;

  const editorRef = useRef<HTMLDivElement>(null);
  const rangeNodeRef = useRef<Node | null | undefined>(undefined);
  const observerRef = useRef<MutationObserver | null>(null);
  const atUserListRef = useRef<
    Map<string, { nickname: string; count: number }>
  >(new Map());
  const topicListRef = useRef<Map<string, { name: string; count: number }>>(
    new Map()
  );
  const lastCursorIndexRef = useRef<number>(0);

  useImperativeHandle(
    ref,
    () => {
      return {
        clear: () => {
          editorRef.current!.innerText = "";
          atUserListRef.current.clear();
          topicListRef.current.clear();
        },
        showAt: () => {
          openUserModal();
        },
        showTopic: () => {
          openTopicModal();
        },
      };
    },
    []
  );

  const [showPlaceholder, setShowPlaceholder] = useState(true);

  const {
    open: openUser,
    openModal: openUserModal,
    closeModal: closeUserModal,
  } = useModal();
  const {
    open: openTopic,
    openModal: openTopicModal,
    closeModal: closeTopicModal,
  } = useModal();

  const handleInputChange = () => {
    onChangeValue?.(editorRef.current?.innerText || "");
    const text = editorRef.current?.innerText || "";

    if (text.trim()) {
      setShowPlaceholder(false);
    } else {
      setShowPlaceholder(true);

      const range = new Range();
      range.setStart(editorRef.current!, 0);
      range.setEnd(editorRef.current!, 0);
      const selection = window.getSelection();
      selection?.removeAllRanges();
      selection?.addRange(range);
    }

    rangeNodeRef.current = getRangeNode();
    lastCursorIndexRef.current = getCursorIndex() ?? 0;

    if (showAt(rangeNodeRef.current)) {
      openUserModal();
    } else if (canTopic && showHash(rangeNodeRef.current)) {
      openTopicModal();
    }
  };

  const handleSelectUser = (user: User) => {
    closeUserModal();
    replaceAtUser(
      user,
      lastCursorIndexRef.current,
      rangeNodeRef.current,
      editorRef.current
    );
    // 手动触发输入变化事件
    handleInputChange();
  };

  const handleSelectTopic = (topic: string) => {
    closeTopicModal();
    replaceTopic(
      topic,
      lastCursorIndexRef.current,
      rangeNodeRef.current,
      editorRef.current
    );
    // 手动触发输入变化事件
    handleInputChange();
  };

  const handleAtUserChange = useCallback(
    (type: "add" | "remove", id: string, nickname: string) => {
      if (type === "add") {
        const hasUser = atUserListRef.current.has(id);

        if (hasUser) {
          const user = atUserListRef.current.get(id);
          if (user) {
            user.count++;
            atUserListRef.current.set(id, user);
          }
        } else {
          atUserListRef.current.set(id, { nickname, count: 1 });
        }
      } else {
        const user = atUserListRef.current.get(id);
        if (user) {
          user.count--;
          if (user.count === 0) {
            atUserListRef.current.delete(id);
          } else {
            atUserListRef.current.set(id, user);
          }
        }
      }

      const list = Array.from(atUserListRef.current.keys()).reduce(
        (acc, id) => {
          acc[id] = atUserListRef.current.get(id)?.nickname || "";
          return acc;
        },
        {} as Record<string, string>
      );

      onChangeAtUsers?.(list);
    },
    [onChangeAtUsers]
  );

  // 处理话题变更
  const handleTopicChange = useCallback(
    (type: "add" | "remove", name: string) => {
      if (type === "add") {
        const hasTopic = topicListRef.current.has(name);

        if (hasTopic) {
          const topic = topicListRef.current.get(name);
          if (topic) {
            topic.count++;
            topicListRef.current.set(name, topic);
          }
        } else {
          topicListRef.current.set(name, { name, count: 1 });
        }
      } else {
        const topic = topicListRef.current.get(name);
        if (topic) {
          topic.count--;
          if (topic.count === 0) {
            topicListRef.current.delete(name);
          } else {
            topicListRef.current.set(name, topic);
          }
        }
      }

      const list = Array.from(topicListRef.current.keys()).reduce(
        (acc, name) => {
          acc[name] = name;
          return acc;
        },
        {} as Record<string, string>
      );

      onChangeTopics?.(list);
    },
    [onChangeTopics]
  );

  useEffect(() => {
    if (editorRef.current) {
      observerRef.current = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === "childList") {
            mutation.removedNodes.forEach((node) => {
              if (
                node instanceof HTMLSpanElement &&
                node.classList.contains("at-mention")
              ) {
                handleAtUserChange(
                  "remove",
                  node.getAttribute("data-user-id")!,
                  node.getAttribute("data-user-name")!
                );
              } else if (
                node instanceof HTMLSpanElement &&
                node.classList.contains("topic-tag")
              ) {
                handleTopicChange(
                  "remove",
                  node.getAttribute("data-topic-name")!
                );
              }
            });
            mutation.addedNodes.forEach((node) => {
              if (
                node instanceof HTMLSpanElement &&
                node.classList.contains("at-mention")
              ) {
                handleAtUserChange(
                  "add",
                  node.getAttribute("data-user-id")!,
                  node.getAttribute("data-user-name")!
                );
              } else if (
                node instanceof HTMLSpanElement &&
                node.classList.contains("topic-tag")
              ) {
                handleTopicChange("add", node.getAttribute("data-topic-name")!);
              }
            });
          }
        });
      });

      observerRef.current.observe(editorRef.current, {
        childList: true,
        subtree: true,
      });
    }

    return () => {
      observerRef.current?.disconnect();
    };
  }, [handleAtUserChange, handleTopicChange]);

  return (
    <div className={cn("editor relative z-10", wrapperClassName)}>
      <div
        ref={editorRef}
        className={cn(
          "text-sm p-[10px] focus-visible:outline-none overflow-y-auto",
          className
        )}
        contentEditable
        onInput={handleInputChange}
        onFocus={onFocus}
      />
      {showPlaceholder ? (
        <div className="absolute top-[10px] left-[10px] text-hint-color z-[-1]">
          {placeholder}
        </div>
      ) : null}
      {showCount ? (
        <div
          className={cn(
            "absolute bottom-[10px] right-[10px] text-hint-color z-[-1]",
            maxLength && (editorRef.current?.innerText.length ?? 0) > maxLength
              ? "text-red-500"
              : ""
          )}
        >
          {editorRef.current?.innerText.length}
          {maxLength ? `/${maxLength}` : null}
        </div>
      ) : null}
      <UserSearchModal
        open={openUser}
        onClose={closeUserModal}
        onFinish={handleSelectUser}
      />
      <TopicSearchModal
        open={openTopic}
        onClose={closeTopicModal}
        onFinish={handleSelectTopic}
      />
    </div>
  );
});
