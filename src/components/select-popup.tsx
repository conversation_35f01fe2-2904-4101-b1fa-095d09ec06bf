import { Divider, Popup } from "antd-mobile";
import { Fragment, PropsWithChildren, ReactNode, useState } from "react";

interface Props {
  options: {
    title: ReactNode;
    value: number | string;
    onClick?: () => void;
  }[];
}

export const SelectPopup = (props: PropsWithChildren<Props>) => {
  const { options, children } = props;

  const [visible, setVisible] = useState(false);

  return (
    <>
      <div onClick={() => setVisible(true)}>{children}</div>
      <Popup
        visible={visible}
        bodyClassName="bg-transparent"
        onMaskClick={() => setVisible(false)}
        onClose={() => setVisible(false)}
      >
        <div className="flex flex-col gap-[10px] mx-[15px]">
          <div className="flex flex-col rounded-[10px] bg-app-bar-background">
            {options.map((item, index) => (
              <Fragment key={item.value}>
                <div
                  className="py-[15px] text-center text-base text-[#9d64ff]"
                  onClick={() => {
                    setVisible(false);
                    item.onClick?.();
                  }}
                >
                  {item.title}
                </div>
                {index !== options.length - 1 ? (
                  <Divider className="border-divider m-0" />
                ) : null}
              </Fragment>
            ))}
          </div>
          <div
            className="mb-[10px] py-[15px] text-center text-base text-[#9d64ff] bg-hint-color rounded-[10px]"
            onClick={() => setVisible(false)}
          >
            取消
          </div>
        </div>
      </Popup>
    </>
  );
};
