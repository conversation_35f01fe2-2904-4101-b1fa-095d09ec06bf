import { Avatar } from "./avatar";
import { CommonIcon } from "./common-icon";

// vip 图片大小基于此缩放
const baseSize = 47;

interface Props {
  src: string;
  size: number;
  isVip?: boolean;
  borderColor?: string;
  official?: boolean;
  officialLabel?: boolean;
  anchor?: boolean;
  onClick?: () => void;
}

/**
 * 用户头像
 * @description 是否为 vip
 */
export const UserAvatar = (props: Props) => {
  const {
    src,
    size,
    isVip,
    borderColor,
    official = false,
    officialLabel = true,
    anchor = false,
    onClick,
  } = props;

  const ratio = size / baseSize;

  const borderW = ratio < 1 ? 1 : 2;

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (onClick) {
      e.stopPropagation();
      onClick();
    }
  };

  return (
    <div className="flex-none relative" onClick={handleClick}>
      <Avatar
        className="border-solid"
        size={size}
        src={src}
        style={{
          borderWidth: `${borderW}px`,
          borderColor: isVip ? "#FBBB55" : borderColor ?? "transparent",
        }}
      />
      {isVip ? (
        <span
          className="absolute z-10 bottom-0"
          style={{
            right: `${(-4 * ratio) / 16}rem`,
          }}
        >
          <CommonIcon name="vip" w={size / 3} h={size / 3} />
        </span>
      ) : null}
      {official && officialLabel ? (
        <div
          className="absolute bottom-0 inline-flex justify-center items-center bg-[#9d64ff] border border-white/50"
          style={{
            left: size / 4,
            width: size / 2,
            height: size / 4,
            borderRadius: size,
          }}
        >
          <span
            className="text-white"
            style={{
              fontSize: `${size}px`,
              zoom: 0.14,
            }}
          >
            官方
          </span>
        </div>
      ) : null}
      {!official && anchor ? (
        <div
          className="absolute bottom-0 inline-flex justify-center items-center bg-[#E34F4F] border border-white/50"
          style={{
            left: size / 4,
            width: size / 2,
            height: size / 4,
            borderRadius: size,
          }}
        >
          <span
            className="text-white"
            style={{
              fontSize: `${size}px`,
              zoom: 0.14,
            }}
          >
            主播
          </span>
        </div>
      ) : null}
    </div>
  );
};
