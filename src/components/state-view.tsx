import { PropsWithChildren } from "react";
import { SpinLoading } from "antd-mobile";
import { NoRecord } from "./no-record";

interface Props {
  isLoading?: boolean;
  isEmpty?: boolean;
}

/**
 * 处理页面各种状态问题
 */
export const StateView = (props: PropsWithChildren<Props>) => {
  const { isLoading, isEmpty, children } = props;

  if (isLoading) {
    // 加载中
    return (
      <div className="w-full h-full flex gap-1 flex-col items-center justify-center text-[#9d64ff]">
        <SpinLoading color="currentColor" style={{ "--size": "32px" }} />
      </div>
    );
  }

  if (isEmpty) {
    // 无数据时加载
    return (
      <div className="w-full h-full flex items-center justify-center">
        <NoRecord />
      </div>
    );
  }

  return children;
};
