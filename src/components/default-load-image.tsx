import { defaultImage } from "@/utils/constant";
import { Image } from "antd-mobile";
import classNames from "classnames";

interface Props {
  className?: string;
}

export const DefaultLoadImage = ({ className }: Props) => {
  return (
    <div
      className={classNames(
        "w-full h-full bg-[#eee] flex justify-center items-center",
        className
      )}
    >
      <Image
        src={defaultImage}
        width="100%"
        height="100%"
        fit="contain"
        className="max-h-[60px]"
        placeholder={null}
      />
    </div>
  );
};
