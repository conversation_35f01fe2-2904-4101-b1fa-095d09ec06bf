import { Image } from "antd-mobile";
import { CSSProperties } from "react";
import classNames from "classnames";
import { UserIcon } from "./svg-icon";

interface Props {
  src: string;
  size: number;
  className?: string;
  style?: CSSProperties;
  onClick?: () => void;
}

export const Avatar = (props: Props) => {
  const { size, src, className, style, onClick } = props;

  return (
    <div
      style={{
        flex: "none",
        width: `${size / 16}rem`,
        height: `${size / 16}rem`,
      }}
      onClick={() => {
        onClick?.();
      }}
    >
      <Image
        fit="cover"
        src={src}
        width="100%"
        height="100%"
        lazy
        className={classNames("rounded-full", className)}
        placeholder={
          <div className="bg-[#e5e5e5] w-full h-full">
            <UserIcon size={size} />
          </div>
        }
        style={style}
      />
    </div>
  );
};
