import {
  forwardRef,
  useImperative<PERSON><PERSON>le,
  useRef,
  useState,
  useCallback,
} from "react";
import type { FC, ReactNode } from "react";
import classNames from "classnames";
import { Slides, SlidesRef } from "./slides";
import { Mask, SafeArea } from "antd-mobile";
import { ResourceInfo, Slide } from "./slide";
import { mergeProps, GetContainer, renderToContainer } from "./utils";
import "./image-viewer.less";

const classPrefix = `adm-image-viewer`;

export type ImageViewerProps = {
  info?: ResourceInfo;
  maxZoom?: number | "auto";
  getContainer?: GetContainer;
  visible?: boolean;
  onClose?: () => void;
  afterClose?: () => void;
  renderFooter?: (info: ResourceInfo) => ReactNode;
  classNames?: {
    mask?: string;
    body?: string;
  };
};

const defaultProps = {
  maxZoom: 3,
  getContainer: null,
  visible: false,
};

export const ImageViewer: FC<ImageViewerProps> = (p) => {
  const props = mergeProps(defaultProps, p);

  const node = (
    <Mask
      visible={props.visible}
      disableBodyScroll={false}
      opacity={1}
      afterClose={props.afterClose}
      destroyOnClose
      className={props?.classNames?.mask}
    >
      <div
        className={classNames(
          `${classPrefix}-content`,
          props?.classNames?.body
        )}
      >
        {props.info && (
          <Slide
            info={props.info}
            onTap={props.onClose}
            maxZoom={props.maxZoom}
          />
        )}
      </div>
      {props.info && (
        <div className={`${classPrefix}-footer`}>
          {props.renderFooter?.(props.info)}
          <SafeArea position="bottom" />
        </div>
      )}
    </Mask>
  );
  return renderToContainer(props.getContainer, node);
};

export type MultiImageViewerRef = SlidesRef;

export type MultiImageViewerProps = Omit<
  ImageViewerProps,
  "info" | "renderFooter"
> & {
  list?: ResourceInfo[];
  defaultIndex?: number;
  onIndexChange?: (index: number) => void;
  renderFooter?: (info: ResourceInfo, index: number) => ReactNode;
};

const multiDefaultProps = {
  ...defaultProps,
  defaultIndex: 0,
};

export const MultiImageViewer = forwardRef<
  MultiImageViewerRef,
  MultiImageViewerProps
>((p, ref) => {
  const props = mergeProps(multiDefaultProps, p);
  const { onIndexChange } = props;
  const [index, setIndex] = useState(props.defaultIndex);

  const slidesRef = useRef<SlidesRef>(null);
  useImperativeHandle(ref, () => ({
    swipeTo: (index: number, immediate?: boolean) => {
      setIndex(index);
      slidesRef.current?.swipeTo(index, immediate);
    },
  }));

  const onSlideChange = useCallback(
    (newIndex: number) => {
      if (newIndex === index) return;
      setIndex(newIndex);
      onIndexChange?.(newIndex);
    },
    [onIndexChange, index]
  );

  const node = (
    <Mask
      visible={props.visible}
      disableBodyScroll={false}
      opacity={1}
      afterClose={props.afterClose}
      destroyOnClose
      className={props?.classNames?.mask}
    >
      <div
        className={classNames(
          `${classPrefix}-content`,
          props?.classNames?.body
        )}
      >
        {props.list && (
          <Slides
            ref={slidesRef}
            defaultIndex={index}
            onIndexChange={onSlideChange}
            list={props.list}
            onTap={props.onClose}
            maxZoom={props.maxZoom}
          />
        )}
      </div>
      {props.list && (
        <div className={`${classPrefix}-footer`}>
          {props.renderFooter?.(props.list[index], index)}
          <SafeArea position="bottom" />
        </div>
      )}
    </Mask>
  );
  return renderToContainer(props.getContainer, node);
});
