import { createUseGesture, dragAction, pinchAction } from "@use-gesture/react";
import { ReactElement, ReactPortal } from "react";
import { createPortal } from "react-dom";

export const bound = (
  position: number,
  min: number | undefined,
  max: number | undefined
) => {
  let ret = position;
  if (min !== undefined) {
    ret = Math.max(position, min);
  }
  if (max !== undefined) {
    ret = Math.min(ret, max);
  }
  return ret;
};

const canUseDom = !!(
  typeof window !== "undefined" &&
  typeof document !== "undefined" &&
  window.document &&
  window.document.createElement
);

let tenPxTester: HTMLDivElement | null = null;
let tester: HTMLDivElement | null = null;

if (canUseDom) {
  tenPxTester = document.createElement("div");
  tenPxTester.className = "adm-px-tester";
  tenPxTester.style.setProperty("--size", "10");
  document.body.appendChild(tenPxTester);
  tester = document.createElement("div");
  tester.className = "adm-px-tester";
  document.body.appendChild(tester);
}

export const convertPx = (px: number) => {
  if (tenPxTester === null || tester === null) return px;
  if (tenPxTester.getBoundingClientRect().height === 10) {
    return px;
  }
  tester.style.setProperty("--size", px.toString());
  return tester.getBoundingClientRect().height;
};

export const rubberband = (
  distance: number,
  dimension: number,
  constant: number
) => {
  return (distance * dimension * constant) / (dimension + constant * distance);
};

export const rubberbandIfOutOfBounds = (
  position: number,
  min: number,
  max: number,
  dimension: number,
  constant = 0.15
) => {
  if (constant === 0) return bound(position, min, max);
  if (position < min)
    return -rubberband(min - position, dimension, constant) + min;
  if (position > max)
    return +rubberband(position - max, dimension, constant) + max;
  return position;
};

export const useDragAndPinch = createUseGesture([dragAction, pinchAction]);

export function mergeProps<A, B>(a: A, b: B): B & A;
export function mergeProps<A, B, C>(a: A, b: B, c: C): C & B & A;
export function mergeProps<A, B, C, D>(a: A, b: B, c: C, d: D): D & C & B & A;
export function mergeProps(...items: any[]) {
  const ret: any = {};
  items.forEach((item) => {
    if (item) {
      Object.keys(item).forEach((key) => {
        if (item[key] !== undefined) {
          ret[key] = item[key];
        }
      });
    }
  });
  return ret;
}

export type GetContainer = HTMLElement | (() => HTMLElement) | null;

export function resolveContainer(
  getContainer: HTMLElement | GetContainer | undefined | null
) {
  const container =
    typeof getContainer === "function"
      ? (getContainer as () => HTMLElement)()
      : getContainer;
  return container || document.body;
}

export function renderToContainer(
  getContainer: GetContainer,
  node: ReactElement
) {
  if (canUseDom && getContainer) {
    const container = resolveContainer(getContainer);
    return createPortal(node, container) as ReactPortal;
  }
  return node;
}
