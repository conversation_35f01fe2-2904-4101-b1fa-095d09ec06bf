import { useEffect, useMemo, useRef } from "react";
import type { FC, MutableRefObject } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useSize } from "ahooks";
import * as mat from "./matrix";
import { useDragAndPinch, bound, rubberbandIfOutOfBounds } from "./utils";
import type { Matrix } from "./matrix";
import { useConfigList } from "@/hooks/use-config-list";
import videojs from "video.js";
import { DotLoading, Toast } from "antd-mobile";
import Player from "video.js/dist/types/player";
import "./slide.less";
import { ArrowIcon } from "../svg-icon";

const classPrefix = `adm-image-viewer`;

export type ResourceInfo = {
  thumbnailImage: string;
  loading?: boolean;
  url?: string;
  preview?: boolean;
  type?: "image" | "video";
};

type Props = {
  info: ResourceInfo;
  maxZoom: number | "auto";
  onTap?: () => void;
  onZoomChange?: (zoom: number) => void;
  dragLockRef?: MutableRefObject<boolean>;
};

export const Slide: FC<Props> = (props) => {
  const { dragLockRef, maxZoom, info } = props;
  const {
    thumbnailImage,
    loading = false,
    url,
    type = "image",
    preview,
  } = info;

  const initialMartix = useRef<boolean[]>([]);
  const controlRef = useRef<HTMLDivElement>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<Player>();

  const [{ matrix }, api] = useSpring(() => ({
    matrix: mat.create(),
    config: { tension: 200 },
  }));

  const controlSize = useSize(controlRef);
  const imgSize = useSize(imgRef);

  const pinchLockRef = useRef(false);

  const { getSystemConfig } = useConfigList();
  const blur = getSystemConfig("RESOURCE_PREVIEW_BLUR");

  /**
   * Calculate the min and max value of x and y
   */
  const getMinAndMax = (
    nextMatrix: Matrix
  ): {
    x: {
      position: number;
      minX: number;
      maxX: number;
    };
    y: {
      position: number;
      minY: number;
      maxY: number;
    };
  } => {
    if (!controlSize || !imgSize)
      return {
        x: {
          position: 0,
          minX: 0,
          maxX: 0,
        },
        y: {
          position: 0,
          minY: 0,
          maxY: 0,
        },
      };
    const controlLeft = -controlSize.width / 2;
    const controlTop = -controlSize.height / 2;

    const imgLeft = -imgSize.width / 2;
    const imgTop = -imgSize.height / 2;

    const zoom = mat.getScaleX(nextMatrix);
    const scaledImgWidth = zoom * imgSize.width;
    const scaledImgHeight = zoom * imgSize.height;

    const minX = controlLeft - (scaledImgWidth - controlSize.width);
    const maxX = controlLeft;

    const minY = controlTop - (scaledImgHeight - controlSize.height);
    const maxY = controlTop;

    const [x, y] = mat.apply(nextMatrix, [imgLeft, imgTop]);

    return {
      x: {
        position: x,
        minX,
        maxX,
      },
      y: {
        position: y,
        minY,
        maxY,
      },
    };
  };

  /**
   * Check if is reach the bound
   */
  const getReachBound = (
    position: number,
    min: number,
    max: number,
    buffer = 0
  ) => {
    return [position <= min - buffer, position >= max + buffer];
  };

  /**
   * Limit the matrix in the bound
   */
  const boundMatrix = (
    nextMatrix: Matrix,
    type: "translate" | "scale",
    last = false
  ): Matrix => {
    if (!controlSize || !imgSize) return nextMatrix;

    const zoom = mat.getScaleX(nextMatrix);
    const scaledImgWidth = zoom * imgSize.width;
    const scaledImgHeight = zoom * imgSize.height;

    const {
      x: { position: x, minX, maxX },
      y: { position: y, minY, maxY },
    } = getMinAndMax(nextMatrix);

    if (type === "translate") {
      let boundedX = x;
      let boundedY = y;
      if (scaledImgWidth > controlSize.width) {
        boundedX = last
          ? bound(x, minX, maxX)
          : rubberbandIfOutOfBounds(x, minX, maxX, zoom * 50);
      } else {
        boundedX = -scaledImgWidth / 2;
      }

      if (scaledImgHeight > controlSize.height) {
        boundedY = last
          ? bound(y, minY, maxY)
          : rubberbandIfOutOfBounds(y, minY, maxY, zoom * 50);
      } else {
        boundedY = -scaledImgHeight / 2;
      }

      return mat.translate(nextMatrix, boundedX - x, boundedY - y);
    }

    if (type === "scale" && last) {
      const [boundedX, boundedY] = [
        scaledImgWidth > controlSize.width
          ? bound(x, minX, maxX)
          : -scaledImgWidth / 2,
        scaledImgHeight > controlSize.height
          ? bound(y, minY, maxY)
          : -scaledImgHeight / 2,
      ];
      return mat.translate(nextMatrix, boundedX - x, boundedY - y);
    }

    return nextMatrix;
  };

  useDragAndPinch(
    {
      onDrag: (state) => {
        if (state.first) {
          const {
            x: { position: x, minX, maxX },
          } = getMinAndMax(matrix.get());
          initialMartix.current = getReachBound(x, minX, maxX);
          return;
        }
        if (state.pinching) return state.cancel();

        if (state.tap && state.elapsedTime > 0 && state.elapsedTime < 1000) {
          // 判断点击时间>0是为了过滤掉非正常操作，例如用户长按选择图片之后的取消操作（也是一次点击）
          if (type !== "video") {
            props.onTap?.();
          }
          return;
        }
        const currentZoom = mat.getScaleX(matrix.get());
        if (dragLockRef) {
          dragLockRef.current = currentZoom !== 1;
        }
        if (!pinchLockRef.current && currentZoom <= 1) {
          api.start({
            matrix: mat.create(),
          });
        } else {
          const currentMatrix = matrix.get();
          const offset = [
            state.offset[0] - mat.getTranslateX(currentMatrix),
            state.offset[1] - mat.getTranslateY(currentMatrix),
          ] as const;

          const nextMatrix = mat.translate(
            currentMatrix,
            ...(state.last
              ? ([
                  offset[0] + state.velocity[0] * state.direction[0] * 200,
                  offset[1] + state.velocity[1] * state.direction[1] * 200,
                ] as const)
              : offset)
          );

          api.start({
            matrix: boundMatrix(nextMatrix, "translate", state.last),
            immediate: !state.last,
          });

          const {
            x: { position: x, minX, maxX },
          } = getMinAndMax(nextMatrix);
          if (
            state.last &&
            initialMartix.current.some((i) => i) &&
            getReachBound(x, minX, maxX).some((i) => i)
          ) {
            if (dragLockRef) {
              dragLockRef.current = false;
            }

            api.start({
              matrix: mat.create(),
            });
          }
        }
      },
      onPinch: (state) => {
        pinchLockRef.current = !state.last;
        const [d] = state.offset;
        if (d < 0) return;
        let mergedMaxZoom: number;
        if (maxZoom === "auto") {
          mergedMaxZoom =
            controlSize && imgSize
              ? Math.max(
                  controlSize.height / imgSize.height,
                  controlSize.width / imgSize.width
                )
              : 1;
        } else {
          mergedMaxZoom = maxZoom;
        }

        const nextZoom = state.last ? bound(d, 1, mergedMaxZoom) : d;
        props.onZoomChange?.(nextZoom);
        if (state.last && nextZoom <= 1) {
          api.start({
            matrix: mat.create(),
          });
          if (dragLockRef) {
            dragLockRef.current = false;
          }
        } else {
          if (!controlSize) return;

          const currentMatrix = matrix.get();
          const currentZoom = mat.getScaleX(currentMatrix);

          const originOffsetX = state.origin[0] - controlSize.width / 2;
          const originOffsetY = state.origin[1] - controlSize.height / 2;
          let nextMatrix = mat.translate(
            currentMatrix,
            -originOffsetX,
            -originOffsetY
          );
          nextMatrix = mat.scale(nextMatrix, nextZoom / currentZoom);
          nextMatrix = mat.translate(nextMatrix, originOffsetX, originOffsetY);
          api.start({
            matrix: boundMatrix(nextMatrix, "scale", state.last),
            immediate: !state.last,
          });
          if (dragLockRef) {
            dragLockRef.current = true;
          }
        }
      },
    },
    {
      target: controlRef,
      drag: {
        from: () => [
          mat.getTranslateX(matrix.get()),
          mat.getTranslateY(matrix.get()),
        ],
        pointer: { touch: true },
      },
      pinch: {
        from: () => [mat.getScaleX(matrix.get()), 0],
        pointer: { touch: true },
      },
    }
  );

  useEffect(() => {
    if (loading) {
      Toast.show({
        icon: "loading",
        duration: 0,
      });
    }

    return () => {
      Toast.clear();
    };
  }, [loading]);

  useEffect(() => {
    if (url && videoRef.current) {
      if (playerRef.current) {
        playerRef.current.reset();
        playerRef.current.src(url);
      } else {
        playerRef.current = videojs(videoRef.current, {
          preload: "auto",
          controls: true,
          autoplay: true,
          loop: true,
          enableSmoothSeeking: true,
          language: "zh-CN",
        });
      }

      // 设置自定义播放速度选项
      playerRef.current?.playbackRates([0.5, 0.75, 1, 1.5, 2]);

      // 添加双击事件处理函数
      const handleDoubleClick = (event: MouseEvent | TouchEvent) => {
        // 检查双击是否发生在控制栏之外
        const controlBar = playerRef.current
          ?.el()
          .querySelector(".vjs-control-bar");
        if (controlBar && !controlBar.contains(event.target as Node)) {
          if (playerRef.current?.paused()) {
            playerRef.current?.play();
          } else {
            playerRef.current?.pause();
          }
        }
      };

      let lastTap = 0;
      const handleTap = (event: TouchEvent) => {
        const currentTime = new Date().getTime();
        const tapLength = currentTime - lastTap;
        if (tapLength < 500 && tapLength > 0) {
          handleDoubleClick(event);
          event.preventDefault();
        }
        lastTap = currentTime;
      };

      // 获取视频元素
      const videoElement = playerRef.current
        ?.el()
        .querySelector(".vjs-tech") as HTMLVideoElement | null;

      if (videoElement) {
        videoElement.addEventListener("touchend", handleTap);
      }

      // 清理函数
      return () => {
        if (videoElement) {
          videoElement.removeEventListener("touchend", handleTap);
        }
      };
    }
  }, [url]);

  useEffect(
    () => () => {
      playerRef.current?.dispose();
    },
    []
  );

  const renderContent = useMemo(() => {
    if (type === "video") {
      // 此时还没有视频url
      if (loading || !url) {
        return (
          <div className="text-[#9d64ff] w-full h-full flex justify-center items-center gap-1">
            <span>加载中</span>
            <DotLoading color="currentColor" />
          </div>
        );
      }

      return (
        <div className="w-full h-full">
          <video
            ref={videoRef}
            className="slide-video-preview video-js vjs-default-skin vjs-big-play-centered w-full h-full"
          >
            <source
              src={url}
              type={
                url.endsWith(".mp4") ? "video/mp4" : "application/x-mpegURL"
              }
            />
          </video>
        </div>
      );
    } else {
      if (preview) {
        // 先使用缩略图占位
        return (
          <>
            <img
              ref={imgRef}
              src={url ?? thumbnailImage}
              draggable={false}
              alt={url}
              style={{ filter: `blur(${blur}px)` }}
            />
            <div className="absolute top-0 left-0 w-full h-full text-white text-xl flex items-center justify-center bg-white/15">
              付费内容，购买后可看
            </div>
          </>
        );
      } else {
        return (
          <img
            ref={imgRef}
            src={url ?? thumbnailImage}
            draggable={false}
            alt={url}
          />
        );
      }
    }
  }, [blur, loading, preview, thumbnailImage, type, url]);

  return (
    <div className={`${classPrefix}-slide`}>
      <div className={`${classPrefix}-control`} ref={controlRef}>
        <animated.div
          className={`${classPrefix}-image-wrapper relative`}
          style={{
            matrix,
          }}
        >
          {renderContent}
        </animated.div>
      </div>
      {type === "video" && (
        <div
          className="absolute top-3 left-3 z-10 w-6 h-6 flex items-center justify-center"
          onClick={() => {
            props.onTap?.();
          }}
        >
          <ArrowIcon className="rotate-180" />
        </div>
      )}
    </div>
  );
};
