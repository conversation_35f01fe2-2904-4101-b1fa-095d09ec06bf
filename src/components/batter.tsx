import React, { useState, useCallback, useRef } from "react";

interface BatterProps {
  // 正常模式下的子组件
  children: React.ReactNode;
  // 连击中构建组件
  builder: (
    currentWait: number,
    maxWait: number,
    number: number
  ) => React.ReactNode;
  // 是否启用
  enabled?: boolean;
  // 完成事件
  onFinish: (number: number) => void;
  // 最大等待时间，单位为毫秒
  maxWait?: number;
  // 最大连击
  maxBatter?: number;
}

const Batter: React.FC<BatterProps> = ({
  children,
  builder,
  enabled = true,
  onFinish,
  maxWait = 1000,
  maxBatter,
}) => {
  // 是否连击中
  const [running, setRunning] = useState(false);
  // 连击次数
  const [number, setNumber] = useState(0);
  // 当前计时器
  const [current, setCurrent] = useState(0);

  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const longTimerRef = useRef<number | null>(null);

  // 清理定时器
  const clearTimers = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    if (longTimerRef.current) {
      clearInterval(longTimerRef.current);
      longTimerRef.current = null;
    }
  }, []);

  // 追加连击
  const append = useCallback(() => {
    if (maxBatter && number >= maxBatter) return;
    setNumber((prev) => prev + 1);
    setCurrent(0);
  }, [maxBatter, number]);

  // 停止连击
  const stop = useCallback(() => {
    clearTimers();
    setRunning(false);
    setCurrent(0);
    setNumber((currentNumber) => {
      onFinish(currentNumber);
      return 1;
    });
  }, [clearTimers, onFinish]);

  // 开始连击
  const start = useCallback(() => {
    setRunning(true);
    setNumber(1);
    setCurrent(0);

    timerRef.current = setInterval(() => {
      setCurrent((prev) => {
        if (prev + 50 > maxWait) {
          stop();
          return prev;
        }
        return prev + 50;
      });
    }, 50);
  }, [maxWait, stop]);

  // 点击事件处理
  const handleTap = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      e.stopPropagation();

      if (!enabled) return;
      if (!running) {
        start();
      } else {
        append();
      }
    },
    [enabled, running, start, append]
  );

  // 长按开始事件
  const handleLongPressStart = useCallback(() => {
    longTimerRef.current = setInterval(handleTap, 300);
  }, [handleTap]);

  // 长按结束事件
  const handleLongPressEnd = useCallback(() => {
    if (longTimerRef.current) {
      clearInterval(longTimerRef.current);
      longTimerRef.current = null;
    }
  }, []);

  if (!enabled) {
    return <>{children}</>;
  }

  return (
    <div
      onClick={handleTap}
      onMouseDown={handleLongPressStart}
      onMouseUp={handleLongPressEnd}
      onMouseLeave={handleLongPressEnd}
      onTouchStart={handleLongPressStart}
      onTouchEnd={handleLongPressEnd}
      style={{ touchAction: "manipulation" }}
    >
      {running ? builder(current, maxWait, number) : children}
    </div>
  );
};

export default Batter;
