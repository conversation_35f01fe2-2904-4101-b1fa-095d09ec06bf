import { PropsWithChildren } from "react";
import classNames from "classnames";

interface Props {
  className?: string;
  color?: string;
  size?: number;
}

const BaseIcon = (props: PropsWithChildren<Props>) => {
  const { children, className = "", color, size } = props;

  return (
    <span
      className={classNames("flex items-center", className)}
      style={{
        color: color ?? undefined,
        fontSize: size ? `${size / 16}rem` : undefined,
      }}
    >
      {children}
    </span>
  );
};

const svgList = {
  arrow: (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      fill="currentColor"
    >
      <path d="M724.48 521.728c-1.8432 7.7824-5.7344 14.848-11.3664 20.48l-341.9136 342.016c-16.6912 16.6912-43.7248 16.6912-60.3136 0s-16.6912-43.7248 0-60.3136L622.6944 512 310.8864 200.0896c-16.6912-16.6912-16.6912-43.7248 0-60.3136 16.6912-16.6912 43.7248-16.6912 60.3136 0l341.9136 341.9136c10.8544 10.8544 14.6432 26.112 11.3664 40.0384z"></path>
    </svg>
  ),
  more: (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      fill="currentColor"
    >
      <path d="M288 456.864A63.264 63.264 0 0 0 256 448a64 64 0 1 0 0 128c11.712 0 22.56-3.392 32-8.896 19.04-11.072 32-31.488 32-55.104 0-23.648-12.96-44.064-32-55.136M544 456.864A63.264 63.264 0 0 0 512 448c-11.712 0-22.56 3.36-32 8.864-19.04 11.072-32 31.488-32 55.136 0 23.616 12.96 44.032 32 55.104 9.44 5.504 20.288 8.896 32 8.896s22.56-3.392 32-8.896c19.04-11.072 32-31.488 32-55.104 0-23.648-12.96-44.064-32-55.136M768 448c-11.712 0-22.56 3.392-32 8.864-19.04 11.104-32 31.52-32 55.136 0 23.616 12.96 44.032 32 55.136 9.44 5.472 20.288 8.864 32 8.864a64 64 0 1 0 0-128"></path>
    </svg>
  ),
  plus: (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      fill="currentColor"
    >
      <path d="M474 152m8 0l60 0q8 0 8 8l0 704q0 8-8 8l-60 0q-8 0-8-8l0-704q0-8 8-8Z"></path>
      <path d="M168 474m8 0l672 0q8 0 8 8l0 60q0 8-8 8l-672 0q-8 0-8-8l0-60q0-8 8-8Z"></path>
    </svg>
  ),
  play: (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      fill="currentColor"
    >
      <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m144.1 454.9L437.7 677.8c-1.4 1-3 1.5-4.7 1.5-4.4 0-8-3.6-8-8V353.7c0-1.7 0.5-3.3 1.5-4.7 2.6-3.6 7.6-4.4 11.2-1.8L656.1 506c0.7 0.5 1.3 1.1 1.8 1.8 2.6 3.5 1.8 8.5-1.8 11.1z"></path>
    </svg>
  ),
  voice1: (
    <svg
      viewBox="0 0 4.943 5.174"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1.43em"
      fill="currentColor"
    >
      <path
        d="M189.47,71.96a2.59,2.59,0,0,0,0,5.174,2.463,2.463,0,0,0,1.757-.766,2.678,2.678,0,0,0,0-3.642,2.463,2.463,0,0,0-1.757-.766Zm0,0"
        transform="translate(-186.998 -71.96)"
      />
    </svg>
  ),
  voice2: (
    <svg
      viewBox="0 0 13.17 19.03"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1.43em"
      fill="currentColor"
    >
      <path
        d="M197.348,65.154a1.678,1.678,0,0,0-1.222-.71,1.655,1.655,0,0,0-1.32.494l-.034.038a1.852,1.852,0,0,0-.193,2.333,12.132,12.132,0,0,1,.295,13.4,1.835,1.835,0,0,0,.315,2.333,1.644,1.644,0,0,0,1.331.4,1.661,1.661,0,0,0,1.175-.762,15.739,15.739,0,0,0,2.475-8.506,15.557,15.557,0,0,0-2.82-9.028ZM189.47,71.96a2.59,2.59,0,0,0,0,5.174,2.463,2.463,0,0,0,1.757-.766,2.678,2.678,0,0,0,0-3.642,2.463,2.463,0,0,0-1.757-.766Zm0,0"
        transform="translate(-186.998 -64.438)"
      />
    </svg>
  ),
  voice3: (
    <svg
      viewBox="0 0 22 31.602"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1.43em"
      fill="currentColor"
    >
      <path
        d="M203.012,58.706a1.685,1.685,0,0,0-2.566.094l-.112.146a1.9,1.9,0,0,0,.095,2.475,18.6,18.6,0,0,1,.132,25.221,1.911,1.911,0,0,0-.095,2.462,1.69,1.69,0,0,0,2.58.111,22.478,22.478,0,0,0-.034-30.51Zm-5.664,6.448a1.678,1.678,0,0,0-1.222-.71,1.655,1.655,0,0,0-1.32.494l-.034.038a1.852,1.852,0,0,0-.193,2.333,12.132,12.132,0,0,1,.295,13.4,1.835,1.835,0,0,0,.315,2.333,1.644,1.644,0,0,0,1.331.4,1.661,1.661,0,0,0,1.175-.762,15.739,15.739,0,0,0,2.475-8.506,15.557,15.557,0,0,0-2.82-9.028ZM189.47,71.96a2.59,2.59,0,0,0,0,5.174,2.463,2.463,0,0,0,1.757-.766,2.678,2.678,0,0,0,0-3.642,2.463,2.463,0,0,0-1.757-.766Zm0,0"
        transform="translate(-186.998 -58.158)"
      />
    </svg>
  ),
  user: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M772.016477 696.022177c-39.228443-39.229466-85.763292-68.49807-136.530536-86.546122 26.774807-13.283538 51.500954-30.976502 73.254398-52.729945 52.55189-52.550867 81.494059-122.422214 81.494059-196.74085s-28.941146-144.189983-81.494059-196.741873c-52.550867-52.550867-122.422214-81.493036-196.74085-81.493036s-144.189983 28.942169-196.741873 81.493036c-52.55189 52.550867-81.494059 122.422214-81.494059 196.741873s28.941146 144.189983 81.494059 196.74085c21.753443 21.753443 46.480614 39.446407 73.256444 52.729945-50.76929 18.049075-97.303116 47.316655-136.532583 86.546122-66.188468 66.187445-104.009865 153.166425-107.422591 246.208495l48.730864 0c3.387144-80.028685 36.140105-154.783249 93.129051-211.770148 55.771211-55.771211 128.557958-88.326675 206.650547-92.867084 6.27389 0.418532 12.582573 0.645706 18.929118 0.645706 6.345522 0 12.656251-0.227174 18.929118-0.645706 78.091566 4.54041 150.880359 37.095873 206.650547 92.867084 56.987922 56.986899 89.741907 131.741463 93.129051 211.770148l48.730864 0C876.027365 849.188602 838.204945 762.209622 772.016477 696.022177zM282.466792 360.004237c0-126.564557 102.96814-229.53372 229.53372-229.53372 126.564557 0 229.53372 102.969163 229.53372 229.53372 0 120.304993-93.040023 219.280192-210.942293 228.77545-6.170536-0.304945-12.369725-0.460488-18.591427-0.460488-6.222725 0-12.420891 0.155543-18.59245 0.460488C375.505791 579.284429 282.466792 480.30923 282.466792 360.004237z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  personAddAlt: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M554.666667 341.333333c0-94.293333-76.373333-170.666667-170.666667-170.666666S213.333333 247.04 213.333333 341.333333s76.373333 170.666667 170.666667 170.666667 170.666667-76.373333 170.666667-170.666667z m-85.333334 0c0 46.933333-38.4 85.333333-85.333333 85.333334s-85.333333-38.4-85.333333-85.333334 38.4-85.333333 85.333333-85.333333 85.333333 38.4 85.333333 85.333333zM42.666667 768v85.333333h682.666666v-85.333333c0-113.493333-227.413333-170.666667-341.333333-170.666667s-341.333333 57.173333-341.333333 170.666667z m85.333333 0c8.533333-30.293333 140.8-85.333333 256-85.333333 114.773333 0 246.613333 54.613333 256 85.333333H128z m725.333333-128v-128h128v-85.333333h-128V298.666667h-85.333333v128h-128v85.333333h128v128h85.333333z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  personRemove: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M896 256h-170.666667a42.666667 42.666667 0 0 0 0 85.333333h170.666667a42.666667 42.666667 0 0 0 0-85.333333zM426.666667 469.333333a170.666667 170.666667 0 1 0-170.666667-170.666666 170.666667 170.666667 0 0 0 170.666667 170.666666zM682.666667 896a42.666667 42.666667 0 0 0 42.666666-42.666667 298.666667 298.666667 0 0 0-597.333333 0 42.666667 42.666667 0 0 0 42.666667 42.666667"
        fill="currentColor"
      ></path>
    </svg>
  ),
  sms: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M853.333333 85.333333H170.666667c-46.933333 0-85.333333 38.4-85.333334 85.333334v768l170.666667-170.666667h597.333333c46.933333 0 85.333333-38.4 85.333334-85.333333V170.666667c0-46.933333-38.4-85.333333-85.333334-85.333334z m0 597.333334H220.586667L170.666667 732.586667V170.666667h682.666666v512z m-384-170.666667h85.333334v85.333333h-85.333334v-85.333333z m0-256h85.333334v170.666667h-85.333334V256z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  arrowFill: (
    <svg viewBox="0 0 1024 1024" width="1em" height="1em">
      <path d="M384 768 640 512 384 256Z" fill="currentColor"></path>
    </svg>
  ),
  close: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M693.034667 738.261333L285.738667 330.965333a10.666667 10.666667 0 0 1 0-15.061333l30.165333-30.165333a10.666667 10.666667 0 0 1 15.061333 0l407.296 407.296a10.666667 10.666667 0 0 1 0 15.061333l-30.165333 30.165333a10.666667 10.666667 0 0 1-15.061333 0z"
        fill="currentColor"
      ></path>
      <path
        d="M315.904 738.261333l-30.165333-30.165333a10.666667 10.666667 0 0 1 0-15.061333l407.296-407.296a10.666667 10.666667 0 0 1 15.061333 0l30.165333 30.165333a10.666667 10.666667 0 0 1 0 15.061333l-407.296 407.296a10.666667 10.666667 0 0 1-15.061333 0z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  report: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M896 405.76a128 128 0 0 0-37.546667-90.453333l-149.76-149.76A128 128 0 0 0 618.24 128h-213.333333a128 128 0 0 0-90.453334 37.546667L165.546667 315.306667A128 128 0 0 0 128 405.76v213.333333a128 128 0 0 0 37.546667 90.453334l149.76 149.76a128 128 0 0 0 90.453333 36.693333h213.333333a128 128 0 0 0 90.453334-37.546667l149.76-149.76a128 128 0 0 0 36.693333-90.453333z m-341.333333 256a21.333333 21.333333 0 0 1-21.333334 21.333333h-42.666666a21.333333 21.333333 0 0 1-21.333334-21.333333v-42.666667a21.333333 21.333333 0 0 1 21.333334-21.333333h42.666666a21.333333 21.333333 0 0 1 21.333334 21.333333z m-9.386667-158.72a10.666667 10.666667 0 0 1-10.666667 9.386667h-45.226666a10.666667 10.666667 0 0 1-10.666667-9.386667l-17.066667-137.386667a21.333333 21.333333 0 0 1 21.333334-23.893333h58.026666a21.333333 21.333333 0 0 1 21.333334 23.893333z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  delete: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M602.453333 446.72L512 537.173333l-90.88-90.453333-60.16 60.16L451.84 597.333333l-90.453333 90.453334 60.16 60.16L512 657.493333l90.453333 90.453334 60.16-60.16L572.16 597.333333l90.453333-90.453333-60.16-60.16zM661.333333 170.666667l-42.666666-42.666667h-213.333334l-42.666666 42.666667H213.333333v85.333333h597.333334V170.666667h-149.333334zM256 810.666667c0 46.933333 38.4 85.333333 85.333333 85.333333h341.333334c46.933333 0 85.333333-38.4 85.333333-85.333333V298.666667H256v512zM341.333333 384h341.333334v426.666667H341.333333V384z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  send: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M977.4 461l-0.4-0.2L76.2 87.2c-3.8-1.6-7.8-2.6-11.8-3.2-4-0.6-8.2-0.8-12.2-0.4s-8 1.2-12 2.6-7.6 3.2-11 5.6c-3.6 2.4-6.8 5.2-9.8 8.4C16.4 103.4 14 106.8 12 110.6c-2 3.8-3.6 7.8-4.6 12-1 4.2-1.6 8.4-1.6 12.8v239c0 3 0.2 5.8 0.8 8.8 0.6 2.8 1.2 5.6 2.2 8.4 1 2.8 2.2 5.4 3.6 8 1.4 2.6 3.2 5 5 7.2 1.8 2.2 4 4.4 6.2 6.2 2.2 1.8 4.6 3.6 7.2 5 2.6 1.4 5.2 2.8 7.8 3.8 2.8 1 5.6 1.8 8.4 2.4l491.4 90.8c1 0.2 1.8 0.6 2.8 1 0.8 0.4 1.6 1.2 2.2 1.8 0.6 0.8 1.2 1.6 1.4 2.6 0.4 1 0.4 1.8 0.4 2.8s-0.2 2-0.4 2.8c-0.4 1-0.8 1.8-1.4 2.6-0.6 0.8-1.4 1.4-2.2 1.8-0.8 0.4-1.8 0.8-2.8 1L47 622.2c-2.8 0.6-5.6 1.4-8.4 2.4-2.8 1-5.4 2.2-7.8 3.8s-4.8 3.2-7.2 5c-2.2 1.8-4.2 4-6.2 6.2-1.8 2.2-3.6 4.6-5 7.2-1.4 2.6-2.6 5.2-3.6 8s-1.8 5.6-2.2 8.4c-0.6 2.8-0.8 5.8-0.8 8.8v239c0 4.2 0.4 8.2 1.6 12.2 1 4 2.4 7.8 4.4 11.4 2 3.6 4.4 7 7 10 2.8 3 5.8 5.6 9.4 8 4.2 2.8 8.6 4.8 13.4 6.2 4.8 1.4 9.6 2.2 14.6 2.2 7 0 13.6-1.4 20-4l900.8-371.6 0.4-0.2c3-1.2 6-2.8 8.8-4.6s5.4-3.6 8-5.8c2.6-2.2 4.8-4.4 7-6.8 2.2-2.4 4.2-5 6-7.8 1.8-2.8 3.4-5.6 4.8-8.6s2.6-6 3.4-9.2c1-3.2 1.6-6.4 2.2-9.6 0.4-3.2 0.8-6.6 0.8-9.8 0-3.2-0.2-6.6-0.8-9.8-0.4-3.2-1.2-6.4-2.2-9.6s-2.2-6.2-3.4-9.2-3-5.8-4.8-8.6c-1.8-2.8-3.8-5.4-6-7.8-2.2-2.4-4.6-4.8-7-6.8-2.6-2.2-5.2-4-8-5.8s-5.8-3.2-8.8-4.4z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  stopRounded: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M341.333333 256h341.333334c46.933333 0 85.333333 38.4 85.333333 85.333333v341.333334c0 46.933333-38.4 85.333333-85.333333 85.333333H341.333333c-46.933333 0-85.333333-38.4-85.333333-85.333333V341.333333c0-46.933333 38.4-85.333333 85.333333-85.333333z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  playRounded: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M238.933333 64l605.866667 392.533333c38.4 25.6 51.2 76.8 25.6 119.466667-8.533333 12.8-17.066667 21.333333-25.6 25.6L238.933333 964.266667c-38.4 25.6-93.866667 12.8-115.2-29.866667-8.533333-12.8-12.8-29.866667-12.8-42.666667V132.266667c0-46.933333 38.4-85.333333 85.333334-85.333334 12.8 4.266667 29.866667 8.533333 42.666666 17.066667z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  monetizationOn: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M512 85.333333C276.48 85.333333 85.333333 276.48 85.333333 512s191.146667 426.666667 426.666667 426.666667 426.666667-191.146667 426.666667-426.666667S747.52 85.333333 512 85.333333z m60.16 686.506667V853.333333h-113.92v-82.346666c-72.96-15.36-134.826667-62.293333-139.52-145.066667h83.626667c4.266667 44.8 34.986667 79.786667 113.066666 79.786667 83.626667 0 102.4-41.813333 102.4-67.84 0-35.413333-18.773333-68.693333-113.92-91.306667-105.813333-25.6-178.346667-69.12-178.346666-156.586667 0-73.386667 59.306667-121.173333 132.693333-136.96V170.666667h113.92v83.2c79.36 19.2 119.04 79.36 121.6 144.64H610.133333c-2.133333-47.36-27.306667-79.786667-94.72-79.786667-64 0-102.4 29.013333-102.4 69.973333 0 35.84 27.733333 59.306667 113.92 81.493334s178.346667 59.306667 178.346667 166.826666c-0.426667 78.08-58.88 120.746667-133.12 134.826667z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  error: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M512 85.333333C276.48 85.333333 85.333333 276.266667 85.333333 512s191.146667 426.666667 426.666667 426.666667 426.666667-190.933333 426.666667-426.666667S747.52 85.333333 512 85.333333z m42.666667 640h-85.333334v-85.333333h85.333334v85.333333z m0-170.666666h-85.333334V298.666667h85.333334v256z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  visibility: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M512 192C298.666667 192 115.2 324.266667 42.666667 512c72.533333 187.733333 256 320 469.333333 320s396.8-132.266667 469.333333-320c-72.533333-187.733333-256-320-469.333333-320zM512 725.333333c-119.466667 0-213.333333-93.866667-213.333333-213.333333s93.866667-213.333333 213.333333-213.333333 213.333333 93.866667 213.333333 213.333333-93.866667 213.333333-213.333333 213.333333z m0-341.333333c-72.533333 0-128 55.466667-128 128s55.466667 128 128 128 128-55.466667 128-128-55.466667-128-128-128z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  visibilityOff: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M512 298.666667c119.466667 0 213.333333 93.866667 213.333333 213.333333 0 25.6-4.266667 55.466667-17.066666 76.8l123.733333 123.733333c64-55.466667 115.2-123.733333 145.066667-200.533333-72.533333-187.733333-256-320-469.333334-320-59.733333 0-115.2 12.8-170.666666 29.866667l93.866666 93.866666c25.6-12.8 55.466667-17.066667 81.066667-17.066666zM85.333333 183.466667l98.133334 98.133333 17.066666 17.066667C128 354.133333 72.533333 426.666667 42.666667 512c72.533333 187.733333 256 320 469.333333 320 64 0 128-12.8 187.733333-34.133333l17.066667 17.066666 123.733333 123.733334 55.466667-55.466667L140.8 128 85.333333 183.466667z m234.666667 234.666666L384 482.133333v29.866667c0 72.533333 55.466667 128 128 128 8.533333 0 17.066667 0 29.866667-4.266667l64 64c-29.866667 17.066667-59.733333 25.6-93.866667 25.6-119.466667 0-213.333333-93.866667-213.333333-213.333333 0-34.133333 8.533333-64 21.333333-93.866667z m183.466667-34.133333l132.266666 132.266667v-8.533334c0-72.533333-55.466667-128-128-128 0 4.266667 0 4.266667-4.266666 4.266667z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  keyboard: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M160 512a352 352 0 1 0 704 0 352 352 0 0 0-704 0zM512 96c229.76 0 416 186.24 416 416s-186.24 416-416 416S96 741.76 96 512 282.24 96 512 96z m-85.077333 572.458667h171.861333a21.333333 21.333333 0 0 1 21.333333 21.333333v16a21.333333 21.333333 0 0 1-21.333333 21.333333h-171.861333a21.333333 21.333333 0 0 1-21.333334-21.333333v-16a21.333333 21.333333 0 0 1 21.333334-21.333333zM324.437333 355.541333h-35.541333a21.333333 21.333333 0 0 0-21.333333 21.333334v35.584a21.333333 21.333333 0 0 0 21.333333 21.333333h35.541333a21.333333 21.333333 0 0 0 21.333334-21.333333v-35.584a21.333333 21.333333 0 0 0-21.333334-21.333334z m-33.834666 136.917334h35.541333a21.333333 21.333333 0 0 1 21.333333 21.333333v35.541333a21.333333 21.333333 0 0 1-21.333333 21.333334h-35.541333a21.333333 21.333333 0 0 1-21.333334-21.333334v-35.541333a21.333333 21.333333 0 0 1 21.333334-21.333333z m170.709333-136.917334H425.813333a21.333333 21.333333 0 0 0-21.333333 21.333334v35.584a21.333333 21.333333 0 0 0 21.333333 21.333333h35.541334a21.333333 21.333333 0 0 0 21.333333-21.333333v-35.584a21.333333 21.333333 0 0 0-21.333333-21.333334z m-34.389333 136.917334h35.541333a21.333333 21.333333 0 0 1 21.333333 21.333333v35.541333a21.333333 21.333333 0 0 1-21.333333 21.333334h-35.541333a21.333333 21.333333 0 0 1-21.333334-21.333334v-35.541333a21.333333 21.333333 0 0 1 21.333334-21.333333z m171.306666-136.917334h-35.584a21.333333 21.333333 0 0 0-21.333333 21.333334v35.584a21.333333 21.333333 0 0 0 21.333333 21.333333h35.584a21.333333 21.333333 0 0 0 21.333334-21.333333v-35.584a21.333333 21.333333 0 0 0-21.333334-21.333334z m-34.986666 136.917334h35.541333a21.333333 21.333333 0 0 1 21.333333 21.333333v35.541333a21.333333 21.333333 0 0 1-21.333333 21.333334h-35.541333a21.333333 21.333333 0 0 1-21.333334-21.333334v-35.541333a21.333333 21.333333 0 0 1 21.333334-21.333333z m171.861333-136.917334h-35.541333a21.333333 21.333333 0 0 0-21.333334 21.333334v35.584a21.333333 21.333333 0 0 0 21.333334 21.333333h35.541333a21.333333 21.333333 0 0 0 21.333333-21.333333v-35.584a21.333333 21.333333 0 0 0-21.333333-21.333334z m-35.541333 136.917334h35.541333a21.333333 21.333333 0 0 1 21.333333 21.333333v35.541333a21.333333 21.333333 0 0 1-21.333333 21.333334h-35.541333a21.333333 21.333333 0 0 1-21.333334-21.333334v-35.541333a21.333333 21.333333 0 0 1 21.333334-21.333333z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  mood: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M511.573333 85.333333C276.053333 85.333333 85.333333 276.48 85.333333 512s190.72 426.666667 426.24 426.666667C747.52 938.666667 938.666667 747.52 938.666667 512S747.52 85.333333 511.573333 85.333333zM512 853.333333c-188.586667 0-341.333333-152.746667-341.333333-341.333333s152.746667-341.333333 341.333333-341.333333 341.333333 152.746667 341.333333 341.333333-152.746667 341.333333-341.333333 341.333333z m149.333333-384c35.413333 0 64-28.586667 64-64S696.746667 341.333333 661.333333 341.333333 597.333333 369.92 597.333333 405.333333s28.586667 64 64 64z m-298.666666 0c35.413333 0 64-28.586667 64-64S398.08 341.333333 362.666667 341.333333 298.666667 369.92 298.666667 405.333333 327.253333 469.333333 362.666667 469.333333z m149.333333 277.333334c99.413333 0 183.893333-62.293333 218.026667-149.333334H293.973333c34.133333 87.04 118.613333 149.333333 218.026667 149.333334z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  addOutline: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M512 70.4A441.6 441.6 0 1 0 953.6 512 441.92 441.92 0 0 0 512 70.4z m0 800A358.4 358.4 0 1 1 870.4 512 358.72 358.72 0 0 1 512 870.4z"
        fill="currentColor"
      ></path>
      <path
        d="M672 470.4h-118.4V352a41.6 41.6 0 0 0-83.2 0v118.4H352a41.6 41.6 0 0 0 0 83.2h118.4V672a41.6 41.6 0 0 0 83.2 0v-118.4H672a41.6 41.6 0 0 0 0-83.2z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  mic: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M512 949.138286c238.72 0 437.138286-197.997714 437.138286-437.138286 0-238.72-198.857143-437.138286-437.577143-437.138286C272.438857 74.861714 74.88 273.28 74.88 512c0 239.140571 197.997714 437.138286 437.138286 437.138286z m0-72.850286C309.705143 876.288 148.114286 714.276571 148.114286 512c0-201.874286 161.152-364.288 363.446857-364.288 201.856 0 364.269714 162.432 364.708571 364.288 0.420571 202.294857-162.432 364.288-364.288 364.288z m0-303.853714c42.002286 0 71.131429-32.585143 71.131429-75.867429v-168.411428c0-43.300571-29.129143-75.885714-71.131429-75.885715s-71.131429 32.585143-71.131429 75.885715v168.411428c0 43.282286 29.129143 75.867429 71.131429 75.867429z m-108.854857 170.569143h218.148571c9.856 0 18.413714-8.996571 18.413715-18.852572 0-10.294857-8.557714-18.870857-18.432-18.870857h-90.422858v-50.998857c85.284571-8.557714 142.281143-71.990857 142.281143-158.134857v-54.857143a18.194286 18.194286 0 0 0-17.993143-18.432c-9.856 0-18.852571 8.137143-18.852571 18.432v54.857143c0 71.990857-50.578286 123.849143-124.288 123.849143-74.148571 0-124.288-51.858286-124.288-123.849143v-54.857143c0-10.294857-8.996571-18.432-18.852571-18.432-9.435429 0-18.011429 8.137143-18.011429 18.432v54.857143c0 86.125714 57.014857 149.997714 142.299429 158.134857v51.017143h-90.002286c-10.294857 0-19.291429 8.557714-19.291429 18.834285 0 9.874286 8.996571 18.870857 19.291429 18.870858z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  favorite: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M704 128c-74.24 0-145.493333 34.56-192 88.96C465.493333 162.56 394.24 128 320 128 188.373333 128 85.333333 231.04 85.333333 362.666667c0 161.066667 145.066667 292.693333 364.8 492.16L512 910.933333l61.866667-56.106666C793.6 655.36 938.666667 523.733333 938.666667 362.666667c0-131.626667-103.04-234.666667-234.666667-234.666667z m-187.52 663.68l-4.48 4.053333-4.48-4.053333C304.64 607.573333 170.666667 485.973333 170.666667 362.666667c0-85.12 64.213333-149.333333 149.333333-149.333334 65.706667 0 129.706667 42.453333 152.106667 100.693334h79.573333C574.293333 255.786667 638.293333 213.333333 704 213.333333c85.12 0 149.333333 64.213333 149.333333 149.333334 0 123.306667-133.973333 244.906667-336.853333 429.013333z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  edit: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M827.733333 313.173333L710.826667 196.266667A85.333333 85.333333 0 0 0 597.333333 193.28l-384 384a85.333333 85.333333 0 0 0-24.32 51.626667L170.666667 806.826667a42.666667 42.666667 0 0 0 12.373333 34.133333A42.666667 42.666667 0 0 0 213.333333 853.333333h3.84l177.92-16.213333a85.333333 85.333333 0 0 0 51.626667-24.32l384-384a81.92 81.92 0 0 0-2.986667-115.626667zM682.666667 455.68L568.32 341.333333l83.2-85.333333L768 372.48z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  radioOn: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M512 85.333333a426.666667 426.666667 0 1 0 426.666667 426.666667A426.666667 426.666667 0 0 0 512 85.333333z m0 768a341.333333 341.333333 0 1 1 341.333333-341.333333 341.333333 341.333333 0 0 1-341.333333 341.333333z"
        fill="currentColor"
      ></path>
      <path
        d="M512 298.666667a213.333333 213.333333 0 1 0 213.333333 213.333333 213.333333 213.333333 0 0 0-213.333333-213.333333z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  radioOff: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M512 938.666667a426.666667 426.666667 0 1 1 426.666667-426.666667 426.666667 426.666667 0 0 1-426.666667 426.666667z m0-768a341.333333 341.333333 0 1 0 341.333333 341.333333 341.333333 341.333333 0 0 0-341.333333-341.333333z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  share: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M768.704 703.616c-35.648 0-67.904 14.72-91.136 38.304l-309.152-171.712c9.056-17.568 14.688-37.184 14.688-58.272 0-12.576-2.368-24.48-5.76-35.936l304.608-189.152c22.688 20.416 52.384 33.184 85.216 33.184 70.592 0 128-57.408 128-128s-57.408-128-128-128-128 57.408-128 128c0 14.56 2.976 28.352 7.456 41.408l-301.824 187.392c-23.136-22.784-54.784-36.928-89.728-36.928-70.592 0-128 57.408-128 128 0 70.592 57.408 128 128 128 25.664 0 49.504-7.744 69.568-20.8l321.216 178.4c-3.04 10.944-5.184 22.208-5.184 34.08 0 70.592 57.408 128 128 128s128-57.408 128-128S839.328 703.616 768.704 703.616zM767.2 128.032c35.296 0 64 28.704 64 64s-28.704 64-64 64-64-28.704-64-64S731.904 128.032 767.2 128.032zM191.136 511.936c0-35.296 28.704-64 64-64s64 28.704 64 64c0 35.296-28.704 64-64 64S191.136 547.232 191.136 511.936zM768.704 895.616c-35.296 0-64-28.704-64-64s28.704-64 64-64 64 28.704 64 64S804 895.616 768.704 895.616z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  setting: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M904.533333 422.4l-85.333333-14.933333-17.066667-38.4 49.066667-70.4c14.933333-21.333333 12.8-49.066667-6.4-68.266667l-53.333333-53.333333c-19.2-19.2-46.933333-21.333333-68.266667-6.4l-70.4 49.066666-38.4-17.066666-14.933333-85.333334c-2.133333-23.466667-23.466667-42.666667-49.066667-42.666666h-74.666667c-25.6 0-46.933333 19.2-53.333333 44.8l-14.933333 85.333333-38.4 17.066667L296.533333 170.666667c-21.333333-14.933333-49.066667-12.8-68.266666 6.4l-53.333334 53.333333c-19.2 19.2-21.333333 46.933333-6.4 68.266667l49.066667 70.4-17.066667 38.4-85.333333 14.933333c-21.333333 4.266667-40.533333 25.6-40.533333 51.2v74.666667c0 25.6 19.2 46.933333 44.8 53.333333l85.333333 14.933333 17.066667 38.4L170.666667 727.466667c-14.933333 21.333333-12.8 49.066667 6.4 68.266666l53.333333 53.333334c19.2 19.2 46.933333 21.333333 68.266667 6.4l70.4-49.066667 38.4 17.066667 14.933333 85.333333c4.266667 25.6 25.6 44.8 53.333333 44.8h74.666667c25.6 0 46.933333-19.2 53.333333-44.8l14.933334-85.333333 38.4-17.066667 70.4 49.066667c21.333333 14.933333 49.066667 12.8 68.266666-6.4l53.333334-53.333334c19.2-19.2 21.333333-46.933333 6.4-68.266666l-49.066667-70.4 17.066667-38.4 85.333333-14.933334c25.6-4.266667 44.8-25.6 44.8-53.333333v-74.666667c-4.266667-27.733333-23.466667-49.066667-49.066667-53.333333z m-19.2 117.333333l-93.866666 17.066667c-10.666667 2.133333-19.2 8.533333-23.466667 19.2l-29.866667 70.4c-4.266667 10.666667-2.133333 21.333333 4.266667 29.866667l53.333333 76.8-40.533333 40.533333-76.8-53.333333c-8.533333-6.4-21.333333-8.533333-29.866667-4.266667L576 768c-10.666667 4.266667-17.066667 12.8-19.2 23.466667l-17.066667 93.866666h-57.6l-17.066666-93.866666c-2.133333-10.666667-8.533333-19.2-19.2-23.466667l-70.4-29.866667c-10.666667-4.266667-21.333333-2.133333-29.866667 4.266667l-76.8 53.333333-40.533333-40.533333 53.333333-76.8c6.4-8.533333 8.533333-21.333333 4.266667-29.866667L256 576c-4.266667-10.666667-12.8-17.066667-23.466667-19.2l-93.866666-17.066667v-57.6l93.866666-17.066666c10.666667-2.133333 19.2-8.533333 23.466667-19.2l29.866667-70.4c4.266667-10.666667 2.133333-21.333333-4.266667-29.866667l-53.333333-76.8 40.533333-40.533333 76.8 53.333333c8.533333 6.4 21.333333 8.533333 29.866667 4.266667L448 256c10.666667-4.266667 17.066667-12.8 19.2-23.466667l17.066667-93.866666h57.6l17.066666 93.866666c2.133333 10.666667 8.533333 19.2 19.2 23.466667l70.4 29.866667c10.666667 4.266667 21.333333 2.133333 29.866667-4.266667l76.8-53.333333 40.533333 40.533333-53.333333 76.8c-6.4 8.533333-8.533333 21.333333-4.266667 29.866667L768 448c4.266667 10.666667 12.8 17.066667 23.466667 19.2l93.866666 17.066667v55.466666z"
        fill="currentColor"
      ></path>
      <path
        d="M512 394.666667c-64 0-117.333333 53.333333-117.333333 117.333333s53.333333 117.333333 117.333333 117.333333 117.333333-53.333333 117.333333-117.333333-53.333333-117.333333-117.333333-117.333333z m0 170.666666c-29.866667 0-53.333333-23.466667-53.333333-53.333333s23.466667-53.333333 53.333333-53.333333 53.333333 23.466667 53.333333 53.333333-23.466667 53.333333-53.333333 53.333333z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  supervisorAccount: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M384 553.984q43.989333 0 102.016 11.989333-102.016 56.021333-102.016 148.010667l0 96-297.984 0 0-105.984q0-45.994667 54.997333-82.005333t121.002667-52.010667 121.984-16zM704 598.016q73.984 0 153.984 32t80 84.010667l0 96-468.010667 0 0-96q0-52.010667 80-84.010667t153.984-32zM384 470.016q-52.010667 0-89.984-38.016t-38.016-89.984 38.016-89.984 89.984-38.016 89.984 38.016 38.016 89.984-38.016 89.984-89.984 38.016zM704 512q-43.989333 0-75.008-31.018667t-31.018667-75.008 31.018667-75.989333 75.008-32 75.008 32 31.018667 75.989333-31.018667 75.008-75.008 31.018667z"
        fill="currentColor"
      ></path>
    </svg>
  ),
  tipsAndUpdates: (
    <svg
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
    >
      <path
        d="M268.8 896h172.8c0 44.8-38.4 83.2-83.2 83.2s-89.6-38.4-89.6-83.2z m-89.6-44.8h339.2V768H179.2v83.2zM672 448c0 160-115.2 249.6-160 275.2H192C147.2 697.6 32 608 32 448c0-179.2 140.8-320 320-320s320 140.8 320 320z m204.8-89.6l-57.6 25.6 57.6 25.6 25.6 57.6 25.6-57.6 64-25.6-57.6-25.6-25.6-57.6-32 57.6z m-96-57.6l38.4-89.6 89.6-38.4-89.6-44.8-38.4-89.6-44.8 89.6-89.6 38.4 89.6 38.4 44.8 96z"
        fill="currentColor"
      ></path>
    </svg>
  ),
};

export const ArrowIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.arrow}</BaseIcon>;
};

export const MoreIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.more}</BaseIcon>;
};

export const PlusIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.plus}</BaseIcon>;
};

export const PlayIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.play}</BaseIcon>;
};

export const Voice1Icon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.voice1}</BaseIcon>;
};

export const Voice2Icon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.voice2}</BaseIcon>;
};

export const Voice3Icon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.voice3}</BaseIcon>;
};

export const UserIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.user}</BaseIcon>;
};

export const PersonAddAltIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.personAddAlt}</BaseIcon>;
};

export const PersonRemoveIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.personRemove}</BaseIcon>;
};

export const SmsIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.sms}</BaseIcon>;
};

export const ArrowFillIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.arrowFill}</BaseIcon>;
};

export const CloseIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.close}</BaseIcon>;
};

export const ReportIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.report}</BaseIcon>;
};

export const DeleteIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.delete}</BaseIcon>;
};

export const SendIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.send}</BaseIcon>;
};

export const StopRoundedIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.stopRounded}</BaseIcon>;
};

export const PlayRoundedIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.playRounded}</BaseIcon>;
};

export const MonetizationOnIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.monetizationOn}</BaseIcon>;
};

export const ErrorIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.error}</BaseIcon>;
};

export const VisibilityIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.visibility}</BaseIcon>;
};

export const VisibilityOffIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.visibilityOff}</BaseIcon>;
};

export const KeyboardIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.keyboard}</BaseIcon>;
};

export const MoodIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.mood}</BaseIcon>;
};

export const AddOutlineIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.addOutline}</BaseIcon>;
};

export const MicIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.mic}</BaseIcon>;
};

export const FavoriteIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.favorite}</BaseIcon>;
};

export const EditIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.edit}</BaseIcon>;
};

export const RadioOnIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.radioOn}</BaseIcon>;
};

export const RadioOffIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.radioOff}</BaseIcon>;
};

export const ShareIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.share}</BaseIcon>;
};

export const SettingIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.setting}</BaseIcon>;
};

export const SupervisorAccountIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.supervisorAccount}</BaseIcon>;
};

export const TipsAndUpdatesIcon = (props: Props) => {
  return <BaseIcon {...props}>{svgList.tipsAndUpdates}</BaseIcon>;
};
