import { PropsWithChildren } from "react";
import { PullToRefresh as AntdPullToRefresh, DotLoading } from "antd-mobile";
import classNames from "classnames";
import "./pull-to-refresh.less";

interface Props {
  className?: string;
  onRefresh: () => Promise<any>;
}

export const PullToRefresh = (props: PropsWithChildren<Props>) => {
  const { className, onRefresh, children } = props;

  return (
    <div className={classNames("w-full h-full pull-to-refresh", className)}>
      <AntdPullToRefresh
        pullingText={<div className="text-[#9d64ff]">下拉刷新</div>}
        canReleaseText={<div className="text-[#9d64ff]">释放立即刷新</div>}
        refreshingText={
          <div className="text-[#9d64ff]">
            <DotLoading color="currentColor" />
          </div>
        }
        completeText={<div className="text-[#9d64ff]">刷新成功</div>}
        onRefresh={onRefresh}
      >
        {children}
      </AntdPullToRefresh>
    </div>
  );
};
