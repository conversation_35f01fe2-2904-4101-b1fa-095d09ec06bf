import { useMemo } from "react";
import { DotLoading, InfiniteScroll } from "antd-mobile";
import {
  type QueryFunctionContext,
  useInfiniteQuery,
} from "@tanstack/react-query";
import { PageParams } from "@/type";
import { PageResponse, type ApiResponse } from "@/lib/request";
import { NoRecord } from "./no-record";

interface Props<T> {
  defaultPage?: number;
  size?: number;
  noMoreStyle?: React.CSSProperties;
  queryKey: any[];
  showTail?: boolean;
  enabled?: boolean;
  emptyElement?: React.ReactNode;
  queryFn: (
    params: PageParams
  ) => Promise<ApiResponse<T[] | PageResponse<T[]>>>;
  children: (data: T[]) => React.ReactNode;
}

export const ScrollLoadData = <T,>(props: Props<T>) => {
  const {
    defaultPage = 1,
    size = 20,
    queryKey,
    queryFn,
    children,
    noMoreStyle,
    enabled = true,
    showTail = true,
    emptyElement,
  } = props;

  const { data, hasNextPage, fetchNextPage } = useInfiniteQuery({
    queryKey,
    queryFn: (ctx: QueryFunctionContext<string[], PageParams>) =>
      queryFn(ctx.pageParam),
    initialPageParam: {
      page: defaultPage,
      size,
    },
    getNextPageParam(lastPageData, _allPagesData, lastPageParam) {
      let page = lastPageParam.page + 1;
      let size = lastPageParam.size;

      if (lastPageData.data && "last" in lastPageData.data) {
        page = Number(lastPageData.data.currentPage) + 1;
        size = Number(lastPageData.data.size);

        if (lastPageData.data.last) {
          return null;
        }
      } else {
        // 忽略分页就通过数据长度判断
        const len = lastPageData.data?.length || 0;

        if (len < size) {
          //  没有下一页了
          return null;
        }
      }

      return {
        page,
        size,
      };
    },
    enabled,
  });

  const datasource: T[] = useMemo(
    () =>
      data?.pages
        .map((page) => {
          if (page.data) {
            if ("last" in page.data) {
              return page.data.data ?? [];
            }

            return page.data;
          }

          return [];
        })
        .flat() || [],
    [data]
  );

  const renderFooter = useMemo(() => {
    if (data === undefined || hasNextPage) {
      // 第一次加载 || 其他加载情况
      return (
        <div className="text-[#9d64ff] w-full h-full flex justify-center items-center gap-1">
          <span>加载中</span>
          <DotLoading color="currentColor" />
        </div>
      );
    }
    if (showTail) {
      return (
        <span className="text-foreground" style={noMoreStyle}>
          没有更多数据了～
        </span>
      );
    }
    return null;
  }, [data, hasNextPage, noMoreStyle, showTail]);

  if (data !== undefined && !hasNextPage && !datasource.length) {
    return emptyElement || <NoRecord style={noMoreStyle} />;
  }

  return (
    <>
      {children(datasource)}
      <InfiniteScroll
        className="!p-1 !pt-3"
        hasMore={hasNextPage}
        loadMore={async () => {
          await fetchNextPage();
        }}
      >
        {renderFooter}
      </InfiniteScroll>
    </>
  );
};
