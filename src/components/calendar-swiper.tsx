import { Swiper } from "antd-mobile";
import dayjs from "dayjs";
import { useMemo, useState } from "react";
import { Calendar } from "./calendar";
import { UserPunchInItemResult } from "@/type/user-punch-in-item-result";
import { getCalendarDays } from "@/utils/get-calendar-days";

interface Props {
  date: dayjs.Dayjs;
  isLoading: boolean;
  punchInInfo?: UserPunchInItemResult[];
  onDateChange: (date: dayjs.Dayjs) => void;
  onRePunchInClick: (date: dayjs.Dayjs) => void;
}

export const CalendarSwiper = (props: Props) => {
  const { date, punchInInfo, isLoading, onDateChange, onRePunchInClick } =
    props;

  const [swiperIndex, setSwiperIndex] = useState(1);

  const list = useMemo(() => {
    if (swiperIndex === 0) {
      return [date, date.add(1, "month"), date.subtract(1, "month")];
    }
    if (swiperIndex === 1) {
      return [date.subtract(1, "month"), date, date.add(1, "month")];
    }
    return [date.add(1, "month"), date.subtract(1, "month"), date];
  }, [swiperIndex, date]);

  const handleIndexChange = (index: number) => {
    const item = list[index];
    setSwiperIndex(index);
    onDateChange(item);
  };

  const weeks = useMemo(() => {
    return Math.floor(getCalendarDays(list[swiperIndex]).length / 7);
  }, [swiperIndex, list]);

  return (
    <Swiper
      defaultIndex={1}
      indicator={false}
      onIndexChange={handleIndexChange}
      loop
      style={{
        height: `${30 + 40 * weeks}px`,
      }}
    >
      {list.map((item) => {
        return (
          <Swiper.Item key={item.format("YYYY-MM")}>
            <Calendar
              date={item}
              punchInInfo={punchInInfo}
              isLoading={isLoading}
              onRePunchInClick={onRePunchInClick}
            />
          </Swiper.Item>
        );
      })}
    </Swiper>
  );
};
