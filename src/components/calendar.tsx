import { UserPunchInItemResult } from "@/type/user-punch-in-item-result";
import { calendarHeader } from "@/utils/constant";
import { getCalendarDays } from "@/utils/get-calendar-days";
import dayjs from "dayjs";
import { useCallback, useMemo } from "react";

interface Props {
  date: dayjs.Dayjs;
  isLoading: boolean;
  punchInInfo?: UserPunchInItemResult[];
  onRePunchInClick: (date: dayjs.Dayjs) => void;
}

export const Calendar = (props: Props) => {
  const { date, punchInInfo = [], isLoading, onRePunchInClick } = props;

  console.log(date.format("YYYY-MM"));

  const getSignInStatus = (day: dayjs.Dayjs) => {
    // 加载中 或者 不是当前月份 或者 在未来时间
    const invalid =
      isLoading || !day.isSame(date, "month") || day.isAfter(dayjs());

    if (invalid) {
      return undefined;
    }

    const item = punchInInfo.find(
      (item) => item.date === day.format("YYYY-MM-DD 00:00:00")
    );

    return item?.punchIn;
  };

  const calendarDays = useMemo(() => {
    return getCalendarDays(date);
  }, [date]);

  const weekDays = useMemo(() => {
    return calendarDays.reduce((prev, curVal, index) => {
      const week = Math.floor(index / 7);

      if (prev[week]) {
        prev[week].push(curVal);
      } else {
        prev[week] = [curVal];
      }

      return prev;
    }, [] as dayjs.Dayjs[][]);
  }, [calendarDays]);

  const renderDayInfo = useCallback((day: dayjs.Dayjs, status: boolean) => {
    if (status) {
      return (
        <span className="text-[#FF595D] text-[20px]" style={{ zoom: 0.5 }}>
          已签到
        </span>
      );
    }

    return (
      <span
        className="w-[27px] h-[13px] rounded-[6px] flex justify-center items-center bg-[#FFE8E8]"
        onClick={() => {
          onRePunchInClick(day);
        }}
      >
        <span className="text-[#FFA19F] text-[20px]" style={{ zoom: 0.5 }}>
          补签
        </span>
      </span>
    );
  }, []);

  return (
    <div className="w-full text-black">
      <div className="grid grid-cols-7 text-[13px]">
        {calendarHeader.map((day) => (
          <div key={day} className="h-[30px] flex items-center justify-center">
            {day}
          </div>
        ))}
      </div>
      {weekDays.map((item, index) => (
        <div key={index} className="grid grid-cols-7 text-[15px]">
          {item.map((day, index) => {
            const status = getSignInStatus(day);

            return (
              <div
                key={index}
                className="h-[40px] flex flex-col items-center justify-center text-[#999]"
                style={typeof status === "boolean" ? { color: "#000" } : {}}
                title={day.format("YYYY-MM-DD")}
              >
                {day.date()}
                {typeof status === "boolean"
                  ? renderDayInfo(day, status)
                  : null}
              </div>
            );
          })}
        </div>
      ))}
    </div>
  );
};
