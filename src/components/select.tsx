import { Input } from "@/ui/input";
import { Divider, Popup } from "antd-mobile";
import { Fragment, useMemo, useState } from "react";

interface Props {
  options: {
    title: string;
    value: number | string;
  }[];
  titleRender?: (label: string) => string;
  disabled?: boolean;
  placeholder?: string;
  value?: number | string;
  onChange?: (value: number | string) => void;
}

export const Select = (props: Props) => {
  const { options, value, onChange, placeholder, disabled, titleRender } =
    props;

  const label = useMemo(() => {
    return options.find((item) => item.value === value)?.title;
  }, [options, value]);

  const [visible, setVisible] = useState(false);

  const handleSelect = (value: number | string) => {
    setVisible(false);
    onChange?.(value);
  };

  return (
    <div>
      <Input
        placeholder={placeholder}
        autoComplete="off"
        value={label && titleRender ? titleRender(label) : label}
        readOnly
        disabled={disabled}
        onClick={() => setVisible(true)}
      />
      <Popup
        visible={visible}
        bodyClassName="bg-transparent"
        onMaskClick={() => setVisible(false)}
        onClose={() => setVisible(false)}
      >
        <div className="flex flex-col gap-[10px] mx-[15px]">
          <div className="flex flex-col rounded-[10px] bg-app-bar-background">
            {options.map((item, index) => (
              <Fragment key={item.value}>
                <div
                  className="py-[15px] text-center text-base text-[#9d64ff]"
                  onClick={() => handleSelect(item.value)}
                >
                  {item.title}
                </div>
                {index !== options.length - 1 ? (
                  <Divider className="border-divider m-0" />
                ) : null}
              </Fragment>
            ))}
          </div>
          <div
            className="mb-[10px] py-[15px] text-center text-base text-[#9d64ff] bg-hint-color rounded-[10px]"
            onClick={() => setVisible(false)}
          >
            取消
          </div>
        </div>
      </Popup>
    </div>
  );
};
