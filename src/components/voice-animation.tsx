import { useState, useEffect } from "react";
import { Voice1Icon, Voice2Icon, Voice3Icon } from "./svg-icon";

interface Props {
  isPlaying: boolean;
}

const VoiceAnimation = ({ isPlaying }: Props) => {
  const [currentIcon, setCurrentIcon] = useState(0);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (isPlaying) {
      intervalId = setInterval(() => {
        setCurrentIcon((prev) => (prev + 1) % 3);
      }, 400); // 每400毫秒切换一次图标
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isPlaying]);

  const renderIcon = () => {
    switch (currentIcon) {
      case 0:
        return <Voice1Icon size={2} className="text-[#fff]" />;
      case 1:
        return <Voice2Icon size={6} className="text-[#fff]" />;
      case 2:
        return <Voice3Icon size={11} className="text-[#fff]" />;
      default:
        return <Voice3Icon size={11} className="text-[#fff]" />;
    }
  };

  if (!isPlaying) {
    // 未播放时图标
    return <Voice3Icon size={11} className="text-[#fff]" />;
  }

  return <div>{renderIcon()}</div>;
};

export default VoiceAnimation;
