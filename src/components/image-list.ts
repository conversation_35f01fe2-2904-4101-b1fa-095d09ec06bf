// common-icon
import barBack from "../assets/images/icon/bar-back.png";
import chargeSign from "../assets/images/icon/charge-sign.png";
import chatRoomMember from "../assets/images/icon/chat_room_member.png";
import checkCircle from "../assets/images/icon/check-circle.png";
import comment from "../assets/images/icon/comment.png";
import communitySelected from "../assets/images/icon/community-selected.png";
import featured from "../assets/images/icon/featured.png";
import forward from "../assets/images/icon/forward.png";
import gift from "../assets/images/icon/gift.png";
import gold from "../assets/images/icon/gold.png";
import likeSelected from "../assets/images/icon/like-selected.png";
import like from "../assets/images/icon/like.png";
import location from "../assets/images/icon/location.png";
import messageSelected from "../assets/images/icon/message-selected.png";
import mindWallLove from "../assets/images/icon/mind-wall-love.png";
import mySelected from "../assets/images/icon/my-selected.png";
import search from "../assets/images/icon/search.png";
import state_0 from "../assets/images/icon/state_0.png";
import state_1 from "../assets/images/icon/state_1.png";
import state_2 from "../assets/images/icon/state_2.png";
import stickie from "../assets/images/icon/stickie.png";
import topic from "../assets/images/icon/topic.png";
import videoSelected from "../assets/images/icon/video-selected.png";
import vip from "../assets/images/icon/vip.png";
import gender0 from "../assets/images/gender/0.png";
import gender1 from "../assets/images/gender/1.png";
import gender2 from "../assets/images/gender/2.png";
import device from "../assets/images/platform/device.png";
import qq from "../assets/images/platform/qq.png";
import wechat from "../assets/images/platform/wechat.png";
import question from "../assets/images/icon/question.png";
import paySuccess from "../assets/images/icon/pay-success.png";
import currency from "../assets/images/icon/currency.png";
import confirmIcon from "../assets/images/icon/confirm-icon.png";
import tipsError from "../assets/images/icon/tips-error.png";
import tipsSuccess from "../assets/images/icon/tips-success.png";
import chargeConfigChat from "../assets/images/icon/charge-config/chat.png";
import chargeConfigVideo from "../assets/images/icon/charge-config/video.png";
import chargeConfigVoice from "../assets/images/icon/charge-config/voice.png";
import charm from "../assets/images/icon/level/charm.png";
import charmSelected from "../assets/images/icon/level/charm-selected.png";
import heroism from "../assets/images/icon/level/heroism.png";
import heroismSelected from "../assets/images/icon/level/heroism-selected.png";
import integral from "../assets/images/icon/level/integral.png";
import integralSelected from "../assets/images/icon/level/integral-selected.png";
import level from "../assets/images/icon/level/level.png";
import ranking1 from "../assets/images/icon/level/ranking_1.png";
import ranking2 from "../assets/images/icon/level/ranking_2.png";
import ranking3 from "../assets/images/icon/level/ranking_3.png";
import logo from "../assets/images/icon/logo.png";
import mind from "../assets/images/icon/mind.png";
import chat from "../assets/images/icon/chat.png";
import video from "../assets/images/icon/video.png";
import follow from "../assets/images/icon/follow.png";
import bean from "../assets/images/icon/bean.png";
import collection from "../assets/images/icon/collection.png";
import collectionSelected from "../assets/images/icon/collection-selected.png";
import shareDel from "../assets/images/icon/share/del.png";
import shareDownload from "../assets/images/icon/share/download.png";
import shareFriend from "../assets/images/icon/share/friend.png";
import shareLink from "../assets/images/icon/share/link.png";
import shareMoment from "../assets/images/icon/share/moment.png";
import sharePoster from "../assets/images/icon/share/poster.png";
import shareQq from "../assets/images/icon/share/qq.png";
import shareQzone from "../assets/images/icon/share/qzone.png";
import shareReport from "../assets/images/icon/share/report.png";
import shareWechat from "../assets/images/icon/share/wechat.png";
import voiceRecordStill from "../assets/images/icon/voice_record_still.png";
import download from "../assets/images/icon/download.png";
import rankingList from "../assets/images/icon/ranking-list.png";
import shortVideoCollectionSelected from "../assets/images/icon/short-video/collection-selected.png";
import shortVideoCollection from "../assets/images/icon/short-video/collection.png";
import shortVideoComment from "../assets/images/icon/short-video/comment.png";
import shortVideoLikeSelected from "../assets/images/icon/short-video/like-selected.png";
import shortVideoLike from "../assets/images/icon/short-video/like.png";
import shortVideoShare from "../assets/images/icon/short-video/share.png";
import customerService from "../assets/images/icon/customer-service.png";
import userTransactionBid from "../assets/images/icon/user-transaction-bid.png";
import add from "../assets/images/icon/add.png";
import tradeBuy from "../assets/images/icon/trade/buy.png";
import tradeSell from "../assets/images/icon/trade/sell.png";
import rtcAnswer from "../assets/images/icon/rtc/answer.png";
import rtcBeauty from "../assets/images/icon/rtc/beauty.png";
import rtcFlip from "../assets/images/icon/rtc/flip.png";
import rtcHangUp from "../assets/images/icon/rtc/hang-up.png";
import rtcMicrophoneOff from "../assets/images/icon/rtc/microphone-off.png";
import rtcMicrophoneOn from "../assets/images/icon/rtc/microphone-on.png";
import rtcSpeakerOff from "../assets/images/icon/rtc/speaker-off.png";
import rtcSpeakerOn from "../assets/images/icon/rtc/speaker-on.png";

// theme-icon
// light
import lightImage from "../assets/images/light/icon/image.png";
import lightCommunity from "../assets/images/light/icon/community.png";
import lightCustomerService from "../assets/images/light/icon/customer-service.png";
import lightCommunitySelected from "../assets/images/light/icon/community-selected.png";
import lightMessageSelected from "../assets/images/light/icon/message-selected.png";
import lightMessage from "../assets/images/light/icon/message.png";
import lightMySelected from "../assets/images/light/icon/my-selected.png";
import lightMy from "../assets/images/light/icon/my.png";
import lightVideoSelected from "../assets/images/light/icon/video-selected.png";
import lightVideo from "../assets/images/light/icon/video.png";
import lightVoice from "../assets/images/light/icon/voice.png";
import lightLive from "../assets/images/light/icon/live.png";
import lightLiveSelected from "../assets/images/light/icon/live-selected.png";
import lightMenuAgent from "../assets/images/light/icon/menus/agent.png";
import lightMenuBuyRecord from "../assets/images/light/icon/menus/buy-record.png";
import lightMenuCamouflage from "../assets/images/light/icon/menus/camouflage.png";
import lightMenuCollection from "../assets/images/light/icon/menus/collection.png";
import lightMenuCopartner from "../assets/images/light/icon/menus/copartner.png";
import lightMenuFeedback from "../assets/images/light/icon/menus/feedback.png";
import lightMenuGradle from "../assets/images/light/icon/menus/gradle.png";
import lightMenuIdcardAuth from "../assets/images/light/icon/menus/idcard-auth.png";
import lightMenuJoinSpecial from "../assets/images/light/icon/menus/join-special.png";
import lightMenuLiveGuild from "../assets/images/light/icon/menus/live-guild.png";
import lightMenuMedal from "../assets/images/light/icon/menus/medal.png";
import lightMenuMerchantTransaction from "../assets/images/light/icon/menus/merchant-transaction.png";
import lightMenuMounts from "../assets/images/light/icon/menus/mounts.png";
import lightMenuOrderRefund from "../assets/images/light/icon/menus/order-refund.png";
import lightMenuPersonalTransaction from "../assets/images/light/icon/menus/personal-transaction.png";
import lightMenuPublish from "../assets/images/light/icon/menus/publish.png";
import lightMenuQrcode from "../assets/images/light/icon/menus/qrcode.png";
import lightMenuRealAuth from "../assets/images/light/icon/menus/real-auth.png";
import lightMenuSetting from "../assets/images/light/icon/menus/setting.png";
import lightMenuSignIn from "../assets/images/light/icon/menus/sign-in.png";
import lightMenuTask from "../assets/images/light/icon/menus/task.png";
import lightMenuTransmission from "../assets/images/light/icon/menus/transmission.png";
import lightMenuTreasureLottery from "../assets/images/light/icon/menus/treasure-lottery.png";
import lightMenuTurntableLottery from "../assets/images/light/icon/menus/turntable-lottery.png";
import lightMenuVip from "../assets/images/light/icon/menus/vip.png";
import lightMenuVisitRecord from "../assets/images/light/icon/menus/visit-record.png";
import lightMenuWallet from "../assets/images/light/icon/menus/wallet.png";
import lightSettingAbout from "../assets/images/light/icon/settings/about.png";
import lightSettingBlacklist from "../assets/images/light/icon/settings/blacklist.png";
import lightSettingCommon from "../assets/images/light/icon/settings/common.png";
import lightSettingLocation from "../assets/images/light/icon/settings/location.png";
import lightSettingMessage from "../assets/images/light/icon/settings/message.png";
import lightSettingPrivacy from "../assets/images/light/icon/settings/privacy.png";
import lightSettingSecurity from "../assets/images/light/icon/settings/security.png";
import lightChatCamera from "../assets/images/light/icon/chat/camera.png";
import lightChatGift from "../assets/images/light/icon/chat/gift.png";
import lightChatImage from "../assets/images/light/icon/chat/image.png";
import lightChatInteractive from "../assets/images/light/icon/chat/interactive.png";
import lightChatRedEnvelope from "../assets/images/light/icon/chat/red_envelope.png";
import lightChatRtc from "../assets/images/light/icon/chat/rtc.png";
import lightMicrophone from "../assets/images/light/icon/microphone.png";

// dark
import darkCommunitySelected from "../assets/images/dark/icon/community-selected.png";
import darkCommunity from "../assets/images/dark/icon/community.png";
import darkCustomerService from "../assets/images/dark/icon/customer-service.png";
import darkImage from "../assets/images/dark/icon/image.png";
import darkMessageSelected from "../assets/images/dark/icon/message-selected.png";
import darkMessage from "../assets/images/dark/icon/message.png";
import darkMySelected from "../assets/images/dark/icon/my-selected.png";
import darkMy from "../assets/images/dark/icon/my.png";
import darkVideoSelected from "../assets/images/dark/icon/video-selected.png";
import darkVideo from "../assets/images/dark/icon/video.png";
import darkVoice from "../assets/images/dark/icon/voice.png";
import darkLive from "../assets/images/dark/icon/live.png";
import darkLiveSelected from "../assets/images/dark/icon/live-selected.png";
import darkMenuAgent from "../assets/images/dark/icon/menus/agent.png";
import darkMenuBuyRecord from "../assets/images/dark/icon/menus/buy-record.png";
import darkMenuCamouflage from "../assets/images/dark/icon/menus/camouflage.png";
import darkMenuCollection from "../assets/images/dark/icon/menus/collection.png";
import darkMenuCopartner from "../assets/images/dark/icon/menus/copartner.png";
import darkMenuFeedback from "../assets/images/dark/icon/menus/feedback.png";
import darkMenuGradle from "../assets/images/dark/icon/menus/gradle.png";
import darkMenuIdcardAuth from "../assets/images/dark/icon/menus/idcard-auth.png";
import darkMenuJoinSpecial from "../assets/images/dark/icon/menus/join-special.png";
import darkMenuLiveGuild from "../assets/images/dark/icon/menus/live-guild.png";
import darkMenuMedal from "../assets/images/dark/icon/menus/medal.png";
import darkMenuMerchantTransaction from "../assets/images/dark/icon/menus/merchant-transaction.png";
import darkMenuMounts from "../assets/images/dark/icon/menus/mounts.png";
import darkMenuOrderRefund from "../assets/images/dark/icon/menus/order-refund.png";
import darkMenuPersonalTransaction from "../assets/images/dark/icon/menus/personal-transaction.png";
import darkMenuPublish from "../assets/images/dark/icon/menus/publish.png";
import darkMenuQrcode from "../assets/images/dark/icon/menus/qrcode.png";
import darkMenuRealAuth from "../assets/images/dark/icon/menus/real-auth.png";
import darkMenuSetting from "../assets/images/dark/icon/menus/setting.png";
import darkMenuSignIn from "../assets/images/dark/icon/menus/sign-in.png";
import darkMenuTask from "../assets/images/dark/icon/menus/task.png";
import darkMenuTransmission from "../assets/images/dark/icon/menus/transmission.png";
import darkMenuTreasureLottery from "../assets/images/dark/icon/menus/treasure-lottery.png";
import darkMenuTurntableLottery from "../assets/images/dark/icon/menus/turntable-lottery.png";
import darkMenuVip from "../assets/images/dark/icon/menus/vip.png";
import darkMenuVisitRecord from "../assets/images/dark/icon/menus/visit-record.png";
import darkMenuWallet from "../assets/images/dark/icon/menus/wallet.png";
import darkSettingAbout from "../assets/images/dark/icon/settings/about.png";
import darkSettingBlacklist from "../assets/images/dark/icon/settings/blacklist.png";
import darkSettingCommon from "../assets/images/dark/icon/settings/common.png";
import darkSettingLocation from "../assets/images/dark/icon/settings/location.png";
import darkSettingMessage from "../assets/images/dark/icon/settings/message.png";
import darkSettingPrivacy from "../assets/images/dark/icon/settings/privacy.png";
import darkSettingSecurity from "../assets/images/dark/icon/settings/security.png";
import darkChatCamera from "../assets/images/dark/icon/chat/camera.png";
import darkChatGift from "../assets/images/dark/icon/chat/gift.png";
import darkChatImage from "../assets/images/dark/icon/chat/image.png";
import darkChatInteractive from "../assets/images/dark/icon/chat/interactive.png";
import darkChatRedEnvelope from "../assets/images/dark/icon/chat/red_envelope.png";
import darkChatRtc from "../assets/images/dark/icon/chat/rtc.png";
import darkMicrophone from "../assets/images/dark/icon/microphone.png";

// images
import noRecord from "../assets/images/no-record.png";
import imageLoadDefault from "../assets/images/image-load-default.png";
import communityTabBg from "../assets/images/community-tab-bg.png";
import topicDetailBg from "../assets/images/topic-detailed-default-background.png";
import vipBanner from "../assets/images/vip-banner.png";
import walletBackground from "../assets/images/wallet-background.png";
import vipBackground from "../assets/images/vip-background.png";
import proxyBackground from "../assets/images/proxy-background.png";
import userInvitePosterBackgroundOld from "../assets/images/user-invite-poster-background-old.png";
import userInfoBackground from "../assets/images/user-info-background.png";
import copartnerBackground from "../assets/images/copartner-background.png";
import signInBackgroundCalendar from "../assets/images/sign-in-background-calendar.png";
import signInBackground from "../assets/images/sign-in-background.png";
import idcardAuthSuccess from "../assets/images/idcard-auth-success.png";
import userLevelHomeBackground from "../assets/images/user-level-home-background.png";
import contactBackground from "../assets/images/contact-background.png";
import rechargeCostVipBackground from "../assets/images/recharge-cost-vip-background.png";
import rechargeCostVipSelectedBackground from "../assets/images/recharge-cost-vip-selected-background.png";
import userLevelRankingBackground from "../assets/images/user-level-ranking-background.png";
import redEnvelopeEfficient from "../assets/images/chat/red-envelope-efficient.png";
import redEnvelopeInvalid from "../assets/images/chat/red-envelope-invalid.png";
import discountActivityBackground from "../assets/images/discount-activity-background.png";
import marginBackground from "../assets/images/margin-background.png";
import callWaitBackground from "../assets/images/call-wait-background.png";
import liveBackground from "../assets/images/live-background.png";
import liveBuy from "../assets/images/live-buy.png";

// images theme
import lightLoginBackground from "../assets/images/light/login-background.png";
import darkLoginBackground from "../assets/images/dark/login-background.png";
import lightHomeTopBackground from "../assets/images/light/home-top-background.png";
import darkHomeTopBackground from "../assets/images/dark/home-top-background.png";
import lightWalletBillBackground from "../assets/images/light/wallet-bill-background.png";
import darkWalletBillBackground from "../assets/images/dark/wallet-bill-background.png";

export {
  noRecord,
  imageLoadDefault,
  communityTabBg,
  topicDetailBg,
  vipBanner,
  walletBackground,
  vipBackground,
  proxyBackground,
  userInvitePosterBackgroundOld,
  userInfoBackground,
  copartnerBackground,
  signInBackgroundCalendar,
  signInBackground,
  idcardAuthSuccess,
  userLevelHomeBackground,
  contactBackground,
  rechargeCostVipBackground,
  rechargeCostVipSelectedBackground,
  userLevelRankingBackground,
  redEnvelopeEfficient,
  redEnvelopeInvalid,
  discountActivityBackground,
  marginBackground,
  callWaitBackground,
  liveBackground,
  liveBuy,
};

export {
  lightLoginBackground,
  darkLoginBackground,
  lightHomeTopBackground,
  darkHomeTopBackground,
  lightWalletBillBackground,
  darkWalletBillBackground,
};

export const commonIconList: Record<string, string> = {
  "bar-back": barBack,
  "charge-sign": chargeSign,
  chat_room_member: chatRoomMember,
  "check-circle": checkCircle,
  comment: comment,
  "community-selected": communitySelected,
  featured: featured,
  forward: forward,
  gift: gift,
  gold: gold,
  "like-selected": likeSelected,
  like: like,
  location,
  "message-selected": messageSelected,
  "mind-wall-love": mindWallLove,
  "my-selected": mySelected,
  search,
  state_0,
  state_1,
  state_2,
  stickie,
  topic,
  videoSelected,
  vip,
  gender0,
  gender1,
  gender2,
  device,
  qq,
  wechat,
  question,
  paySuccess,
  currency,
  confirmIcon,
  tipsError,
  tipsSuccess,
  chargeConfigChat,
  chargeConfigVideo,
  chargeConfigVoice,
  charm,
  charmSelected,
  heroism,
  heroismSelected,
  integral,
  integralSelected,
  level,
  ranking1,
  ranking2,
  ranking3,
  logo,
  mind,
  chat,
  video,
  follow,
  bean,
  collection,
  "collection-selected": collectionSelected,
  "share/del": shareDel,
  "share/download": shareDownload,
  "share/friend": shareFriend,
  "share/link": shareLink,
  "share/moment": shareMoment,
  "share/poster": sharePoster,
  "share/qq": shareQq,
  "share/qzone": shareQzone,
  "share/report": shareReport,
  "share/wechat": shareWechat,
  voiceRecordStill,
  download,
  "ranking-list": rankingList,
  "short-video/collection-selected": shortVideoCollectionSelected,
  "short-video/collection": shortVideoCollection,
  "short-video/comment": shortVideoComment,
  "short-video/like-selected": shortVideoLikeSelected,
  "short-video/like": shortVideoLike,
  "short-video/share": shortVideoShare,
  customerService,
  userTransactionBid,
  add,
  tradeBuy,
  tradeSell,
  rtcAnswer,
  rtcBeauty,
  rtcFlip,
  rtcHangUp,
  rtcMicrophoneOff,
  rtcMicrophoneOn,
  rtcSpeakerOff,
  rtcSpeakerOn,
};

export const themeIconList: {
  light: Record<string, string>;
  dark: Record<string, string>;
} = {
  light: {
    "community-selected": lightCommunitySelected,
    community: lightCommunity,
    "customer-service": lightCustomerService,
    image: lightImage,
    "message-selected": lightMessageSelected,
    message: lightMessage,
    "my-selected": lightMySelected,
    my: lightMy,
    "video-selected": lightVideoSelected,
    video: lightVideo,
    voice: lightVoice,
    live: lightLive,
    "live-selected": lightLiveSelected,
    "menus/agent": lightMenuAgent,
    "menus/buy-record": lightMenuBuyRecord,
    "menus/camouflage": lightMenuCamouflage,
    "menus/collection": lightMenuCollection,
    "menus/copartner": lightMenuCopartner,
    "menus/feedback": lightMenuFeedback,
    "menus/gradle": lightMenuGradle,
    "menus/idcard-auth": lightMenuIdcardAuth,
    "menus/join-special": lightMenuJoinSpecial,
    "menus/live-guild": lightMenuLiveGuild,
    "menus/medal": lightMenuMedal,
    "menus/merchant-transaction": lightMenuMerchantTransaction,
    "menus/mounts": lightMenuMounts,
    "menus/order-refund": lightMenuOrderRefund,
    "menus/personal-transaction": lightMenuPersonalTransaction,
    "menus/publish": lightMenuPublish,
    "menus/qrcode": lightMenuQrcode,
    "menus/real-auth": lightMenuRealAuth,
    "menus/setting": lightMenuSetting,
    "menus/sign-in": lightMenuSignIn,
    "menus/task": lightMenuTask,
    "menus/transmission": lightMenuTransmission,
    "menus/treasure-lottery": lightMenuTreasureLottery,
    "menus/turntable-lottery": lightMenuTurntableLottery,
    "menus/vip": lightMenuVip,
    "menus/visit-record": lightMenuVisitRecord,
    "menus/wallet": lightMenuWallet,
    "settings/about": lightSettingAbout,
    "settings/blacklist": lightSettingBlacklist,
    "settings/common": lightSettingCommon,
    "settings/location": lightSettingLocation,
    "settings/message": lightSettingMessage,
    "settings/privacy": lightSettingPrivacy,
    "settings/security": lightSettingSecurity,
    "chat/camera": lightChatCamera,
    "chat/gift": lightChatGift,
    "chat/image": lightChatImage,
    "chat/interactive": lightChatInteractive,
    "chat/red_envelope": lightChatRedEnvelope,
    "chat/rtc": lightChatRtc,
    microphone: lightMicrophone,
  },
  dark: {
    "community-selected": darkCommunitySelected,
    community: darkCommunity,
    "customer-service": darkCustomerService,
    image: darkImage,
    "message-selected": darkMessageSelected,
    message: darkMessage,
    "my-selected": darkMySelected,
    my: darkMy,
    "video-selected": darkVideoSelected,
    video: darkVideo,
    voice: darkVoice,
    live: darkLive,
    "live-selected": darkLiveSelected,
    "menus/agent": darkMenuAgent,
    "menus/buy-record": darkMenuBuyRecord,
    "menus/camouflage": darkMenuCamouflage,
    "menus/collection": darkMenuCollection,
    "menus/copartner": darkMenuCopartner,
    "menus/feedback": darkMenuFeedback,
    "menus/gradle": darkMenuGradle,
    "menus/idcard-auth": darkMenuIdcardAuth,
    "menus/join-special": darkMenuJoinSpecial,
    "menus/live-guild": darkMenuLiveGuild,
    "menus/medal": darkMenuMedal,
    "menus/merchant-transaction": darkMenuMerchantTransaction,
    "menus/mounts": darkMenuMounts,
    "menus/order-refund": darkMenuOrderRefund,
    "menus/personal-transaction": darkMenuPersonalTransaction,
    "menus/publish": darkMenuPublish,
    "menus/qrcode": darkMenuQrcode,
    "menus/real-auth": darkMenuRealAuth,
    "menus/setting": darkMenuSetting,
    "menus/sign-in": darkMenuSignIn,
    "menus/task": darkMenuTask,
    "menus/transmission": darkMenuTransmission,
    "menus/treasure-lottery": darkMenuTreasureLottery,
    "menus/turntable-lottery": darkMenuTurntableLottery,
    "menus/vip": darkMenuVip,
    "menus/visit-record": darkMenuVisitRecord,
    "menus/wallet": darkMenuWallet,
    "settings/about": darkSettingAbout,
    "settings/blacklist": darkSettingBlacklist,
    "settings/common": darkSettingCommon,
    "settings/location": darkSettingLocation,
    "settings/message": darkSettingMessage,
    "settings/privacy": darkSettingPrivacy,
    "settings/security": darkSettingSecurity,
    "chat/camera": darkChatCamera,
    "chat/gift": darkChatGift,
    "chat/image": darkChatImage,
    "chat/interactive": darkChatInteractive,
    "chat/red_envelope": darkChatRedEnvelope,
    "chat/rtc": darkChatRtc,
    microphone: darkMicrophone,
  },
};
