import { Image } from "antd-mobile";
import { noRecord } from "./image-list";

interface Props {
  style?: React.CSSProperties;
}

export const NoRecord = (props: Props) => {
  return (
    <div className="w-full h-full justify-center flex flex-col items-center py-2 gap-2">
      <Image
        src={noRecord}
        fit="cover"
        width={`${150 / 16}rem`}
        height="auto"
        placeholder={null}
      />
      <span className="text-foreground" style={props.style}>
        没有找到数据～
      </span>
    </div>
  );
};
