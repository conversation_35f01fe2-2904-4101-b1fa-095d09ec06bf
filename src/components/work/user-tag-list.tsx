import { User } from "@/type/user";
import { colorToRgba } from "@/utils";
import { genderConfig } from "@/utils/enums";
import { useMemo } from "react";

export const UserTagList = ({
  user,
  transparent = true,
}: {
  user: User;
  transparent?: boolean;
}) => {
  const { gender, age, tags } = user;

  const ageTag = useMemo(() => {
    const config = genderConfig.find((config) => config.value === gender);

    if (config === undefined) {
      return null;
    }

    return (
      <div
        className="h-full rounded-[12px] flex"
        style={{
          background: `hsl(${config.color}, ${transparent ? 0.2 : 1})`,
        }}
      >
        <div
          className="w-[14px] h-full rounded-full flex justify-center items-center"
          style={{ background: `hsl(${config.color})` }}
        >
          <span
            className="text-[18px] text-white font-bold"
            style={{ zoom: 0.5 }}
          >
            {config.label}
          </span>
        </div>
        <div
          className="h-full p-[5px] flex justify-center items-center"
          style={{ color: transparent ? `hsl(${config.color})` : "#fff" }}
        >
          <span
            className="text-[18px] font-bold"
            style={{
              zoom: 0.5,
            }}
          >
            {age}
          </span>
        </div>
      </div>
    );
  }, [transparent, age, gender]);

  const userTag = useMemo(() => {
    return (
      tags?.map((tag) => {
        return (
          <div
            key={tag.label}
            style={{
              background: colorToRgba(
                tag.backgroundColor,
                transparent ? undefined : 1
              ),
            }}
            className="h-full px-[5px] rounded-[12px] flex justify-center items-center flex-none"
          >
            <span
              className="text-[18px] font-medium flex-none"
              style={{
                zoom: 0.5,
                color: transparent ? colorToRgba(tag.color) : "#fff",
              }}
            >
              {tag.label}
            </span>
          </div>
        );
      }) ?? null
    );
  }, [tags, transparent]);

  if (!tags?.length) return null;

  return (
    <div className="flex w-full gap-1 h-[14px]">
      {ageTag}
      {userTag}
    </div>
  );
};
