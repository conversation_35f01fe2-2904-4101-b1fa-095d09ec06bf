import {
  shortVideoCommentList,
  shortVideoCommentPublishCheck,
  shortVideoCommentPublishUrl,
} from "@/lib/api/video";
import { PageParams } from "@/type";
import { ShortVideoResult } from "@/type/short-video-result";
import { <PERSON>vide<PERSON>, Popup, Toast } from "antd-mobile";
import { ScrollLoadData } from "../scroll-load-data";
import { CommentItem } from "./comment-item";
import { Fragment } from "react/jsx-runtime";
import { CommentInput, FinishType } from "./comment-input";
import { ReactNode, useState } from "react";
import { useUploadQueueManager } from "@/hooks/use-upload-queue-manager";
import { useMutation } from "@tanstack/react-query";
import { getRandomParams } from "@/utils/common";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { CommonCommentResult } from "@/type/common-comment-result";
import { PullToRefresh } from "../pull-to-refresh";

interface Props {
  data: ShortVideoResult;
  open: boolean;
  onClose: () => void;
}

export const ShortVideoCommentModal = (props: Props) => {
  const { data: shortVideo, open, onClose } = props;

  const [quote, setQuote] = useState<
    { id: string; content: ReactNode } | undefined
  >();

  const queryKey = ["short-video-comment-list", shortVideo.id];
  const queryFn = (params: PageParams) =>
    shortVideoCommentList({ targetId: shortVideo.id! }, params);

  const { handleRefresh } = usePageListRefresh(queryKey);

  const { addToQueue } = useUploadQueueManager();

  const shortVideoCommentPublishCheckMutations = useMutation({
    mutationFn: shortVideoCommentPublishCheck,
  });

  const handleComment = async (data: FinishType) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await shortVideoCommentPublishCheckMutations.mutateAsync({
      targetId: shortVideo.id!,
      content: data.text,
      ats: data.ats,
      voice: data.voice ? getRandomParams() : undefined,
      resources: data.resource
        ? data.resource.map(() => getRandomParams())
        : undefined,
      topId: quote?.id,
      parentId: quote?.id,
    });

    if (ok) {
      addToQueue(
        {
          title: "短视频评论",
          url: shortVideoCommentPublishUrl,
          params: {
            targetId: shortVideo.id!,
            content: data.text,
            ats: data.ats,
            voice: data.voice,
            resources: data.resource,
            topId: quote?.id,
            parentId: quote?.id,
          },
        },
        () => {
          handleRefresh();
        }
      );
    }
  };

  const handleSetQuote = (quote: CommonCommentResult) => {
    setQuote({
      id: quote.id!,
      content: `回复：${quote?.user?.nickname} “${quote.voice ? "[语音]" : ""}${
        (quote.resources ?? []).some((r) => r.duration) ? "[视频]" : ""
      }${(quote.resources ?? []).some((r) => !r.duration) ? "[图片]" : ""}${
        quote.content
      }”`,
    });
  };

  return (
    <Popup
      destroyOnClose
      visible={open}
      bodyClassName="rounded-t-[10px] bg-app-bar-background"
      onMaskClick={onClose}
      onClose={onClose}
    >
      <div className="flex flex-col h-[500px]">
        <div className="py-[15px] text-center">
          {shortVideo.commentNumber}条评论
        </div>
        <div className="flex-1 h-0 overflow-y-auto px-[15px]">
          <PullToRefresh onRefresh={handleRefresh}>
            <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
              {(data) => {
                return data.map((item, index) => {
                  return (
                    <Fragment key={index}>
                      <CommentItem
                        id={shortVideo.id!}
                        type="short-video"
                        data={item}
                        onSetQuote={handleSetQuote}
                      />
                      {index !== data.length - 1 ? (
                        <Divider className="border-divider" />
                      ) : null}
                    </Fragment>
                  );
                });
              }}
            </ScrollLoadData>
          </PullToRefresh>
        </div>
        <CommentInput
          showGift={false}
          quote={quote}
          onClearQuote={() => setQuote(undefined)}
          onFinish={handleComment}
        />
      </div>
    </Popup>
  );
};
