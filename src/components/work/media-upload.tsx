import { BaseMediaUpload } from "../base-media-upload";
import { AspectRatio } from "@/ui/aspect-ratio";
import { UploadFile } from "./upload-file";

interface Props {
  maxCount?: number;
  accept?: "image/*" | "video/*" | "image/*,video/*";
  value: File[];
  onChange: (files: File[]) => void;
}

export const MediaUpload = (props: Props) => {
  const { maxCount = 9, accept, value, onChange } = props;

  const canUploadCount = maxCount - (value?.length ?? 0);

  const handleUpload = (files: File[]) => {
    onChange?.([...(value ?? []), ...files]);
  };

  const handleDelete = (index: number) => {
    onChange?.(value?.filter((_, i) => i !== index));
  };

  return (
    <div className="flex-1">
      <div className="grid grid-cols-3 gap-[15px]">
        {value?.length
          ? value.map((file, index) => (
              <AspectRatio key={index} ratio={1}>
                <UploadFile file={file} onDelete={() => handleDelete(index)} />
              </AspectRatio>
            ))
          : null}
        {canUploadCount > 0 ? (
          <AspectRatio ratio={1}>
            <BaseMediaUpload
              accept={accept}
              className="w-full h-full"
              maxCount={canUploadCount}
              onSelected={handleUpload}
            />
          </AspectRatio>
        ) : null}
      </div>
    </div>
  );
};
