import { PosterShareData } from "@/type/poster-share-data";
import { Dialog, Divider, Image, Toast } from "antd-mobile";
import { DefaultLoadImage } from "../default-load-image";
import { CommonIcon } from "../common-icon";
import { But<PERSON> } from "@/ui/button";
import { domToPng } from "modern-screenshot";
import { CloseIcon } from "../svg-icon";
import { useEffect, useRef, useState } from "react";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useConfigList } from "@/hooks/use-config-list";
import "./poster-share-dialog.less";

interface Props {
  data: PosterShareData;
}

export const PosterShareDialog = (props: Props) => {
  const { data } = props;
  const { targetTypeLabel, cover, title, desc, qrcode, tail } = data;

  const [visible, setVisible] = useState(true);

  const { data: userInfo } = useFullUserInfo();
  const { getSystemConfig } = useConfigList();

  const posterRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setVisible(true);
  }, [data]);

  const onClose = () => {
    setVisible(false);
  };

  const handleSave = () => {
    if (!posterRef.current) {
      return;
    }

    Toast.show({
      icon: "loading",
      content: "请稍候...",
      duration: 0,
    });
    domToPng(posterRef.current).then((dataUrl) => {
      const link = document.createElement("a");
      link.href = dataUrl;
      link.download = "海报.png";
      link.click(); // 模拟点击下载
      Toast.clear();
    });
  };

  return (
    <Dialog
      visible={visible}
      className="poster-share-dialog"
      closeOnMaskClick
      destroyOnClose
      title={null}
      onClose={onClose}
      content={
        <div className="flex flex-col gap-[15px] relative">
          <div className="rounded-[10px] p-5 bg-white" ref={posterRef}>
            <div className="flex flex-col items-center">
              <span className="text-[15px] text-black">
                {userInfo?.nickname}分享的{targetTypeLabel}
              </span>
              {cover ? (
                <div className="pt-3 w-[273px] h-[273px]">
                  <Image
                    src={cover}
                    width="100%"
                    height="100%"
                    fit="cover"
                    className="rounded-[5px]"
                    placeholder={<DefaultLoadImage />}
                    fallback={<DefaultLoadImage />}
                  />
                </div>
              ) : null}
              <div className="pt-3 self-start">
                <span className="text-base text-black line-clamp-1">
                  {title}
                </span>
              </div>
              <div className="pt-[5px] self-start">
                <span className="text-[15px] text-black line-clamp-2">
                  {desc}
                </span>
              </div>
              <Divider className="border-[#EEEE] w-full" />
              <div className="h-[75px] flex gap-[10px] items-center">
                <CommonIcon name="logo" w={50} h={50} />
                <div className="flex-1 flex flex-col">
                  <span className="text-[15px] text-black">
                    {getSystemConfig("APP_SHOW_NAME")}
                  </span>
                  <span className="text-sm text-black">扫描二维码查看分享</span>
                </div>
                <Image
                  src={qrcode}
                  width={75}
                  height={75}
                  placeholder={<DefaultLoadImage />}
                  fallback={<DefaultLoadImage />}
                />
              </div>
              <div className="pt-[10px]">
                <span className="text-sm text-[#ccc]">{tail}</span>
              </div>
            </div>
          </div>
          <div>
            <Button
              variant="primary"
              size="lg"
              className="w-full"
              onClick={handleSave}
            >
              保存到相册
            </Button>
          </div>
          <div
            className="absolute right-[10px] top-[10px] text-black text-[28px]"
            onClick={() => onClose()}
          >
            <CloseIcon />
          </div>
        </div>
      }
    />
  );
};
