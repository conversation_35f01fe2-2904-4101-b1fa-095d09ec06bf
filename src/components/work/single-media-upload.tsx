import { Image } from "antd-mobile";
import { CloseIcon, PlayIcon } from "../svg-icon";
import { formatDuration } from "@/utils/format-duration";
import { getVideoDuration, isVideo } from "@/utils/common";
import { useEffect, useState } from "react";
import { BaseMediaUpload } from "../base-media-upload";
import classNames from "classnames";

interface Props {
  accept?: "image/*" | "video/*" | "image/*,video/*";
  value?: File;
  className?: string;
  placeholder?: React.ReactNode;
  onChange: (file?: File) => void;
}

export const SingleMediaUpload = (props: Props) => {
  const { accept, value, className, placeholder, onChange } = props;

  const canUploadCount = !value ? 1 : 0;

  const handleUpload = (file: File) => {
    onChange?.(file);
  };

  const handleDelete = () => {
    onChange?.(undefined);
  };

  return (
    <div className={classNames("flex-1", className)}>
      {value ? <UploadFile file={value} onDelete={handleDelete} /> : null}
      {canUploadCount > 0 ? (
        <BaseMediaUpload
          accept={accept}
          className="w-full h-full"
          maxCount={canUploadCount}
          onSelected={(files) => handleUpload(files[0])}
          placeholder={placeholder}
        />
      ) : null}
    </div>
  );
};

const UploadFile = ({
  file,
  onDelete,
}: {
  file: File;
  onDelete: () => void;
}) => {
  const [duration, setDuration] = useState<string>("-:-");
  const [thumbnailUrl, setThumbnailUrl] = useState<string>("");

  useEffect(() => {
    if (isVideo(file)) {
      getVideoDuration(file).then((duration) => {
        setDuration(formatDuration(duration));
      });

      // 获取视频缩略图
      const video = document.createElement("video");
      const canvas = document.createElement("canvas");
      video.src = URL.createObjectURL(file);

      video.onloadeddata = () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        const ctx = canvas.getContext("2d");
        ctx?.drawImage(video, 0, 0, canvas.width, canvas.height);
        setThumbnailUrl(canvas.toDataURL("image/jpeg"));
        URL.revokeObjectURL(video.src); // 清理 URL
      };
    }
  }, [file]);

  return (
    <div className="relative w-full h-full">
      <div className="relative overflow-hidden rounded-[5px] w-full h-full">
        <Image
          src={
            isVideo(file)
              ? thumbnailUrl || URL.createObjectURL(file)
              : URL.createObjectURL(file)
          }
          width="100%"
          height="100%"
          fit="cover"
          className="rounded-[5px]"
          onLoad={() => {}}
        />
        {isVideo(file) ? (
          <div className="flex absolute top-0 left-0 w-full h-full justify-center items-center">
            <PlayIcon size={30} className="text-white/50 rounded-full" />
            <div className="text-white absolute bottom-[3px] right-[5px]">
              {duration}
            </div>
          </div>
        ) : null}
      </div>
      <div className="absolute top-[-5px] right-[-5px] z-10">
        <span onClick={onDelete}>
          <CloseIcon className="text-white bg-[#FF005C] rounded-full" />
        </span>
      </div>
    </div>
  );
};
