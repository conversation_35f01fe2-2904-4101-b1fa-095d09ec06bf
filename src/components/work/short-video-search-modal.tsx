import { shortVideoTagList } from "@/lib/api/video";
import { PageParams } from "@/type";
import { Input } from "@/ui/input";
import { Popup } from "antd-mobile";
import { forwardRef } from "react";
import { useImperativeHandle } from "react";
import { useState } from "react";
import { ScrollLoadData } from "../scroll-load-data";
import { Button } from "@/ui/button";

export interface ShortVideoSearchModalRef {
  open: () => void;
  close: () => void;
}

interface Props {
  onSelect: (name: string) => void;
}

export const ShortVideoSearchModal = forwardRef<
  ShortVideoSearchModalRef,
  Props
>(({ onSelect }, ref) => {
  const [open, setOpen] = useState(false);

  const [name, setName] = useState("");

  const queryKey = ["short-video-tag-list", name];

  const queryFn = (params: PageParams) => shortVideoTagList({ name }, params);

  const onOpen = () => {
    setName("");
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const handleSelect = (name: string) => {
    onSelect(name);
    onClose();
  };

  useImperativeHandle(ref, () => ({
    open: onOpen,
    close: onClose,
  }));

  return (
    <Popup
      destroyOnClose
      visible={open}
      bodyClassName="rounded-t-[10px]"
      onMaskClick={onClose}
      onClose={onClose}
    >
      <div className="h-[450px] flex flex-col gap-[15px] bg-app-bar-background p-[15px]">
        <div className="text-center text-base">标签搜索</div>
        <div>
          <Input
            className="border-none bg-scaffold-background rounded px-[15px]"
            placeholder="请输入标签名"
            value={name}
            onChange={(e) => setName(e.target.value)}
          />
        </div>
        <div className="flex-1 h-0 overflow-y-auto">
          <ScrollLoadData
            queryKey={queryKey}
            queryFn={queryFn}
            size={20}
            emptyElement={
              name.trim() ? (
                <div>
                  <Button
                    variant="secondary"
                    className="w-full"
                    onClick={() => handleSelect(name.trim())}
                  >
                    使用标签“{name.trim()}”
                  </Button>
                </div>
              ) : undefined
            }
          >
            {(data) => {
              return (
                <div className="flex flex-col">
                  {data.map((item) => {
                    return (
                      <div
                        className="px-[15px] py-[10px] text-[15px]"
                        key={item.id}
                        onClick={() => handleSelect(item.name!)}
                      >
                        #{item.name}#
                      </div>
                    );
                  })}
                </div>
              );
            }}
          </ScrollLoadData>
        </div>
      </div>
    </Popup>
  );
});
