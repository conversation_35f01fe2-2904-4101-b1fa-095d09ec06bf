import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { LiveRoomDetailedResult } from "@/type/live-room-detailed-result";
import { User } from "@/type/user";
import { CustomNodeDataType } from "@/utils/constant";
import { LiveNodeDataType } from "@/utils/enums";
import {
  StoreName,
  TUIGroupService,
  TUIStore,
  IMessageModel,
} from "@tencentcloud/chat-uikit-engine";
import { useCallback, useEffect, useState } from "react";
import { useQueue } from "./use-queue";
import { useGiftQueue } from "./use-gift-queue";
import { useSystemMounts } from "@/hooks/use-system-mounts";
import { useGiftList } from "@/hooks/use-gift-list";
import { useSystemJoinSpecial } from "@/hooks/use-system-join-special";
import { queryClient } from "@/provider/query-client";
import { useMutation } from "@tanstack/react-query";
import { exitLiveRoom } from "@/lib/api/live";

export interface MessageData {
  uuid: string;
  liveType: LiveNodeDataType;
  user?: User;
  id?: string;
  content?: string;
  text?: string;
  image?: string;
  number?: string;
}

export interface MessageParams {
  members: {
    number: number;
    list: User[];
  };
}

export interface FullAnimationData {
  uuid: string;
  path: string;
}

export interface JoinSpecialAnimationData {
  uuid: string;
  path: string;
  id: string;
  user?: User;
  desc?: string;
}

export const useMessage = (
  queryKey: string[],
  detail: LiveRoomDetailedResult | undefined,
  cb: (fn: MessageParams) => void
) => {
  const [messageList, setMessageList] = useState<MessageData[]>([]);

  const { data: userInfo } = useFullUserInfo();
  const { navigateBack } = useNavigateRoute();
  const { list: mountsList } = useSystemMounts();
  const { list: joinSpecicalList } = useSystemJoinSpecial();
  const { data: giftList } = useGiftList();

  const { mutateAsync: exitRoom } = useMutation({
    mutationFn: exitLiveRoom,
  });

  const {
    addQueue: addInstantReminderQueue,
    removeQueue: removeInstantReminderQueue,
    current: instantReminderCurrent,
  } = useQueue<MessageData>();

  // 礼物消息每次分为单独的队列，最多存储 2 条。意味着最多同时显示 2 条礼物消息
  const {
    addQueue: addGiftQueue,
    removeQueue: removeGiftQueue,
    current: giftCurrent,
  } = useGiftQueue();

  const {
    addQueue: addFullAnimationQueue,
    removeQueue: removeFullAnimationQueue,
    current: fullAnimationCurrent,
  } = useQueue<FullAnimationData>();

  const {
    addQueue: addJoinSpecialAnimationQueue,
    removeQueue: removeJoinSpecialAnimationQueue,
    current: joinSpecialAnimationCurrent,
  } = useQueue<JoinSpecialAnimationData>();

  const onNewMessage = useCallback(
    (message: IMessageModel) => {
      const { payload, ID } = message;

      try {
        const data: any = JSON.parse(payload?.data);

        if (data.type !== CustomNodeDataType.Live) {
          return;
        }

        if (data.liveType === LiveNodeDataType.memberChange) {
          // 成员变更
          cb({
            members: {
              number: data.memberNumber,
              list: data.members,
            },
          });

          return;
        }

        if (data.liveType === LiveNodeDataType.killUser) {
          // 踢出直播间
          if (data.userId === userInfo?.id) {
            navigateBack();
          }

          return;
        }

        if (data.liveType === LiveNodeDataType.closed) {
          exitRoom({ id: detail?.id! });

          queryClient.fetchQuery({
            queryKey,
          });
          return;
        }

        if (data.liveType === LiveNodeDataType.mounts) {
          const targetMounts = mountsList.find(
            (mounts) => mounts.id === data.id
          );

          if (targetMounts) {
            addFullAnimationQueue({
              uuid: ID,
              path: targetMounts.animationResource!,
            });
          }

          return;
        }

        if (data.liveType === LiveNodeDataType.joinSpecial) {
          const targetJoinSpecical = joinSpecicalList.find(
            (joinSpecical) => joinSpecical.id === data.id
          );

          if (targetJoinSpecical?.animationResource) {
            addJoinSpecialAnimationQueue({
              uuid: ID,
              id: data.id,
              path: targetJoinSpecical.animationResource,
              user: data.user,
              desc: data.desc,
            });
          }
          return;
        }

        if (data.liveType === LiveNodeDataType.gift) {
          addGiftQueue({
            uuid: ID,
            id: data.id,
            content: data.content,
            image: data.image,
            number: data.number,
            user: data.user,
          });

          const targetGift = giftList?.find((gift) => gift.id === data.id);

          if (targetGift?.animation) {
            Array.from({ length: data.number || 1 }).forEach((_, i) => {
              addFullAnimationQueue({
                uuid: `${ID}_${i}`,
                path: targetGift.animation!,
              });
            });
          }
        }

        if (
          data.liveType === LiveNodeDataType.chargeChange &&
          detail?.user?.id !== userInfo?.id
        ) {
          // 主播不需要处理
        }

        if (data.instantReminder && detail?.user?.id !== userInfo?.id) {
          addInstantReminderQueue(data);
          return;
        }

        setMessageList((prev) => {
          if (prev.length >= 500) {
            return [...prev.slice(1), { ...data, uuid: ID }];
          }
          return [...prev, { ...data, uuid: ID }];
        });
      } catch {
        //
      }
    },
    [
      detail?.user?.id,
      detail?.id,
      userInfo?.id,
      cb,
      navigateBack,
      exitRoom,
      queryKey,
      mountsList,
      addFullAnimationQueue,
      joinSpecicalList,
      addJoinSpecialAnimationQueue,
      addGiftQueue,
      giftList,
      addInstantReminderQueue,
    ]
  );

  const onNewMessageList = useCallback(
    (list: IMessageModel[]) => {
      list.forEach((item) => {
        onNewMessage(item);
      });
    },
    [onNewMessage]
  );

  useEffect(() => {
    let isJoin = false;

    if (detail) {
      TUIGroupService.joinGroup({
        groupID: detail.groupId!,
        applyMessage: "join live group",
      }).then(() => {
        isJoin = true;

        TUIStore.watch(StoreName.CHAT, {
          newMessageList: onNewMessageList,
        });
      });
    }

    return () => {
      if (detail?.groupId && isJoin) {
        TUIGroupService.quitGroup(detail.groupId);
      }

      TUIStore.unwatch(StoreName.CHAT, {
        newMessageList: onNewMessageList,
      });
    };
  }, [detail, onNewMessageList]);

  return {
    messageList,
    setMessageList,
    instantReminderCurrent,
    removeInstantReminderQueue,
    addGiftQueue,
    giftCurrent,
    removeGiftQueue,
    removeFullAnimationQueue,
    fullAnimationCurrent,
    removeJoinSpecialAnimationQueue,
    joinSpecialAnimationCurrent,
  };
};
