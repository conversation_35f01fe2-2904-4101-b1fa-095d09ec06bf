import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { exitLiveRoom, joinLiveRoom, LiveRoomSendGift } from "@/lib/api/live";
import { useMutation, useQuery } from "@tanstack/react-query";
import { LiveClosed } from "./components/live-closed";
import {
  MouseEvent,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { LiveRoomSimpleResult } from "@/type/live_room_simple_result";
import {
  Image as ImageAntd,
  ProgressCircle,
  SafeArea,
  TextArea,
  TextAreaRef,
  Toast,
} from "antd-mobile";
import { liveBackground } from "@/components/image-list";
import {
  GiftType,
  LiveNodeDataType,
  LiveRoomType,
  ShareTargetType,
} from "@/utils/enums";
import { LivePlayer, LivePlayerRef } from "./components/live-player";
import { Button } from "@/ui/button";
import { follow, unfollow } from "@/lib/api/user";
import { UserAvatar } from "@/components/user-avatar";
import { UserInfoModal, UserInfoModalRef } from "../user-info-modal";
import { LiveLayoutMembersModal } from "@/pages/live-player/components/live-layout-members-modal";
import { CloseIcon, ShareIcon } from "@/components/svg-icon";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { MessageParams, useMessage } from "./use-message";
import { isNil } from "lodash-es";
import { CommonIcon } from "@/components/common-icon";
import { useModal } from "@/hooks/use-modal";
import { LiveGiftModal } from "../live-gift-modal";
import { UserBackpackGiftResult } from "@/type/user-backpack-gift-result";
import { GiftResult } from "@/type/gift-result";
import Batter from "@/components/batter";
import { DefaultLoadImage } from "@/components/default-load-image";
import { useGiftList } from "@/hooks/use-gift-list";
import { ShareModal } from "../share-modal";
import { MessageItem } from "./components/message-item";
import InstantReminderMessage from "./components/instant-reminder-message";
import { GiftMessage } from "./components/gift-message";
import { ScrollArea } from "@/ui/scroll-area";
import { FullAnimation } from "./components/full-animation";
import { JoinSpecialAnimation } from "./components/join-special-animation";
import { LivePreview, LivePreviewRef } from "./components/live-preview";
import { LiveRoomDetailedResult } from "@/type/live-room-detailed-result";
import { v4 as uuidv4 } from "uuid";

interface Props {
  id: string;
  type?: LiveRoomType;
  data?: LiveRoomSimpleResult;
}

export default function LivePlayerCom(props: Props) {
  const { id, type: propsType, data } = props;

  const [closedNote, setClosedNote] = useState("");
  const [closedBackground, setClosedBackground] = useState("");
  const [inputting, setInputting] = useState(false);
  const [inputMessage, setInputMessage] = useState("");

  const [detail, setDetail] = useState<LiveRoomDetailedResult>();

  const userInfoModalRef = useRef<UserInfoModalRef>(null);
  const textareaRef = useRef<TextAreaRef>(null);
  const sendClickedRef = useRef(false);
  const livePreviewRef = useRef<LivePreviewRef>(null);
  const livePlayerRef = useRef<LivePlayerRef>(null);

  const { navigateBack } = useNavigateRoute();

  const { data: userInfo } = useFullUserInfo();

  const { getByType } = useGiftList();

  const firstGift = getByType(GiftType.Live)?.[0];

  const {
    open: openGift,
    openModal: openGiftModal,
    closeModal: closeGiftModal,
  } = useModal();

  const queryKey = useMemo(() => {
    return ["live-room", id];
  }, [id]);

  const { isLoading: isLoadingDetail, data: roomDetail } = useQuery({
    queryKey,
    queryFn: () => joinLiveRoom(id!),
    enabled: !!id,
  });

  useEffect(() => {
    if (roomDetail) {
      setDetail(roomDetail.data);
    }
  }, [roomDetail]);

  const { mutateAsync: exitRoom } = useMutation({
    mutationFn: exitLiveRoom,
  });

  const type = detail?.type ?? propsType;

  const messageCb = useCallback((params: MessageParams) => {
    const { members } = params;

    if (!isNil(members)) {
      setDetail((prev) => {
        if (prev) {
          return {
            ...prev,
            members: members.list,
            memberNumber: members.number,
          };
        }
      });
    }
  }, []);

  const {
    messageList,
    setMessageList,
    instantReminderCurrent,
    removeInstantReminderQueue,
    giftCurrent,
    removeGiftQueue,
    fullAnimationCurrent,
    removeFullAnimationQueue,
    joinSpecialAnimationCurrent,
    removeJoinSpecialAnimationQueue,
  } = useMessage(queryKey, detail, messageCb);

  const bottomRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 每次消息列表变化时滚动到底部
    bottomRef.current?.scrollTo({
      top: bottomRef.current?.scrollHeight,
      behavior: "smooth",
    });
  }, [messageList]);

  useEffect(() => {
    if (roomDetail?.data?.tips) {
      setMessageList(
        roomDetail.data.tips.map((tip) => ({
          uuid: uuidv4(),
          liveType: LiveNodeDataType.tips,
          content: tip,
        }))
      );
    }

    return () => {
      if (roomDetail?.data) {
        exitRoom({ id });
      }
    };
  }, [id, roomDetail, exitRoom, setMessageList]);

  const joinParams = useMemo(() => {
    const { token, id, playUrl, cover } = roomDetail?.data ?? {};

    if (token && id && userInfo?.userid) {
      return {
        token,
        channel: id,
        uid: userInfo.userid,
        hlsUrl: playUrl?.hls,
        poster: cover,
      };
    }
  }, [roomDetail, userInfo?.userid]);

  const followMutation = useMutation({
    mutationFn: follow,
  });
  const unfollowMutation = useMutation({
    mutationFn: unfollow,
  });

  const {
    open: openShare,
    openModal: openShareModal,
    closeModal: closeShareModal,
  } = useModal();

  const handleFollowChange = useCallback(
    async (e: MouseEvent) => {
      e.stopPropagation();

      Toast.show({
        icon: "loading",
        content: "加载中...",
        duration: 0,
      });

      const mutation = detail?.user?.hasFollow
        ? unfollowMutation
        : followMutation;
      const { ok } = await mutation.mutateAsync(detail?.user?.id!);

      if (ok) {
        setDetail((prev) => {
          if (prev) {
            return {
              ...prev,
              user: { ...prev.user, hasFollow: !prev.user?.hasFollow },
            };
          }
        });
      }
    },
    [detail?.user, followMutation, unfollowMutation]
  );

  const { mutateAsync: sendGift } = useMutation({
    mutationFn: LiveRoomSendGift,
  });

  const handleSendGift = async (
    gift: GiftResult | undefined,
    backpackGift: UserBackpackGiftResult | undefined,
    number: number
  ) => {
    sendGift({
      id,
      userId: detail?.user?.id!,
      giftId: gift?.id,
      targetId: backpackGift?.targetId,
      number,
    });
  };

  const handleInputMessage = () => {
    if (detail?.disabledChat) {
      Toast.show({
        content: detail.disabledChatTips ?? "聊天功能不可用",
      });

      return;
    }

    setInputting(true);
  };

  useEffect(() => {
    if (inputting) {
      textareaRef.current?.focus();
    }
  }, [inputting]);

  useEffect(() => {
    if (detail?.hasBuy === false) {
      livePreviewRef.current?.startPreview(
        detail?.chargeType!,
        detail?.price!,
        detail?.allowPreview || false,
        detail?.maxPreviewTime || 0
      );
    }
  }, [
    detail?.allowPreview,
    detail?.chargeType,
    detail?.hasBuy,
    detail?.maxPreviewTime,
    detail?.price,
  ]);

  useEffect(() => {
    if (!isLoadingDetail) {
      if (!roomDetail?.ok) {
        setClosedNote(roomDetail?.msg || "直播间加载失败");
        setClosedBackground(data?.cover ?? userInfo?.avatarBigImageUrl!);
      }
    }
  }, [isLoadingDetail, roomDetail, data?.cover, userInfo?.avatarBigImageUrl]);

  if (closedNote && closedBackground) {
    return (
      <div className="w-full h-full relative">
        <LiveClosed
          background={userInfo?.avatarBigImageUrl!}
          note="直播已结束"
        />
      </div>
    );
  }

  return (
    <div className="w-full h-full relative z-10">
      <div className="w-full h-full relative">
        <div className="absolute top-0 left-0 w-full h-full z-[-1]">
          <ImageAntd
            width="100%"
            height="100%"
            src={liveBackground}
            fit="cover"
          />
        </div>
        {isLoadingDetail ? (
          <div className="w-full h-full flex items-center justify-center">
            <span>直播画面加载中</span>
          </div>
        ) : null}
        {type === LiveRoomType.视频 && joinParams ? (
          <LivePlayer ref={livePlayerRef} {...joinParams} />
        ) : null}
        {type && type !== LiveRoomType.视频 ? (
          <div className="w-full h-full flex items-center justify-center">
            <span>暂不支持该类型直播，请升级客户端版本</span>
          </div>
        ) : null}
        {detail ? (
          <>
            {/* 顶部 */}
            <div className="absolute z-20 top-0 w-full flex justify-between gap-[10px] px-[15px] pt-[15px]">
              <div
                className="h-8 bg-black/30 rounded-[16px] flex items-center pl-[1px] pr-1"
                onClick={() => {
                  userInfoModalRef.current?.open(detail?.user?.id!);
                }}
              >
                <div className="w-[29px] h-[29px] flex-none">
                  <ImageAntd
                    src={detail?.user?.avatarBigImageUrl}
                    width="100%"
                    height="100%"
                    fit="cover"
                    className="rounded-[29px]"
                  />
                </div>
                <div className="flex flex-col px-3">
                  <span className="line-clamp-1 text-[13px] text-white">
                    {detail?.user?.nickname}
                  </span>
                  <span
                    className="text-[20px] text-white"
                    style={{ zoom: 0.5 }}
                  >
                    ID: {detail?.user?.userid}
                  </span>
                </div>
                {!detail?.user?.hasFollow ? (
                  <Button
                    variant="primary"
                    className="w-[44px] h-[24px] text-[13px]"
                    onClick={handleFollowChange}
                  >
                    关注
                  </Button>
                ) : null}
              </div>
              <div>
                <div className="flex gap-[2px] items-center">
                  {detail?.members?.map((item) => (
                    <UserAvatar
                      key={item.id}
                      src={item.avatarBigImageUrl!}
                      isVip={item.vip}
                      size={32}
                      official={item.official}
                      anchor={item.liveAnchor}
                      onClick={() => {
                        userInfoModalRef.current?.open(item.id!);
                      }}
                    />
                  ))}
                  <LiveLayoutMembersModal id={id}>
                    <div className="flex justify-center items-center text-[13px] h-[30px] min-w-[30px] bg-black/30 rounded-[30px]">
                      <span className="px-[5px]">{detail?.memberNumber}</span>
                    </div>
                  </LiveLayoutMembersModal>
                  <div
                    onClick={() => {
                      navigateBack();
                    }}
                  >
                    <CloseIcon size={24} />
                  </div>
                </div>
              </div>
            </div>
            {!detail?.hasBuy ? (
              <div className="absolute z-20 left-[15px] top-[55px]">
                <LivePreview
                  ref={livePreviewRef}
                  visible
                  id={id}
                  setDetail={setDetail}
                  pausePlay={() => {
                    livePlayerRef.current?.pause();
                  }}
                  resumePlay={() => {
                    livePlayerRef.current?.play();
                  }}
                />
              </div>
            ) : null}
            {/* 底部 */}
            <div className="absolute z-20 bottom-0 w-full">
              <div className="flex flex-col gap-[5px] px-[15px]">
                <div className="flex flex-col gap-[5px]">
                  {giftCurrent.map((item, index) => {
                    if (!item) {
                      return null;
                    }

                    return (
                      <GiftMessage
                        key={item.uuid}
                        gift={item}
                        onFinish={() => {
                          removeGiftQueue[index]();
                        }}
                      />
                    );
                  })}
                </div>
                <ScrollArea
                  ref={bottomRef}
                  className="[&>[data-radix-scroll-area-viewport]]:max-h-[200px]"
                >
                  <div className="flex flex-col items-start gap-[5px]">
                    {messageList.map((item, index) => {
                      return (
                        <MessageItem
                          key={index}
                          anchor={detail?.user!}
                          message={item}
                        />
                      );
                    })}
                  </div>
                </ScrollArea>
                {instantReminderCurrent ? (
                  <div className="h-[35px] flex items-center">
                    <InstantReminderMessage
                      key={instantReminderCurrent.uuid}
                      anchor={detail?.user!}
                      message={instantReminderCurrent}
                      onFinish={() => {
                        removeInstantReminderQueue();
                      }}
                    />
                  </div>
                ) : null}
              </div>
              <div
                className="flex gap-[9px] p-[10px]"
                style={{
                  display: inputting ? "none" : "flex",
                }}
              >
                <div
                  className="flex-1 px-[17px] h-[39px] rounded-[19px] bg-black/30 text-sm flex items-center"
                  onClick={() => {
                    sendClickedRef.current = false;
                    handleInputMessage();
                  }}
                >
                  {detail.disabledChatTips ?? "说点什么吧..."}
                </div>
                {firstGift ? (
                  <Batter
                    builder={(current, maxWait, number) => (
                      <div
                        className="flex justify-center items-center relative"
                        style={{ width: "37px", height: "37px" }}
                      >
                        {/* 内容圆形区域 */}
                        <div
                          className="flex flex-col justify-center items-center relative rounded-full bg-gradient-to-br from-[#FF3460] to-[#FF7B57]"
                          style={{
                            width: "37px",
                            height: "37px",
                          }}
                        >
                          {/* 连击数字 */}
                          <div className="flex items-center text-white">
                            <span className="text-[15px] font-bold">
                              x{number}
                            </span>
                          </div>
                        </div>

                        {/* 进度条 */}
                        <div className="absolute top-0 left-0 w-full h-full flex justify-center items-center">
                          <ProgressCircle
                            percent={(current / maxWait) * 100}
                            style={{
                              "--fill-color": "#fff",
                              "--track-color": "transparent",
                              "--size": "38px",
                              "--track-width": "2px",
                            }}
                          />
                        </div>
                      </div>
                    )}
                    onFinish={(number) => {
                      handleSendGift(firstGift, undefined, number);
                    }}
                  >
                    <div className="w-[38px] h-[38px] bg-black/30 rounded-[38px] flex items-center justify-center">
                      <ImageAntd
                        src={firstGift.image}
                        width="20px"
                        height="20px"
                        fit="contain"
                        placeholder={<DefaultLoadImage />}
                        fallback={<DefaultLoadImage />}
                      />
                    </div>
                  </Batter>
                ) : null}
                <div
                  className="w-[38px] h-[38px] bg-black/30 rounded-[38px] flex items-center justify-center"
                  onClick={() => {
                    openGiftModal();
                  }}
                >
                  <CommonIcon name="gift" w={20} h={21} />
                </div>
                <div
                  className="w-[38px] h-[38px] bg-black/30 rounded-[38px] flex items-center justify-center"
                  onClick={() => {
                    openShareModal();
                  }}
                >
                  <ShareIcon size={17} />
                </div>
              </div>
              <div
                className="p-[10px] bg-scaffold-background gap-[10px] items-end"
                style={{
                  display: inputting ? "flex" : "none",
                }}
              >
                <div className="py-[5px] bg-app-bar-background rounded-[19px] w-full">
                  <TextArea
                    className="border-none px-[10px] py-0 resize-none"
                    placeholder="善语结善缘，恶语伤人心"
                    style={{
                      "--color": "#fff",
                      "--font-size": "14px",
                    }}
                    rows={1}
                    autoSize={{
                      minRows: 1,
                      maxRows: 3,
                    }}
                    ref={textareaRef}
                    value={inputMessage}
                    onChange={(value) => {
                      setInputMessage(value);
                    }}
                    onBlur={() => {
                      if (sendClickedRef.current) {
                        sendClickedRef.current = false;
                        return;
                      }

                      setInputting(false);
                      setInputMessage("");
                    }}
                  />
                </div>
                {inputMessage.trim().length ? (
                  <Button
                    variant="primary"
                    className="h-[31px] w-[75px] text-sm"
                    onMouseDown={() => {
                      sendClickedRef.current = true;
                    }}
                    onClick={() => {
                      console.log("msg", inputMessage);
                      setInputting(false);
                      setInputMessage("");
                    }}
                  >
                    发送
                  </Button>
                ) : null}
              </div>
              <SafeArea position="bottom" />
            </div>
            <UserInfoModal ref={userInfoModalRef} />
            <LiveGiftModal
              open={openGift}
              onClose={closeGiftModal}
              onSend={handleSendGift}
            />
            <ShareModal
              type={ShareTargetType.Live}
              id={id!}
              open={openShare}
              onClose={closeShareModal}
            />
          </>
        ) : null}
      </div>
      {fullAnimationCurrent ? (
        <FullAnimation
          data={fullAnimationCurrent}
          removeQueue={removeFullAnimationQueue}
        />
      ) : null}

      {joinSpecialAnimationCurrent ? (
        <JoinSpecialAnimation
          data={joinSpecialAnimationCurrent}
          removeQueue={removeJoinSpecialAnimationQueue}
        />
      ) : null}
    </div>
  );
}
