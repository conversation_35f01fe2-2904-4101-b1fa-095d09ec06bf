import { useCallback, useState } from "react";

export const useQueue = <T>(maxSize?: number) => {
  const [queue, setQueue] = useState<T[]>([]);

  /**
   * 添加消息到队列
   */
  const addQueue = useCallback(
    (item: T) => {
      setQueue((prev) => {
        if (maxSize !== undefined && prev.length >= maxSize) {
          // 移除最早的元素（第一个元素），然后添加新元素
          return [...prev.slice(1), item];
        }
        return [...prev, item];
      });
    },
    [maxSize]
  );

  /**
   * 获取并移除队首消息
   */
  const removeQueue = useCallback(() => {
    if (queue.length === 0) return null;

    const first = queue[0];
    setQueue((prev) => prev.slice(1));
    return first;
  }, [queue]);

  /**
   * 清空队列
   */
  const clear = useCallback(() => {
    setQueue([]);
  }, []);

  return {
    queue,
    addQueue,
    removeQueue,
    clear,
    size: queue.length,
    current: queue[0] || null,
  };
};
