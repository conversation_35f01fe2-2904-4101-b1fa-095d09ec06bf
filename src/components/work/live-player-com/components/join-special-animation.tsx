import { useEffect, useRef } from "react";
import { JoinSpecialAnimationData } from "../use-message";
import { DB, Parser, Player } from "svga";

interface Props {
  data: JoinSpecialAnimationData;
  removeQueue: () => void;
}

export const JoinSpecialAnimation = (props: Props) => {
  const { data, removeQueue } = props;

  const joinSpecialAnimationRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const { path } = data;
    let player: Player | null = null;

    const paySvga = async () => {
      const db = new DB();
      let svga = await db.find(path);

      if (!svga) {
        // Parser 需要配置取消使用 ImageBitmap 特性，ImageBitmap 数据无法直接存储到 DB 内
        const parser = new Parser({ isDisableImageBitmapShim: true });
        svga = await parser.load(path);
        await db.insert(path, svga);
      }

      // 创建离屏 canvas
      const fontCanvas = document.createElement("canvas");
      const fontContext = fontCanvas.getContext("2d")!;

      // 设置 canvas 尺寸
      fontCanvas.width = 300;
      fontCanvas.height = 32;

      // 设置文本样式
      fontContext.font = "32px Arial";
      fontContext.textAlign = "center";
      fontContext.textBaseline = "middle";
      fontContext.fillStyle = "#fff";

      // 绘制文本
      fontContext.fillText(
        data.user?.nickname ?? "",
        fontCanvas.width / 2,
        fontCanvas.height / 2
      );

      svga.replaceElements["01"] = fontCanvas;

      // 创建离屏 canvas
      const fontCanvas1 = document.createElement("canvas");
      const fontContext1 = fontCanvas1.getContext("2d")!;

      // 设置 canvas 尺寸
      fontCanvas1.width = 300; // 根据实际需要设置宽度
      fontCanvas1.height = 24;

      // 设置文本样式
      fontContext1.font = "24px Arial";
      fontContext1.textAlign = "center";
      fontContext1.textBaseline = "middle";
      fontContext1.fillStyle = "#fff";

      // 绘制文本
      fontContext1.fillText(
        data.desc ?? "",
        fontCanvas1.width / 2,
        fontCanvas1.height / 2
      );
      svga.replaceElements["02"] = fontCanvas1;

      const image = new Image();
      image.src = data.user?.avatarBigImageUrl ?? "";

      svga.replaceElements["03"] = image;

      player = new Player({
        container: joinSpecialAnimationRef.current!,
        loop: 1,
      });

      await player.mount(svga);

      player.onEnd = () => {
        removeQueue();
      };

      // 开始播放动画
      player.start();
    };

    paySvga();

    return () => {
      player?.destroy();
    };
  }, [data, removeQueue]);

  return data ? (
    <canvas
      ref={joinSpecialAnimationRef}
      className=" absolute top-[100px] left-[100px] w-full h-[100px] z-20"
    />
  ) : null;
};
