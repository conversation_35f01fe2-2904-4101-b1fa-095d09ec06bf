import { useEffect, useRef } from "react";
import { FullAnimationData } from "../use-message";
import { DB, Parser, Player } from "svga";

interface Props {
  data: FullAnimationData;
  removeQueue: () => void;
}

export const FullAnimation = (props: Props) => {
  const { data, removeQueue } = props;

  const fullAnimationRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const { path } = data;
    let player: Player | null = null;

    const paySvga = async () => {
      const db = new DB();
      let svga = await db.find(path);

      if (!svga) {
        // Parser 需要配置取消使用 ImageBitmap 特性，ImageBitmap 数据无法直接存储到 DB 内
        const parser = new Parser({ isDisableImageBitmapShim: true });
        svga = await parser.load(path);
        await db.insert(path, svga);
      }

      player = new Player({
        container: fullAnimationRef.current!,
        loop: 1,
      });

      await player.mount(svga);

      player.onEnd = () => {
        removeQueue();
      };

      // 开始播放动画
      player.start();
    };

    paySvga();

    return () => {
      player?.destroy();
    };
  }, [data, removeQueue]);

  return data ? (
    <canvas
      ref={fullAnimationRef}
      className="absolute top-0 left-0 w-full h-full z-20"
    />
  ) : null;
};
