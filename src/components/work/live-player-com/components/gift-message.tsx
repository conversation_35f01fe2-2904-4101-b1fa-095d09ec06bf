import { UserAvatar } from "@/components/user-avatar";
import { Image } from "antd-mobile";
import { motion } from "motion/react";
import { User } from "@/type/user";
import { useEffect, useState } from "react";

interface Props {
  gift: {
    id: string;
    content?: string;
    image?: string;
    number?: number;
    user: User;
  };
  onFinish: () => void;
}

export const GiftMessage = ({ gift, onFinish }: Props) => {
  const { user, content, image, number } = gift;

  const [visible, setVisible] = useState(true);

  useEffect(() => {
    setTimeout(() => {
      setVisible(false);
      onFinish();
    }, 3000);
  }, [onFinish]);

  if (!visible) return null;

  return (
    <div className="h-10 flex">
      <motion.div
        initial={{ opacity: 0, x: "-40%" }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: "-40%" }}
        transition={{
          duration: 2,
          ease: [0.16, 1, 0.3, 1],
        }}
      >
        <div className="flex items-center px-[3px] py-[3px] rounded-[20px] bg-gradient-to-r from-black/60 to-transparent">
          <div className="mr-[5px]">
            <UserAvatar
              src={user.avatarBigImageUrl!}
              size={34}
              official={user.official}
              anchor={user.liveAnchor}
            />
          </div>

          <div className="mr-[5px] w-[100px]">
            <div className="flex flex-col justify-between">
              <p className="text-[13px] line-clamp-1">{user.nickname}</p>
              <p className="text-[12px] text-hint-color line-clamp-1">
                {content}
              </p>
            </div>
          </div>
          <div className="mr-[5px]">
            <Image src={image} className="h-[34px]" />
          </div>
          <span className="mr-[5px]">x</span>
          <div className="mr-[5px]">
            <motion.span
              className="text-[20px] font-bold"
              style={{
                transform: "skewX(-17deg)",
              }}
            >
              {number}
            </motion.span>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
