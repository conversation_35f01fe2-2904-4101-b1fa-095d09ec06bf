import { useConfigList } from "@/hooks/use-config-list";
import { Button } from "@/ui/button";
import AgoraRTC, { RemoteUser, useJoin, useRemoteUsers } from "agora-rtc-react";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useState,
} from "react";

export interface AgoraPlayerRef {
  play: () => void;
  pause: () => void;
}

interface AgoraPlayerProps {
  token: string;
  channel: string;
  uid: number;
}

export const AgoraPlayer = forwardRef<AgoraPlayerRef, AgoraPlayerProps>(
  (props, ref) => {
    const { token, channel, uid } = props;
    const [isPlayAudio, setIsPlayAudio] = useState(true);
    const [isPlay, setIsPlay] = useState(true);

    const { getSystemConfig } = useConfigList();
    const appid = getSystemConfig("AGORA_APP_ID");
    const remoteUsers = useRemoteUsers();

    // Agora 连接
    useJoin(
      {
        appid: appid!,
        token,
        channel,
        uid,
      },
      true
    );

    // 自动播放失败处理
    useEffect(() => {
      AgoraRTC.onAutoplayFailed = () => {
        console.log("autoplay failed");
        setIsPlayAudio(false);
      };
    }, []);

    // 处理音频播放
    const handlePlayAudio = useCallback(() => {
      setIsPlayAudio(true);

      if (remoteUsers?.[0].audioTrack?.isPlaying === false) {
        remoteUsers?.[0].audioTrack?.stop();
        remoteUsers?.[0].audioTrack?.play();
      }
    }, [remoteUsers]);

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => {
      return {
        pause: () => {
          setIsPlay(false);
        },
        play: () => {
          setIsPlay(true);
        },
      };
    });

    return (
      <div className="w-full h-full">
        {remoteUsers.map((user) => (
          <RemoteUser
            key={user.uid}
            user={user}
            playAudio={isPlay}
            playVideo={isPlay}
          >
            {!isPlayAudio ? (
              <div className="absolute z-10 top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]">
                <Button variant="secondary" onClick={handlePlayAudio}>
                  已静音
                </Button>
              </div>
            ) : null}
          </RemoteUser>
        ))}
      </div>
    );
  }
);
