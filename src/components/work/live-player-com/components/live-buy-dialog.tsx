import { But<PERSON> } from "@/ui/button";
import { Dialog, Image } from "antd-mobile";
import "./live-buy-dialog.less";
import { liveBuy } from "@/components/image-list";

interface Options {
  amount: number;
  mode: string;
  onCancel?: () => void;
  onConfirm?: () => void;
}

class LiveBuyDialog {
  private baseDialog(options: Options) {
    const { amount, mode, onCancel, onConfirm } = options;

    const { close } = Dialog.show({
      className: "live-buy-dialog",
      bodyClassName: "dialog-bg",
      closeOnMaskClick: true,
      title: (
        <div className="text-[20px] text-black pt-[5px] font-normal">
          付费提示
        </div>
      ),
      content: (
        <div className="flex flex-col gap-[10px]">
          <div className="text-black text-sm">
            <div className="text-[15px]">
              <span className="inline-flex p-[2px] rounded-[3px] bg-[#FF595D] mr-[5px]">
                <span className="text-[22px] text-white" style={{ zoom: 0.5 }}>
                  请注意
                </span>
              </span>
              观看已达上限，请点击确认付费按钮获得观看权限
            </div>
            <div></div>
          </div>
          <div className="relative">
            <Image src={liveBuy} />
            <div className="absolute left-0 top-0 w-full h-full flex items-center">
              <div className="flex-1 text-[15px] flex flex-col gap-[5px] items-center justify-center">
                <span className="text-[#C16B5A]">付费金额</span>
                <span className="text-[#830000]">{amount}</span>
              </div>
              <div className="flex-1 text-[15px] flex flex-col gap-[5px] items-center justify-center">
                <span className="text-[#C16B5A]">付费方式</span>
                <span className="text-[#830000]">{mode}</span>
              </div>
            </div>
          </div>
          <div className="flex justify-evenly">
            <Button
              variant="secondary"
              className="mx-[5px] text-base w-full h-[44px]"
              onClick={() => {
                close();
                onCancel?.();
              }}
            >
              取消
            </Button>
            <Button
              variant="primary"
              className="mx-[5px] text-base w-full h-[44px]"
              onClick={() => {
                close();
                onConfirm?.();
              }}
            >
              确认付费
            </Button>
          </div>
        </div>
      ),
    });
  }
  open(options: Options) {
    this.baseDialog(options);
  }
}

export const liveBuyDialog = new LiveBuyDialog();
