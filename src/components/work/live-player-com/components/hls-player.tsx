import { Button } from "@/ui/button";
import videojs from "video.js";
import Player from "video.js/dist/types/player";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";

export interface HLSPlayerRef {
  play: () => void;
  pause: () => void;
}

interface HLSPlayerProps {
  hlsUrl: string;
  poster?: string;
  onLoadingChange?: (loading: boolean) => void;
}

export const HLSPlayer = forwardRef<HLSPlayerRef, HLSPlayerProps>(
  (props, ref) => {
    const { hlsUrl, poster, onLoadingChange } = props;
    const [isPlayAudio, setIsPlayAudio] = useState(true);
    const [isPlay, setIsPlay] = useState(true);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const videoRef = useRef<HTMLVideoElement>(null);
    const playerRef = useRef<Player>();

    // 初始化 Video.js 播放器
    useEffect(() => {
      if (videoRef.current && !playerRef.current) {
        playerRef.current = videojs(videoRef.current, {
          preload: "auto",
          controls: true,
          autoplay: true,
          loop: false,
          enableSmoothSeeking: true,
          language: "zh-CN",
          muted: false,
          poster: poster,
          fluid: true,
          responsive: true,
          liveui: true,
          liveTracker: {
            trackingThreshold: 0,
            liveTolerance: 15,
          },
          errorDisplay: {
            timeout: 5000,
          },
          controlBar: {
            children: [
              "playToggle",
              "volumePanel",
              "currentTimeDisplay",
              "timeDivider",
              "durationDisplay",
              "progressControl",
              "liveDisplay",
              "remainingTimeDisplay",
              "customControlSpacer",
              "fullscreenToggle",
            ],
          },
        });

        // 设置 HLS 源
        if (hlsUrl) {
          playerRef.current.src({
            src: hlsUrl,
            type: "application/x-mpegURL",
          });
        }

        // 事件监听
        playerRef.current.on("loadstart", () => {
          setIsLoading(true);
          setError(null);
          onLoadingChange?.(true);
        });

        playerRef.current.on("canplay", () => {
          setIsLoading(false);
          onLoadingChange?.(false);
        });

        playerRef.current.on("error", (error: any) => {
          console.error("Video.js error:", error);
          const errorMsg = "直播加载失败，请检查网络连接";
          setError(errorMsg);
          setIsLoading(false);
          onLoadingChange?.(false);
        });

        playerRef.current.on("ended", () => {
          const errorMsg = "直播已结束";
          setError(errorMsg);
        });

        playerRef.current.on("autoplay-failed", () => {
          console.log("HLS 自动播放失败，需要用户交互");
          setIsPlayAudio(false);
        });

        // 添加双击事件处理
        const handleDoubleClick = (event: MouseEvent | TouchEvent) => {
          const controlBar = playerRef.current
            ?.el()
            .querySelector(".vjs-control-bar");
          if (controlBar && !controlBar.contains(event.target as Node)) {
            if (playerRef.current?.paused()) {
              playerRef.current?.play();
            } else {
              playerRef.current?.pause();
            }
          }
        };

        let lastTap = 0;
        const handleTap = (event: TouchEvent) => {
          const currentTime = new Date().getTime();
          const tapLength = currentTime - lastTap;
          if (tapLength < 500 && tapLength > 0) {
            handleDoubleClick(event);
            event.preventDefault();
          }
          lastTap = currentTime;
        };

        const videoElement = playerRef.current
          ?.el()
          .querySelector(".vjs-tech") as HTMLVideoElement | null;

        if (videoElement) {
          videoElement.addEventListener("touchend", handleTap);
        }

        return () => {
          if (videoElement) {
            videoElement.removeEventListener("touchend", handleTap);
          }
        };
      }
    }, [hlsUrl, poster, onLoadingChange]);

    // 播放/暂停控制
    useEffect(() => {
      const player = playerRef.current;
      if (player && player.readyState() >= 2) {
        if (isPlay) {
          player.play();
        } else {
          player.pause();
        }
      }
    }, [isPlay]);

    // 音频控制
    useEffect(() => {
      const player = playerRef.current;
      if (player) {
        if (isPlayAudio) {
          player.muted(false);
        } else {
          player.muted(true);
        }
      }
    }, [isPlayAudio]);

    // 处理音频播放
    const handlePlayAudio = useCallback(() => {
      setIsPlayAudio(true);
      const player = playerRef.current;
      if (player) {
        player.muted(false);
        player.play();
      }
    }, []);

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => {
      return {
        pause: () => {
          setIsPlay(false);
        },
        play: () => {
          setIsPlay(true);
        },
      };
    });

    // 组件卸载时清理播放器
    useEffect(() => {
      return () => {
        if (playerRef.current) {
          playerRef.current.dispose();
          playerRef.current = undefined;
        }
      };
    }, []);

    return (
      <div className="w-full h-full relative">
        <video
          ref={videoRef}
          className="video-js vjs-default-skin vjs-big-play-centered w-full h-full"
        />

        {/* 加载状态 */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-10">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
              <span>直播加载中...</span>
            </div>
          </div>
        )}

        {/* 错误状态 */}
        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-10">
            <div className="text-white text-center">
              <div className="mb-2">{error}</div>
              <Button
                variant="secondary"
                onClick={() => {
                  setError(null);
                  setIsLoading(true);
                  onLoadingChange?.(true);
                  if (playerRef.current) {
                    playerRef.current.src({
                      src: hlsUrl,
                      type: "application/x-mpegURL",
                    });
                  }
                }}
              >
                重新加载
              </Button>
            </div>
          </div>
        )}

        {/* 静音提示 */}
        {!isPlayAudio && !error && (
          <div className="absolute z-10 top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]">
            <Button variant="secondary" onClick={handlePlayAudio}>
              已静音，点击播放
            </Button>
          </div>
        )}
      </div>
    );
  }
);
