import { User } from "@/type/user";
import { useMemo } from "react";
import { UserLevelTag } from "../../user-level-tag";
import { LiveNodeDataType } from "@/utils/enums";
import { MessageData } from "../use-message";
import { Image } from "antd-mobile";

interface Props {
  anchor: User;
  message: MessageData;
  showTag?: boolean;
}

export const MessageItem = (props: Props) => {
  const { anchor, message, showTag = true } = props;
  const { liveType, user: sender, content, text, number, image } = message;

  const renderContent = useMemo(() => {
    switch (liveType) {
      case LiveNodeDataType.tips:
        return <span className="text-[#90DCF4]">{content}</span>;
      case LiveNodeDataType.text:
        return <span className="text-white">{text}</span>;
      case LiveNodeDataType.gift:
        return (
          <div className="inline-flex gap-[5px]">
            <span className="text-[#FF5722]">
              {content} x{number}
            </span>
            <Image
              src={image}
              width={`${20 / 16}rem`}
              height={`${20 / 16}rem`}
              fit="cover"
              placeholder={null}
            />
          </div>
        );
      case LiveNodeDataType.chargeChange:
        return <span className="text-[#90DCF4]">{content}</span>;
    }

    return null;
  }, [content, image, liveType, number, text]);

  return (
    <div className="text-sm bg-black/30 rounded-[5px] px-[10px] py-[5px]">
      <div className="inline-flex">
        {sender && showTag ? <UserLevelTag user={sender} /> : null}
        {sender?.id === anchor.id ? (
          <div className="inline-flex ml-[5px] px-[5px] py-[2px] rounded-[12px] bg-[#E34F4F]">
            <span className="text-[18px] text-white" style={{ zoom: 0.5 }}>
              主播
            </span>
          </div>
        ) : null}
      </div>
      {sender ? (
        <span className="text-[#90DCF4] ml-[5px] flex-none">
          {sender.nickname}
        </span>
      ) : null}
      <span className="ml-[5px]">{renderContent}</span>
    </div>
  );
};
