import { forwardRef, useImperativeHandle, useRef } from "react";
import { HLSPlayer, HLSPlayerRef } from "./hls-player";
import { AgoraPlayer, AgoraPlayerRef } from "./agora-player";

export interface LivePlayerRef {
  play: () => void;
  pause: () => void;
}

type Props = {
  token: string;
  channel: string;
  uid: number;
  hlsUrl?: string;
  poster?: string;
};

export const LivePlayer = forwardRef<LivePlayerRef, Props>((props, ref) => {
  const { token, channel, uid, hlsUrl, poster } = props;

  const hlsPlayerRef = useRef<HLSPlayerRef>(null);
  const agoraPlayerRef = useRef<AgoraPlayerRef>(null);

  const useHLS = !!hlsUrl;

  // 暴露统一的方法给父组件
  useImperativeHandle(ref, () => {
    return {
      play: () => {
        if (useHLS) {
          hlsPlayerRef.current?.play();
        } else {
          agoraPlayerRef.current?.play();
        }
      },
      pause: () => {
        if (useHLS) {
          hlsPlayerRef.current?.pause();
        } else {
          agoraPlayerRef.current?.pause();
        }
      },
    };
  });

  return useHLS ? (
    <HLSPlayer ref={hlsPlayerRef} hlsUrl={hlsUrl!} poster={poster} />
  ) : (
    <AgoraPlayer
      ref={agoraPlayerRef}
      token={token}
      channel={channel}
      uid={uid!}
    />
  );
});
