import { motion, AnimatePresence } from "motion/react";
import { MessageData } from "../use-message";
import { MessageItem } from "./message-item";
import { User } from "@/type/user";
import { useEffect, useState } from "react";

interface InstantReminderMessageProps {
  anchor: User;
  message: MessageData;
  onFinish: () => void;
}

const InstantReminderMessage: React.FC<InstantReminderMessageProps> = ({
  anchor,
  message,
  onFinish,
}) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    setTimeout(() => {
      setVisible(false);
      onFinish();
    }, 2000);
  }, [onFinish]);

  if (!message || !visible) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="flex"
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 40 }}
        transition={{
          duration: 1.5,
          ease: [0.4, 0, 0.2, 1],
        }}
      >
        <div className="px-2 py-[5px]">
          <MessageItem anchor={anchor} message={message} />
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default InstantReminderMessage;
