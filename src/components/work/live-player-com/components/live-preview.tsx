import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { liveBuyDialog } from "./live-buy-dialog";
import { liveRoomChargeType } from "@/utils/enums";
import { formatDuration } from "@/utils/format-duration";
import { isNil } from "lodash-es";
import { useCountUpTimer } from "@/hooks/use-count-up-timer";
import { useMutation, useQuery } from "@tanstack/react-query";
import { buyLiveRoom, liveRoomCheckTimeDeduction } from "@/lib/api/live";
import { Toast } from "antd-mobile";
import { queryClient } from "@/provider/query-client";
import { userWalletQueryKey } from "@/hooks/use-user-wallet";
import { LiveRoomDetailedResult } from "@/type/live-room-detailed-result";

export interface LivePreviewProps {
  visible: boolean;
  id: string;
  setDetail: (detail: LiveRoomDetailedResult) => void;
  pausePlay: () => void;
  resumePlay: () => void;
}

export interface LivePreviewRef {
  startPreview: (
    chargeType: number,
    price: number,
    allowPreview: boolean,
    maxPreviewTime: number
  ) => void;
}

export const LivePreview = forwardRef<LivePreviewRef, LivePreviewProps>(
  (props, ref) => {
    const { id, visible, setDetail, pausePlay, resumePlay } = props;

    const cacheKey = useMemo(() => `live_preview_${id}`, [id]);

    const chargeTypeRef = useRef<number>();
    const priceRef = useRef<number>();
    const checkTimeDeductionIntervalRef = useRef<NodeJS.Timeout | null>(null);
    const pausePlayRef = useRef<() => void>();
    const resumePlayRef = useRef<() => void>();

    pausePlayRef.current = pausePlay;
    resumePlayRef.current = resumePlay;

    const [allowPreview, setAllowPreview] = useState(false);
    const [maxPreviewTime, setMaxPreviewTime] = useState(0);

    const { currentTime, handleStart } = useCountUpTimer();

    const { refetch: checkTimeDeductionRefetch } = useQuery({
      queryKey: ["live_room_check_time_deduction", id],
      queryFn: () => liveRoomCheckTimeDeduction({ id }),
      enabled: false,
    });

    const { mutateAsync: buyLiveRoomMutation } = useMutation({
      mutationFn: buyLiveRoom,
      onSuccess: () => {
        showBuyDialog();
      },
    });

    const checkTimeDeduction = useCallback(async () => {
      const { data } = await checkTimeDeductionRefetch();
      if (data) {
        queryClient.refetchQueries({
          queryKey: userWalletQueryKey,
        });
      } else {
        pausePlayRef.current?.();

        if (checkTimeDeductionIntervalRef.current) {
          clearInterval(checkTimeDeductionIntervalRef.current);
          checkTimeDeductionIntervalRef.current = null;
        }
      }
    }, [checkTimeDeductionRefetch]);

    const showBuyDialog = useCallback(() => {
      const targetValue = Object.values(liveRoomChargeType).find(
        (item) => item.value === chargeTypeRef.current
      );

      liveBuyDialog.open({
        amount: priceRef.current ?? 0,
        mode: targetValue?.sign ?? "",
        onConfirm: async () => {
          const { ok, data } = await buyLiveRoomMutation({ id });

          if (ok && data) {
            queryClient.refetchQueries({
              queryKey: userWalletQueryKey,
            });

            Toast.show({
              content: "直播间购买成功，现在可继续观看",
            });

            setDetail(data);
            /** 恢复播放 */
            resumePlayRef.current?.();

            if (data.chargeType === liveRoomChargeType.time.value) {
              // 如果是按场收费直播间，则每60秒查询一下付费状态，如果付费状态错误，则暂停直播
              checkTimeDeductionIntervalRef.current = setInterval(() => {
                checkTimeDeduction();
              }, 60 * 1000);
            }
          }
        },
      });
    }, [buyLiveRoomMutation, id, setDetail, checkTimeDeduction]);

    useEffect(() => {
      if (currentTime > 0) {
        localStorage.setItem(cacheKey, String(currentTime));
      }

      if (maxPreviewTime && currentTime >= maxPreviewTime) {
        showBuyDialog();
        pausePlayRef.current?.();
      }
    }, [cacheKey, currentTime, maxPreviewTime, showBuyDialog]);

    useEffect(() => {
      return () => {
        if (checkTimeDeductionIntervalRef.current) {
          clearInterval(checkTimeDeductionIntervalRef.current);
          checkTimeDeductionIntervalRef.current = null;
        }
      };
    }, []);

    useImperativeHandle(ref, () => ({
      startPreview: (
        chargeType: number,
        price: number,
        allowPreview: boolean,
        maxPreviewTime: number
      ) => {
        chargeTypeRef.current = chargeType;
        priceRef.current = price;

        setAllowPreview(allowPreview);
        setMaxPreviewTime(maxPreviewTime);

        if (!allowPreview) {
          showBuyDialog();
          pausePlayRef.current?.();

          return;
        }

        const cacheValue = localStorage.getItem(cacheKey);

        let currentTime: number;

        if (!isNil(cacheKey)) {
          currentTime = Number(cacheValue);
        } else {
          currentTime = 0;
        }

        handleStart(currentTime, maxPreviewTime);
      },
    }));

    if (!visible) {
      return null;
    }

    return (
      <div className="px-[13px] py-[5px] bg-white/10 rounded-[15px] text-white text-[13px]">
        {!allowPreview
          ? "当前直播间不允许试看"
          : `试看中: ${formatDuration(currentTime)}/${formatDuration(
              maxPreviewTime
            )}`}
      </div>
    );
  }
);
