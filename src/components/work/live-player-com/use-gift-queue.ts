import { User } from "@/type/user";
import { useQueue } from "./use-queue";
import { useCallback, useRef } from "react";

export interface GiftData {
  uuid: string;
  id: string;
  content?: string;
  image?: string;
  number?: number;
  user: User;
}

export const useGiftQueue = () => {
  const indexRef = useRef(0);

  const {
    addQueue: addGiftQueue1,
    removeQueue: removeGiftQueue1,
    current: giftCurrent1,
  } = useQueue<GiftData>();

  const {
    addQueue: addGiftQueue2,
    removeQueue: removeGiftQueue2,
    current: giftCurrent2,
  } = useQueue<GiftData>();

  const addQueue = useCallback(
    (gift: GiftData) => {
      if (indexRef.current === 0) {
        addGiftQueue1(gift);
      } else {
        addGiftQueue2(gift);
      }

      indexRef.current = indexRef.current === 0 ? 1 : 0;
    },
    [addGiftQueue1, addGiftQueue2]
  );

  return {
    addQueue,
    removeQueue: [removeGiftQueue1, removeGiftQueue2],
    current: [giftCurrent1, giftCurrent2],
  };
};
