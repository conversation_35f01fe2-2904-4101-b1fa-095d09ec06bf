import { ScrollArea } from "@/ui/scroll-area";
import {
  custom4349EmojiConfig,
  customTcc1EmojiConfig,
  get4349ImageUrl,
  getTcc1ImageUrl,
  unicodeEmojiList,
} from "@/utils/emoji";
import { Image } from "antd-mobile";
import classNames from "classnames";
import { useState } from "react";

interface Props {
  className?: string;
  onSelectEmoji: (emoji: string) => void;
}

export const EmojiPicker = (props: Props) => {
  const { className, onSelectEmoji } = props;

  const [activeEmoji, setActiveEmoji] = useState<"tcc1" | "4349" | "unicode">(
    "tcc1"
  );

  const iconTcc1 = customTcc1EmojiConfig.icon.replace(".png", "");
  const icon4349 = custom4349EmojiConfig.icon.replace(".png", "");

  return (
    <div
      className={classNames(
        "w-full h-[200px] flex flex-col bg-app-bar-background",
        className
      )}
    >
      <div className="flex-1 h-0">
        <div
          className={classNames("h-full", activeEmoji !== "tcc1" && "hidden")}
        >
          <ScrollArea className="h-full">
            <div className="flex flex-wrap py-1 px-2">
              {customTcc1EmojiConfig.list.map((item, index) => (
                <div
                  key={index}
                  className="p-1"
                  onClick={() => onSelectEmoji(`[${item.replace(".png", "")}]`)}
                >
                  <div className="w-[24px] h-[24px]">
                    <Image
                      width="100%"
                      height="100%"
                      placeholder={null}
                      fallback={null}
                      src={getTcc1ImageUrl(item.replace(".png", ""))}
                    />
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
        <div
          className={classNames("h-full", activeEmoji !== "4349" && "hidden")}
        >
          <ScrollArea className="h-full">
            <div className="flex flex-wrap py-1 px-2">
              {custom4349EmojiConfig.list.map((item, index) => (
                <div
                  key={index}
                  className="p-1"
                  onClick={() => onSelectEmoji(`[${item.replace(".png", "")}]`)}
                >
                  <div className="w-[24px] h-[24px]">
                    <Image
                      width="100%"
                      height="100%"
                      placeholder={null}
                      fallback={null}
                      src={get4349ImageUrl(item.replace(".png", ""))}
                    />
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
        <div
          className={classNames(
            "h-full",
            activeEmoji !== "unicode" && "hidden"
          )}
        >
          <ScrollArea className="h-full">
            <div className="flex flex-wrap py-1 px-2">
              {unicodeEmojiList.map((item, index) => (
                <div
                  key={index}
                  className="p-1"
                  onClick={() => onSelectEmoji(String.fromCodePoint(item))}
                >
                  <div className="text-[24px] leading-[24px]">
                    {String.fromCodePoint(item)}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>
      <div className="flex-none flex p-1">
        <div
          className={classNames(
            "p-1 rounded-[2px]",
            activeEmoji === "tcc1" && " bg-blue-600"
          )}
          onClick={() => setActiveEmoji("tcc1")}
        >
          <div className="w-[20px] h-[20px]">
            <Image
              width="100%"
              height="100%"
              placeholder={null}
              fallback={null}
              src={getTcc1ImageUrl(iconTcc1)}
            />
          </div>
        </div>
        <div
          className={classNames(
            "p-1 rounded-[2px]",
            activeEmoji === "4349" && " bg-blue-600"
          )}
          onClick={() => setActiveEmoji("4349")}
        >
          <div className="w-[20px] h-[20px]">
            <Image
              width="100%"
              height="100%"
              placeholder={null}
              fallback={null}
              src={get4349ImageUrl(icon4349)}
            />
          </div>
        </div>
        <div
          className={classNames(
            "p-[5px] rounded-[2px]",
            activeEmoji === "unicode" && " bg-blue-600"
          )}
          onClick={() => setActiveEmoji("unicode")}
        >
          <div className="text-[18px] leading-[18px]">
            {String.fromCodePoint(unicodeEmojiList[0])}
          </div>
        </div>
      </div>
    </div>
  );
};
