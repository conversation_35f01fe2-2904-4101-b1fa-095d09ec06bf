import {Divider, Image} from "antd-mobile";
import {Fragment} from "react";
import {DefaultLoadImage} from "../default-load-image";
import {PayChannelResult} from "@/type/pay-channel-result";
import {PayType} from "@/utils/enums.ts";

interface Props {
  data: PayChannelResult[];
  onClose: () => void;
  onSelect: (channel: PayChannelResult) => void;
}

export const PayModal = (props: Props) => {
  const { data, onSelect, onClose } = props;

  return (
    <div className="flex flex-col gap-[10px] text-[#9d64ff] mx-[15px] text-base">
      <div className="bg-app-bar-background rounded-[10px]">
        {/*  todo 临时调整，调整为网页版仅保留网页支付，不保留其他内容 */}
        {data?.filter(item => item.type == PayType.Web).map((item, index) => (
        // {data?.map((item, index) => (
          <Fragment key={item.type}>
            <div
              className="flex items-center justify-center gap-[10px] py-[15px]"
              onClick={() => onSelect(item)}
            >
              <div className="w-[21px]">
                <Image
                  src={item.icon}
                  width="100%"
                  height="auto"
                  placeholder={<DefaultLoadImage />}
                  fallback={<DefaultLoadImage />}
                />
              </div>
              <div>{item.name}</div>
            </div>
            {index !== data.length - 1 ? (
              <Divider className="border-divider m-0" />
            ) : null}
          </Fragment>
        ))}
      </div>
      <div
        className="bg-hint-color rounded-[10px] mb-[10px] py-[15px] text-center"
        onClick={onClose}
      >
        取消
      </div>
    </div>
  );
};
