import { TUIMessageProps } from "@tencentcloud/chat-uikit-react";
import { Image, ImageViewer } from "antd-mobile";
import { useState } from "react";

export const ImageMessageNode = ({
  message,
}: {
  message: TUIMessageProps["message"];
}) => {
  const [visible, setVisible] = useState(false);

  const imageList = message?.payload?.imageInfoArray ?? [];

  // sizeType 1 格式 web 无法打开
  const largeImage = imageList.find((item: any) => item.sizeType === 2)?.url;
  const smallImage = imageList.find((item: any) => item.sizeType === 3)?.url;

  return (
    <div className="rounded-[5px] max-h-[256px] min-w-[80px] max-w-[256px] overflow-hidden">
      <Image
        src={smallImage}
        width="100%"
        height="100%"
        className="rounded-[5px]"
        onClick={() => setVisible(true)}
      />
      <ImageViewer
        image={largeImage}
        visible={visible}
        onClose={() => setVisible(false)}
      />
    </div>
  );
};
