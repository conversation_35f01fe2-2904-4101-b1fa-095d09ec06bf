import { CustomNodeDataType } from "@/utils/constant";
import { SystemNoticeNode } from "./system-notice-node";
import { TipsNode } from "./tips-node";
import { ShareCardNode } from "./share-card-node";
import { RedEnvelopeNode } from "./red-envelope-node";

export const CustomMessageNode = ({ data }: { data: any }) => {
  const { type, inviteID } = data;

  if (inviteID) {
    try {
      const value = JSON.parse(data.data);

      return (
        <div className="w-full p-[15px] flex justify-center text-hint-color">
          {value?.desc ?? "未知信令，请升级客户端版本!"}
        </div>
      );
    } catch {
      //
    }
  }

  switch (type) {
    case CustomNodeDataType.SystemNotice: {
      return <SystemNoticeNode data={data} />;
    }
    case CustomNodeDataType.Tips: {
      return <TipsNode data={data} />;
    }
    case CustomNodeDataType.ShareCard: {
      return <ShareCardNode data={data} />;
    }
    case CustomNodeDataType.RedEnvelope: {
      return <RedEnvelopeNode data={data} />;
    }
    case CustomNodeDataType.Signaling: {
      return (
        <div className="p-[15px] flex justify-center text-hint-color">
          {data.desc ?? "未知信令，请升级客户端版本!"}
        </div>
      );
    }
    default:
      return null;
  }
};
