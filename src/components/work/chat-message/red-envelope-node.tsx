import {
  redEnvelopeInvalid,
  redEnvelopeEfficient,
} from "@/components/image-list";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { receiveRedEnvelope } from "@/lib/api/wallet";
import { IRedEnvelopeNode } from "@/type/custom-message-node-data";
import { localCustomMessage } from "@/utils/local-custom-message";
import { useMutation } from "@tanstack/react-query";
import { Image, Toast } from "antd-mobile";
import { useState } from "react";

interface Props {
  data: IRedEnvelopeNode;
}

export const RedEnvelopeNode = ({ data }: Props) => {
  const { id, content } = data;

  const [isValid, setIsValid] = useState(false);

  const invalid = isValid || localCustomMessage.getMessageStatus(id!);

  const { mutateAsync: receiveRedEnvelopeMutate } = useMutation({
    mutationFn: receiveRedEnvelope,
  });

  const { navigateRoute } = useNavigateRoute();

  const handleClick = async () => {
    if (!invalid) {
      Toast.show({
        content: "请稍后...",
        icon: "loading",
        duration: 0,
      });

      const { ok } = await receiveRedEnvelopeMutate(id!);

      if (ok) {
        setIsValid(true);
      }

      localCustomMessage.setMessage(id!);
    }

    navigateRoute("/red-envelope-detail", { id });
  };

  return (
    <div
      className="w-[200px] h-[66px] relative rounded-[20px]"
      onClick={handleClick}
    >
      <Image
        src={invalid ? redEnvelopeInvalid : redEnvelopeEfficient}
        width="100%"
        height="100%"
        placeholder={null}
        className="rounded-[10px]"
        fit="fill"
      />
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="h-full pl-[70px] pr-[15px] flex flex-col justify-center">
          <div className="text-[15px] text-white line-clamp-1">{content}</div>
          <div className="text-xs text-[#FFCECE]">
            {invalid ? "已领取" : "点击领取"}
          </div>
        </div>
      </div>
    </div>
  );
};
