import { DefaultLoadImage } from "@/components/default-load-image";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { getRoute } from "@/router/route-map";
import { IShareCardNode } from "@/type/custom-message-node-data";
import { Image } from "antd-mobile";
import classNames from "classnames";

interface Props {
  data: IShareCardNode;
}

export const ShareCardNode = (props: Props) => {
  const { data } = props;
  const { label, cover, title, subtitle, targetUrl } = data;

  const { navigateRoute } = useNavigateRoute();

  const handleClick = () => {
    if (targetUrl) {
      navigateRoute(getRoute(targetUrl));
    }
  };

  return (
    <div className="flex flex-col w-[225px]" onClick={handleClick}>
      {label ? (
        <div className="px-[15px] py-[5px] bg-[e9e9e9] dark:bg-[#1b232d]">
          {label}
        </div>
      ) : null}
      <div className="px-[15] py-[10px] bg-app-bar-background flex gap-[10px]">
        {cover ? (
          <div className="w-[55px] h-[55px] flex-none">
            <Image
              width="100%"
              height="100%"
              className="rounded-[5px]"
              src={cover}
              fit="cover"
              placeholder={<DefaultLoadImage />}
            />
          </div>
        ) : null}
        <div className="flex flex-col">
          <span className={classNames("text-sm", !subtitle && "line-clamp-1")}>
            {title}
          </span>
          {subtitle ? (
            <span className="text-[13px] text-hint-color line-clamp-2">
              {subtitle}
            </span>
          ) : null}
        </div>
      </div>
    </div>
  );
};
