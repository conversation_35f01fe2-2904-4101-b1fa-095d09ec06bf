import { replaceEmoji } from "@/utils/emoji";
import { TUIMessageProps } from "@tencentcloud/chat-uikit-react";
import classNames from "classnames";

export const TextMessageNode = ({
  message,
  isSelf,
}: {
  message: TUIMessageProps["message"];
  isSelf: boolean;
}) => {
  return (
    <div
      className={classNames(
        "bg-[#8f959e] rounded-[10px] p-[10px] inline-flex items-center",
        isSelf ? "rounded-tr-[2px]" : "rounded-tl-[2px]"
      )}
    >
      {replaceEmoji(message?.payload?.text)}
    </div>
  );
};
