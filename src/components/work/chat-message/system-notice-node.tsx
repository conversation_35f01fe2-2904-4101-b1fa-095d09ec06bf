import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { getRoute } from "@/router/route-map";
import { ISystemNoticeNode } from "@/type/custom-message-node-data";
import { UserInfo } from "../user-info";
import { Divider, Image } from "antd-mobile";
import { DefaultLoadImage } from "@/components/default-load-image";
import { ArrowIcon } from "@/components/svg-icon";

export const SystemNoticeNode = ({ data }: { data: ISystemNoticeNode }) => {
  const { cover, title, subtitle, target, operator } = data;

  const { navigateRoute } = useNavigateRoute();

  const openTarget = (target?: string) => {
    if (target) {
      navigateRoute(getRoute(target));
    }
  };

  return (
    <div
      className="w-full flex flex-col mx-[15px] my-[10px] p-[10px] bg-app-bar-background rounded-[10px]"
      onClick={() => openTarget(target)}
    >
      {operator ? (
        <div className="flex flex-col">
          <UserInfo user={operator} size={40} />
          <Divider className="my-[10px] border-divider" />
        </div>
      ) : null}
      <div className="flex gap-[10px]">
        {cover ? (
          <div className="w-[50px] h-[50px] rounded-[5px] overflow-hidden">
            <Image
              width="100%"
              height="100%"
              fit="cover"
              src={data.cover}
              fallback={<DefaultLoadImage />}
              placeholder={<DefaultLoadImage />}
            />
          </div>
        ) : null}
        <div className="flex flex-col flex-1 justify-center">
          <span className="line-clamp-1 text-[15px]">{title ?? "-"}</span>
          {subtitle ? (
            <span className="text-xs text-hint-color">{subtitle}</span>
          ) : null}
        </div>
        {target ? <ArrowIcon className="text-hint-color" /> : null}
      </div>
    </div>
  );
};
