import { ImageViewer } from "@/components/image-viewer/image-viewer";
import { PlayIcon } from "@/components/svg-icon";
import { TUIMessageProps } from "@tencentcloud/chat-uikit-react";
import { Image } from "antd-mobile";
import { useState } from "react";

export const VideoMessageNode = ({
  message,
}: {
  message: TUIMessageProps["message"];
}) => {
  const [visible, setVisible] = useState(false);

  const { snapshotUrl, videoUrl } = message?.payload ?? {};

  return (
    <div className="relative rounded-[5px] max-h-[256px] min-w-[80px] max-w-[256px] overflow-hidden">
      <Image
        src={snapshotUrl}
        width="100%"
        height="100%"
        className="rounded-[5px]"
      />
      <div
        className="absolute top-0 left-0 w-full h-full flex items-center justify-center"
        onClick={() => setVisible(true)}
      >
        <PlayIcon size={32} />
      </div>
      <ImageViewer
        info={{
          thumbnailImage: snapshotUrl,
          url: videoUrl,
          type: "video",
        }}
        visible={visible}
        onClose={() => setVisible(false)}
      />
    </div>
  );
};
