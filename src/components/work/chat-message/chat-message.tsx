import { TUIMessageProps } from "@tencentcloud/chat-uikit-react";
import { ReactNode, useMemo } from "react";
import { CustomMessageNode } from "./custom-message-node";
import { TextMessageNode } from "./text-message-node";
import classNames from "classnames";
import { Avatar } from "@/components/avatar";
import { CustomNodeDataType } from "@/utils/constant";
import { ImageMessageNode } from "./image-message-node";
import { VideoMessageNode } from "./video-message-node";
import { VoiceMessageNode } from "./voice-message-node";
import TUIChatEngine from "@tencentcloud/chat-uikit-engine";

export const ChatMessage = ({ message }: TUIMessageProps) => {
  const { payload, type, avatar } = message ?? {};

  const userID = TUIChatEngine.getMyUserID();

  const isSelf = userID === message?.from;

  console.log("message", message);

  const customData = useMemo(() => {
    if (payload.data) {
      return JSON.parse(payload.data ?? "");
    }
  }, [payload.data]);

  const messageNode: ReactNode = useMemo(() => {
    if (customData) {
      return (
        <div className="flex">
          <CustomMessageNode data={customData} />
        </div>
      );
    }

    switch (type) {
      case TUIChatEngine.TYPES.MSG_TEXT:
        return <TextMessageNode message={message} isSelf={isSelf} />;

      case TUIChatEngine.TYPES.MSG_IMAGE:
        return <ImageMessageNode message={message} />;

      case TUIChatEngine.TYPES.MSG_VIDEO:
        return <VideoMessageNode message={message} />;

      case TUIChatEngine.TYPES.MSG_AUDIO:
        return <VoiceMessageNode message={message} isSelf={isSelf} />;
    }
  }, [isSelf, message, type, customData]);

  const isFullMessage = useMemo(() => {
    return (
      [
        CustomNodeDataType.SystemNotice,
        CustomNodeDataType.Tips,
        CustomNodeDataType.Signaling,
      ].includes(customData?.type) || customData?.inviteID
    );
  }, [customData]);

  if (isFullMessage) {
    return messageNode;
  }

  return (
    <div
      className={classNames(
        "flex gap-2 w-full items-start px-[15px] py-[10px]",
        isSelf && "justify-end"
      )}
    >
      {isSelf ? (
        <>
          {messageNode}
          <Avatar src={avatar!} size={40} />
        </>
      ) : (
        <>
          <Avatar src={avatar!} size={40} />
          {messageNode}
        </>
      )}
    </div>
  );
};
