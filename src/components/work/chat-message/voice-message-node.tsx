import { TUIMessageProps } from "@tencentcloud/chat-uikit-react";
import { VoicePlayer } from "./voice-player";

export const VoiceMessageNode = ({
  message,
  isSelf,
}: {
  message: TUIMessageProps["message"];
  isSelf: boolean;
}) => {
  const { url, second } = message?.payload ?? {};

  return (
    <div className="w-[80px]">
      <VoicePlayer url={url} duration={second ?? 0} isSelf={isSelf} />
    </div>
  );
};
