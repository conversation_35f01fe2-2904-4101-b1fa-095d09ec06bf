import { useState } from "react";
import { audioManager, AudioStatus } from "@/utils/audio-manager";
import { SpinLoading } from "antd-mobile";
import VoiceAnimation from "@/components/voice-animation";
import classNames from "classnames";

interface Props {
  url: string;
  duration: number;
  isSelf: boolean;
  onPlayer?: () => void;
}

export const VoicePlayer = (props: Props) => {
  const { url, duration, isSelf, onPlayer } = props;

  const [playStatus, setPlayStatus] = useState<AudioStatus>(
    AudioStatus.STOPPED
  );

  const handleStatusChange = (status: AudioStatus) => {
    console.log(status, url);
    setPlayStatus(status);
  };

  const handlePlayClick = (url: string) => {
    audioManager.play({
      url,
      statusCallback: handleStatusChange,
    });
    onPlayer?.();
  };

  return (
    <div
      className={classNames(
        "w-full h-[35px] voice-play-bg flex items-center px-[15px] gap-[5px] rounded-[10px]",
        isSelf ? "rounded-br-none justify-end" : "rounded-bl-none"
      )}
      style={isSelf ? {} : {}}
      onClick={(e) => {
        e.stopPropagation();
        handlePlayClick(url);
      }}
    >
      <span
        className={classNames("w-[16px] order-2", isSelf ? "rotate-180" : "")}
      >
        {playStatus === AudioStatus.LOADING ? (
          <SpinLoading
            style={{
              "--color": "#fff",
              "--size": "11px",
            }}
          />
        ) : (
          <VoiceAnimation isPlaying={playStatus === AudioStatus.PLAYING} />
        )}
      </span>
      <div
        className="text-white flex gap-1 items-center"
        style={isSelf ? { order: 1 } : { order: 3 }}
      >
        <span className="text-base">{duration}s</span>
      </div>
    </div>
  );
};
