import { VisibilityIcon, VisibilityOffIcon } from "@/components/svg-icon";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { ITipsNode } from "@/type/custom-message-node-data";
import { useState } from "react";

export const TipsNode = ({ data }: { data: ITipsNode }) => {
  const { viewableUserList, content, canHidden } = data;

  const [isHidden, setIsHidden] = useState(false);

  const { data: userInfo } = useFullUserInfo();

  if (viewableUserList && !viewableUserList.includes(userInfo?.id ?? "")) {
    // 如果当前用户不在可见用户列表中，则不显示
    return null;
  }

  return (
    <div className="p-[15px] w-full">
      <div
        className="flex gap-[5px] items-center justify-center"
        onClick={() => {
          if (canHidden) {
            setIsHidden(!isHidden);
          }
        }}
      >
        {isHidden ? (
          <span className="text-hint-color font-mono">*********</span>
        ) : (
          <span className="text-hint-color">{content}</span>
        )}
        {canHidden ? (
          isHidden ? (
            <VisibilityIcon size={15} className="text-hint-color" />
          ) : (
            <VisibilityOffIcon size={15} className="text-hint-color" />
          )
        ) : null}
      </div>
    </div>
  );
};
