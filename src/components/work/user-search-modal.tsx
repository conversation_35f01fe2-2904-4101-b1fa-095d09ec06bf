import { Input } from "@/ui/input";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/ui/scroll-area";
import { Divider, Popup } from "antd-mobile";
import { ScrollLoadData } from "../scroll-load-data";
import { userSearch } from "@/lib/api/user";
import { PageParams } from "@/type";
import { Fragment, useEffect, useRef, useState } from "react";
import { UserInfo } from "./user-info";
import { queryClient } from "@/provider/query-client";
import { User } from "@/type/user";

interface Props {
  open: boolean;
  onFinish: (user: User) => void;
  onClose: () => void;
}

export const UserSearchModal = (props: Props) => {
  const { open, onClose, onFinish } = props;

  const [keyword, setKeyword] = useState("");

  const inputRef = useRef<HTMLInputElement>(null);

  const queryKey = ["user-search", keyword];
  const queryFn = (params: PageParams) => userSearch({ keyword }, params);

  const enabled = !!keyword.trim();

  useEffect(() => {
    // 当 keyword 改变时，重置查询。不然会重新查询过多页（比如已经查询1-5页，不重置的话，也会都进行查询）
    queryClient.resetQueries({ queryKey: ["user-search", keyword] });
  }, [keyword]);

  const handleSelectUser = (user: User) => {
    onFinish(user);
  };

  return (
    <Popup
      destroyOnClose
      visible={open}
      bodyClassName="rounded-t-[10px] bg-app-bar-background h-[350px]"
      onMaskClick={onClose}
      onClose={onClose}
      afterClose={() => setKeyword("")}
      afterShow={() => inputRef.current?.focus()}
    >
      <div className="p-[15px] h-full flex flex-col gap-[15px]">
        <div className="text-center">
          <span className="text-[17px]">用户搜索</span>
        </div>
        <div>
          <Input
            ref={inputRef}
            value={keyword}
            placeholder="请输入用户昵称或用户ID"
            className="px-5 border-none text-[15px] rounded-[5px] bg-hint-color/10"
            onChange={(e) => setKeyword(e.target.value)}
          />
        </div>
        <div className="flex-1 h-0">
          {enabled ? (
            <ScrollArea className="h-full">
              <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
                {(data) => (
                  <div>
                    {data.map((item, index) => (
                      <Fragment key={item.id}>
                        <UserInfo
                          key={item.id}
                          user={item}
                          size={50}
                          onClick={() => handleSelectUser(item)}
                        />
                        {index !== data.length - 1 ? (
                          <Divider className="border-divider" />
                        ) : null}
                      </Fragment>
                    ))}
                  </div>
                )}
              </ScrollLoadData>
            </ScrollArea>
          ) : (
            <div className="h-full flex items-center justify-center">
              请输入用户名或用户id进行搜索
            </div>
          )}
        </div>
      </div>
    </Popup>
  );
};
