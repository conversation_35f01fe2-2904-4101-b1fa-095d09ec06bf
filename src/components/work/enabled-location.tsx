import { getLocation } from "@/utils/enabled-location";
import { Switch, Toast } from "antd-mobile";

interface Props {
  value?: boolean;
  onChange?: (value: boolean) => void;
}

export const EnabledLocation = ({ value, onChange }: Props) => {
  const handleChange = (value: boolean) => {
    const location = getLocation();

    if (value && (!location?.province || !location?.city)) {
      Toast.show("定位失败");
      onChange?.(false);
      return;
    }

    onChange?.(value);
  };

  return (
    <div className="flex flex-col items-end">
      <div>
        <Switch checked={value} onChange={handleChange} />
      </div>
      {value ? <span>定位结果: {getLocation().city}</span> : null}
    </div>
  );
};
