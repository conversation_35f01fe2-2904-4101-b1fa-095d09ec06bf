import { ReactNode, useRef, useState } from "react";
import { ThemeIcon } from "../theme-icon";
import { CommonIcon } from "../common-icon";
import { CloseIcon, SendIcon } from "../svg-icon";
import { Button } from "@/ui/button";
import { GiftModal } from "./gift-modal";
import { useModal } from "@/hooks/use-modal";
import { GiftResult } from "@/type/gift-result";
import { Editor, EditorRef } from "../editor";
import { VoiceRecordModal } from "./voice-record-modal";
import { useConfigList } from "@/hooks/use-config-list";
import { Badge } from "antd-mobile";
import { MediaUploadModal } from "./media-upload-modal";

export type FinishType = {
  text?: string;
  ats?: Record<string, string>;
  voice?: File;
  resource?: File[];
};

interface Props {
  showVoice?: boolean;
  showImage?: boolean;
  showGift?: boolean;
  quote?: {
    id: string;
    content: ReactNode;
  };
  action?: ReactNode;

  onClearQuote?: () => void;
  onGiveGift?: (gift: GiftResult, note?: string) => void;
  onFinish?: (data: FinishType) => Promise<void>;
}

export const CommentInput = (props: Props) => {
  const {
    showVoice = true,
    showImage = true,
    showGift = true,
    action,
    quote,
    onClearQuote,
    onGiveGift,
    onFinish,
  } = props;

  const [value, setValue] = useState("");
  const [atList, setAtList] = useState<Record<string, string>>({});
  const [voiceFile, setVoiceFile] = useState<File | null>(null);
  const [mediaFiles, setMediaFiles] = useState<File[]>([]);
  const editorRef = useRef<EditorRef>(null);

  const { getSystemConfig } = useConfigList();

  const minDuration = Number(
    getSystemConfig("COMMENT_PUBLISH_VOICE_MIN_DURATION")
  );
  const maxDuration = Number(
    getSystemConfig("COMMENT_PUBLISH_VOICE_MAX_DURATION")
  );
  const {
    open: openGift,
    openModal: openGiftModal,
    closeModal: closeGiftModal,
  } = useModal();

  const {
    open: openVoice,
    openModal: openVoiceModal,
    closeModal: closeVoiceModal,
  } = useModal();

  const {
    open: openMedia,
    openModal: openMediaModal,
    closeModal: closeMediaModal,
  } = useModal();

  const showSend = !!value.trim();

  const handleValueChange = (value: string) => {
    setValue(value);
  };

  const handleAtUserChange = (ats: Record<string, string>) => {
    setAtList(ats);
  };

  const handleClickSend = async () => {
    if (showSend) {
      await onFinish?.({
        text: value,
        ats: atList,
        voice: voiceFile || undefined,
        resource: mediaFiles,
      });

      // 清空输入框
      editorRef.current?.clear();
      setAtList({});
      setValue("");
      setVoiceFile(null);
      setMediaFiles([]);
      onClearQuote?.();
    }
  };

  const handleAddVoiceRecord = (file: File | null) => {
    setVoiceFile(file);
  };

  const handleAddMediaFile = (files: File[]) => {
    setMediaFiles(files);
  };

  return (
    <div className="relative w-full">
      {quote ? (
        <div className="absolute top-[-30px] left-0 w-full bg-scaffold-background text-xs pl-[7px] py-[7px] flex items-center">
          <span className="flex-1 w-0 line-clamp-1">{quote.content}</span>
          <span
            className="flex-none px-[15px]"
            onClick={() => onClearQuote?.()}
          >
            <CloseIcon size={15} className="text-hint-color" />
          </span>
        </div>
      ) : null}
      <div className="px-[15px] py-[7px] flex gap-2 items-center border-solid border-t border-divider">
        {showVoice ? (
          <Badge
            content={voiceFile ? Badge.dot : null}
            style={{ "--right": "2px", "--top": "2px" }}
          >
            <span onClick={openVoiceModal}>
              <ThemeIcon name="voice" w={27} h={27} />
            </span>
          </Badge>
        ) : null}
        {showImage ? (
          <Badge
            content={mediaFiles.length ? Badge.dot : null}
            style={{ "--right": "2px", "--top": "2px" }}
          >
            <span onClick={openMediaModal}>
              <ThemeIcon name="image" w={27} h={27} />
            </span>
          </Badge>
        ) : null}
        <div className="flex-1 w-0">
          <Editor
            ref={editorRef}
            placeholder="善语结善缘，恶语伤人心"
            wrapperClassName="bg-scaffold-background"
            className="min-h-[40px] max-h-[80px]"
            onChangeValue={handleValueChange}
            onChangeAtUsers={handleAtUserChange}
          />
        </div>
        {!showSend && showGift ? (
          <CommonIcon
            name="gift"
            w={27}
            h={30}
            onClick={(e) => {
              e.stopPropagation();
              openGiftModal();
            }}
          />
        ) : null}
        {!showSend && action ? action : null}
        {showSend ? (
          <Button
            variant="primary"
            className="w-[30px] h-[30px] rounded-full"
            onClick={handleClickSend}
          >
            <SendIcon size={15} color="#fff" className="-rotate-90" />
          </Button>
        ) : null}
      </div>
      <GiftModal open={openGift} onClose={closeGiftModal} onSend={onGiveGift} />
      <VoiceRecordModal
        open={openVoice}
        minDuration={minDuration}
        maxDuration={maxDuration}
        value={voiceFile}
        onClose={closeVoiceModal}
        onFinish={handleAddVoiceRecord}
      />
      <MediaUploadModal
        open={openMedia}
        value={mediaFiles}
        onChange={handleAddMediaFile}
        onClose={closeMediaModal}
      />
    </div>
  );
};
