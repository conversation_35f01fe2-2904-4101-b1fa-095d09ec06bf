import { Button } from "@/ui/button";
import { Dialog } from "antd-mobile";
import { CommonIcon } from "../common-icon";
import "./tips-dialog.less";

interface Options {
  title?: string;
  content: React.ReactNode;
  confirmText?: string;
  cancelText?: string;
  onCancel?: () => void;
  onConfirm?: () => void;
}

class TipsDialog {
  private baseDialog(type: "success" | "error", options: Options) {
    const { title, content, confirmText, cancelText, onCancel, onConfirm } =
      options;

    const { close } = Dialog.show({
      className: "tips-dialog",
      bodyClassName: "dialog-bg",
      closeOnMaskClick: true,
      title: (
        <div className="text-[20px] text-black font-normal pt-[30px]">
          {title ?? "提示信息"}
        </div>
      ),
      content: (
        <div>
          <div className="text-black text-sm text-center">{content}</div>
          <div className="flex mt-[15px] justify-evenly">
            {cancelText ? (
              <Button
                variant="primary"
                className="mx-[5px] text-base w-full h-[44px] bg-[#F0F0F0] text-[#728BA4]"
                onClick={() => {
                  close();
                  onCancel?.();
                }}
              >
                {cancelText}
              </Button>
            ) : null}
            {confirmText ? (
              <Button
                variant="primary"
                className="mx-[5px] text-base w-full h-[44px]"
                onClick={() => {
                  close();
                  onConfirm?.();
                }}
              >
                {confirmText}
              </Button>
            ) : null}
          </div>
          <div className="absolute w-full top-[-50px] left-0 flex justify-center">
            <CommonIcon
              name={type === "success" ? "tipsSuccess" : "tipsError"}
              h={97}
              w={192}
            />
          </div>
        </div>
      ),
    });
  }
  success(options: Options) {
    this.baseDialog("success", options);
  }
  error(options: Options) {
    this.baseDialog("error", options);
  }
}

export const tipsDialog = new TipsDialog();
