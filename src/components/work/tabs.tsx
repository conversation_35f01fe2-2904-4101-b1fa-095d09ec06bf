import classNames from "classnames";
import { communityTabBg } from "../image-list";

interface Props {
  tabs: { label: string }[];
  value: number;
  itemClassName?: string;
  onChange: (value: number) => void;
}

export const Tabs = (props: Props) => {
  const { tabs, value, onChange, itemClassName } = props;

  return (
    <div className="flex gap-2 items-center">
      {tabs.map((tab, index) => (
        <div
          key={index}
          className={classNames(
            "text-[#666] dark:text-foreground",
            value === index
              ? `bg-[length:50px_25px] bg-no-repeat bg-right text-lg font-medium`
              : "",
            itemClassName
          )}
          style={{
            backgroundImage:
              value === index ? `url(${communityTabBg})` : undefined,
          }}
          onClick={() => onChange(index)}
        >
          <div className="my-[5px] mr-[7px]">
            <span>{tab.label}</span>
          </div>
        </div>
      ))}
    </div>
  );
};
