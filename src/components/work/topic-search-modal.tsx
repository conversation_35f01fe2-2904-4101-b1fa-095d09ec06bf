import { useRef, useState } from "react";
import { Popup } from "antd-mobile";
import { Input } from "@/ui/input";
import { Button } from "@/ui/button";

interface Props {
  open: boolean;
  onClose: () => void;
  onFinish: (topic: string) => void;
}

export const TopicSearchModal = (props: Props) => {
  const { open, onClose, onFinish } = props;
  const [keyword, setKeyword] = useState("");

  const inputRef = useRef<HTMLInputElement>(null);

  const handleSelectTopic = () => {
    if (keyword.trim()) {
      onFinish(keyword);
    }
  };

  return (
    <Popup
      destroyOnClose
      visible={open}
      bodyClassName="rounded-t-[10px] bg-app-bar-background"
      onMaskClick={onClose}
      onClose={onClose}
      afterClose={() => setKeyword("")}
      afterShow={() => inputRef.current?.focus()}
    >
      <div className="p-[15px] h-full flex flex-col gap-[15px]">
        <div className="text-center">
          <span className="text-[17px]">话题</span>
        </div>
        <div>
          <Input
            ref={inputRef}
            value={keyword}
            placeholder="请输入话题"
            className="px-5 border-none text-[15px] rounded-[5px] bg-hint-color/10"
            onChange={(e) => setKeyword(e.target.value)}
          />
        </div>
        <div>
          <Button
            variant="primary"
            className="w-full"
            onClick={() => handleSelectTopic()}
          >
            完成
          </Button>
        </div>
      </div>
    </Popup>
  );
};
