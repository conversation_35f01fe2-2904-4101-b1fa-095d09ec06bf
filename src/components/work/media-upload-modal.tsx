import { Popup } from "antd-mobile";
import { Button } from "@/ui/button";
import { MediaUpload } from "./media-upload";

interface Props {
  open: boolean;
  maxCount?: number;
  value: File[];
  onChange: (files: File[]) => void;
  onClose: () => void;
}

export const MediaUploadModal = (props: Props) => {
  const { open, maxCount = 9, value, onChange, onClose } = props;

  return (
    <Popup
      destroyOnClose
      visible={open}
      bodyClassName="rounded-t-[10px] bg-app-bar-background h-[350px]"
      onMaskClick={onClose}
      onClose={onClose}
    >
      <div className="p-[15px] h-full flex flex-col gap-2">
        <MediaUpload maxCount={maxCount} value={value} onChange={onChange} />
        <Button variant="primary" className="w-full" onClick={onClose}>
          完成
        </Button>
      </div>
    </Popup>
  );
};
