import classNames from "classnames";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { PlusIcon } from "../svg-icon";

export interface TagSelectRef {
  onAddTag: (tag: { id: string; label: string }) => void;
}

interface Props {
  append: boolean;
  options: {
    id: string;
    label: string;
  }[];
  value?: string[];
  onChange?: (value: string[]) => void;
  moreClick?: () => void;
}

export const TagSelect = forwardRef<TagSelectRef, Props>((props, ref) => {
  const { append, options, value = [], onChange, moreClick } = props;

  const [tags, setTags] = useState<
    {
      id: string;
      label: string;
    }[]
  >(options || []);

  useEffect(() => {
    setTags(options || []);
  }, [options]);

  const handleSelectChange = (id: string) => {
    if (value.includes(id)) {
      onChange?.(value.filter((t) => t !== id));
    } else {
      onChange?.(value.concat(id));
    }
  };

  useImperativeHandle(ref, () => ({
    onAddTag: (tag: { id: string; label: string }) => {
      if (tags.some((t) => t.id === tag.id)) {
        return;
      }

      setTags((prev) => [...prev, tag]);
      onChange?.([...value, tag.id]);
    },
  }));

  return (
    <div className="flex gap-[10px] flex-wrap">
      {tags.map((tag) => (
        <div
          key={tag.id}
          className={classNames(
            "bg-scaffold-background text-[#999] rounded-[15px] px-[10px] py-[2px] border border-solid border-scaffold-background text-sm",
            value.includes(tag.id) && "bg-[#4E3DA8] border-[#4E3DA8] text-white"
          )}
          onClick={() => handleSelectChange(tag.id)}
        >
          {tag.label}
        </div>
      ))}
      {append ? (
        <div
          className="flex px-[10px] py-[2px] border border-solid border-[#4E3DA8] text-[#4E3DA8] rounded-[15px]"
          onClick={moreClick}
        >
          <PlusIcon size={12} />
          <span className="text-sm">更多</span>
        </div>
      ) : null}
    </div>
  );
});
