import { CommonCommentResult } from "@/type/common-comment-result";
import { CommonIcon } from "../common-icon";
import { UserAvatar } from "../user-avatar";
import { UserInfo } from "./user-info";
import { formatTimeLine } from "@/utils/format-time-line";
import dayjs from "dayjs";
import { Ellipsis } from "../ellipsis";
import { RichContent } from "../rich-content";
import { ResourceContent } from "../resource-content";
import { useMutation } from "@tanstack/react-query";
import { dynamicCommentLike } from "@/lib/api/dynamic";
import { useState } from "react";
import { produce } from "immer";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { UserInfoModal, UserInfoModalRef } from "./user-info-modal";
import {
  playbackVideoCommentLike,
  shortVideoCommentLike,
  videoCommentLike,
} from "@/lib/api/video";
import { useRef } from "react";
import { mindWallCommentLike } from "@/lib/api/mind-wall";
import { userTransactionCommentLike } from "@/lib/api/transaction";

interface Props {
  type:
    | "dynamic"
    | "video"
    | "short-video"
    | "playback-video"
    | "mind-wall"
    | "user-transaction";
  id: string;
  data: CommonCommentResult;
  showChildren?: boolean;
  onSetQuote?: (quote: CommonCommentResult) => void;
}

export const CommentItem = (props: Props) => {
  const { type, id, data: propsData, showChildren = true, onSetQuote } = props;

  const [data, setData] = useState(propsData);

  const { navigateRoute } = useNavigateRoute();
  const commentLikeMutations = useMutation({
    mutationFn: dynamicCommentLike,
  });

  const videoCommentLikeMutations = useMutation({
    mutationFn: videoCommentLike,
  });

  const shortVideoCommentLikeMutations = useMutation({
    mutationFn: shortVideoCommentLike,
  });

  const playbackVideoCommentLikeMutations = useMutation({
    mutationFn: playbackVideoCommentLike,
  });

  const mindWallCommentLikeMutations = useMutation({
    mutationFn: mindWallCommentLike,
  });

  const userTransactionCommentLikeMutations = useMutation({
    mutationFn: userTransactionCommentLike,
  });

  const handleClickLike = async () => {
    let ok = false;

    switch (type) {
      case "dynamic": {
        const res = await commentLikeMutations.mutateAsync({
          commentId: data.id!,
          like: !data.hasLike,
        });

        ok = res.ok;
        break;
      }
      case "video": {
        const res = await videoCommentLikeMutations.mutateAsync({
          commentId: data.id!,
          like: !data.hasLike,
        });

        ok = res.ok;
        break;
      }

      case "short-video": {
        const res = await shortVideoCommentLikeMutations.mutateAsync({
          commentId: data.id!,
          like: !data.hasLike,
        });
        ok = res.ok;
        break;
      }
      case "playback-video": {
        const res = await playbackVideoCommentLikeMutations.mutateAsync({
          commentId: data.id!,
          like: !data.hasLike,
        });
        ok = res.ok;
        break;
      }
      case "mind-wall": {
        const res = await mindWallCommentLikeMutations.mutateAsync({
          commentId: data.id!,
          like: !data.hasLike,
        });
        ok = res.ok;
        break;
      }
      case "user-transaction": {
        const res = await userTransactionCommentLikeMutations.mutateAsync({
          commentId: data.id!,
          like: !data.hasLike,
        });
        ok = res.ok;
        break;
      }
    }

    if (ok) {
      setData(
        produce((draft) => {
          draft.likeNumber = data.hasLike
            ? data.likeNumber! - 1
            : data.likeNumber! + 1;
          draft.hasLike = !data.hasLike;
        })
      );
    }
  };

  const handleClickCommentDetailed = () => {
    switch (type) {
      case "dynamic":
        navigateRoute(
          "/dynamic-comment-detailed",
          {
            targetId: id,
          },
          {
            state: data,
          }
        );
        break;
      case "video":
        navigateRoute(
          "/video-comment-detailed",
          {
            targetId: id,
          },
          {
            state: data,
          }
        );
        break;

      case "short-video":
        navigateRoute(
          "/short-video-comment-detailed",
          {
            targetId: id,
          },
          {
            state: data,
          }
        );
        break;
      case "playback-video":
        navigateRoute(
          "/playback-video-comment-detailed",
          {
            targetId: id,
          },
          {
            state: data,
          }
        );
        break;
      case "mind-wall":
        navigateRoute(
          "/mind-wall-comment-detailed",
          {
            targetId: id,
          },
          {
            state: data,
          }
        );
        break;
      case "user-transaction":
        navigateRoute(
          "/user-transaction-comment-detailed",
          {
            targetId: id,
          },
          {
            state: data,
          }
        );
        break;
    }
  };

  const userInfoModalRef = useRef<UserInfoModalRef>(null);

  const handleAtUserClick = (text: string) => {
    const match = text.match(/@([^#]+)\s/);
    const name = match?.[1];

    const target = Object.entries(data.ats ?? {}).find(
      ([, value]) => value === name
    );

    if (target) {
      userInfoModalRef.current?.open(target[0]);
    }
  };

  return (
    <div className="w-full">
      <div className="flex">
        <div className="flex-1">
          <UserInfo
            user={data.user ?? {}}
            tail={
              data.parent ? (
                <span className="text-hint-color text-xs">
                  回复 {data.parent?.user?.nickname}
                </span>
              ) : undefined
            }
          />
        </div>
        <div className="flex-none text-[13px] flex flex-col justify-center items-end">
          <span className="text-hint-color">
            {formatTimeLine(dayjs(data.releaseTime).valueOf())}
          </span>
          <span className="flex gap-1 items-center">
            <CommonIcon
              name={data.hasLike ? "like-selected" : "like"}
              w={12}
              h={12}
              onClick={handleClickLike}
            />
            <span className="text-hint-color">{data.likeNumber}</span>
          </span>
        </div>
      </div>
      <div
        className="ml-[63px] mt-[6px] flex flex-col gap-2"
        onClick={() => {
          onSetQuote?.(data);
        }}
      >
        <div>
          <div>
            <Ellipsis
              rows={2}
              expandText={<div className="text-[#9D64FF]">全文</div>}
              collapseText={<div className="text-[#9D64FF]">收起</div>}
              content={
                <RichContent
                  content={data.content ?? ""}
                  builds={[
                    "atTextBuild",
                    "emailTextBuild",
                    "ipTextBuild",
                    "phoneTextBuild",
                    "urlTextBuild",
                  ]}
                  onAtClick={handleAtUserClick}
                />
              }
            />
          </div>
          {/* 图片 ｜ 视频 */}
          {data.resources?.length ? (
            <ResourceContent resources={data.resources} />
          ) : null}
        </div>
        {showChildren && data.children?.length ? (
          <div
            className="flex flex-col gap-[5px] px-[18px] py-[15px] rounded-[5px] bg-[#f7f5fb] dark:bg-[#151B24]"
            onClick={handleClickCommentDetailed}
          >
            {data.children.map((item) => (
              <div key={item.id} className="flex gap-3">
                <div>
                  <UserAvatar
                    src={item.user?.avatarUrl ?? ""}
                    size={30}
                    isVip={item.user?.vip}
                  />
                </div>
                <div className="flex flex-col flex-1 justify-between">
                  <div className="flex items-center text-xs">
                    <span className="flex-1 text-[#9F89C7] line-clamp-1">
                      {item.user?.nickname}
                    </span>
                    <span className="text-hint-color">
                      {formatTimeLine(dayjs(item.releaseTime).valueOf())}
                    </span>
                  </div>
                  <div className="line-clamp-2 text-[13px]">
                    {item.voice ? "[语音]" : ""}
                    {item.resources?.some((r) => r.duration) ? "[视频]" : ""}
                    {item.resources?.some((r) => !r.duration) ? "[图片]" : ""}
                    {item.content}
                  </div>
                </div>
              </div>
            ))}
            {data.children.length >= 2 ? (
              <div className="text-[#9d64ff] text-[13px] pl-[42px]">
                查看更多评论
              </div>
            ) : null}
          </div>
        ) : null}
      </div>
      <UserInfoModal ref={userInfoModalRef} />
    </div>
  );
};
