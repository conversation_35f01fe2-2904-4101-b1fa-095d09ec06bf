import { Form, Input } from "antd-mobile";
import { CommonIcon } from "../common-icon";
import { FormProps } from "antd-mobile/es/components/form";
import "./navigation-search.less";

interface Props {
  name: string;
  form?: FormProps["form"];
  initialValue?: string;
  placeholder?: string;
  onFinish: (values: any) => void;
}

export const NavigationSearch = ({
  name,
  form,
  initialValue,
  placeholder,
  onFinish,
}: Props) => {
  return (
    <Form
      form={form}
      className="navigation-search-wrap flex-1 ml-[18px] bg-scaffold-background h-[32px] rounded-[16px] px-[16px]"
      initialValues={{ [name]: initialValue }}
      onFinish={(values) => {
        onFinish({ [name]: values[name] });
      }}
    >
      <div className="flex items-center h-[32px]">
        <CommonIcon name="search" w={13} h={13} />
        <Form.Item name={name} className="flex-1">
          <Input
            placeholder={placeholder}
            autoComplete="off"
            style={{
              "--font-size": "12px",
              "--color": "hsl(var(--foreground))",
              "--placeholder-color": "#666",
            }}
          />
        </Form.Item>
      </div>
    </Form>
  );
};
