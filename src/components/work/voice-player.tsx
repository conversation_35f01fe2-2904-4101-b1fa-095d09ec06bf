import { ReactNode, useState } from "react";
import VoiceAnimation from "../voice-animation";
import { audioManager, AudioStatus } from "@/utils/audio-manager";
import { SpinLoading } from "antd-mobile";

interface Props {
  url: string;
  duration: number;
  suffix?: ReactNode;
  maxPlayerDuration?: number;
  onPlayer?: () => void;
}

export const VoicePlayer = (props: Props) => {
  const { url, duration, suffix, maxPlayerDuration, onPlayer } = props;

  const [playStatus, setPlayStatus] = useState<AudioStatus>(
    AudioStatus.STOPPED
  );

  const handleStatusChange = (status: AudioStatus) => {
    console.log(status, url);
    setPlayStatus(status);
  };

  const handlePlayClick = (url: string) => {
    audioManager.play({
      url,
      maxPlayerDuration,
      statusCallback: handleStatusChange,
    });
    onPlayer?.();
  };

  return (
    <div
      className="w-full h-[35px] voice-play-bg flex items-center px-[15px] gap-[10px] rounded-[10px] rounded-bl-none"
      onClick={(e) => {
        e.stopPropagation();
        handlePlayClick(url);
      }}
    >
      <span className="w-[16px]">
        {playStatus === AudioStatus.LOADING ? (
          <SpinLoading
            style={{
              "--color": "#fff",
              "--size": "11px",
            }}
          />
        ) : (
          <VoiceAnimation isPlaying={playStatus === AudioStatus.PLAYING} />
        )}
      </span>
      <div className="text-white flex gap-1 items-center">
        <span className="text-base">{duration}s</span>
        {suffix}
      </div>
    </div>
  );
};
