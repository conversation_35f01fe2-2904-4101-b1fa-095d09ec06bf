.rtc-call-modal {
  .wave-container {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .wave-container::before,
  .wave-container::after {
    content: "";
    position: absolute;
    border: 10px solid rgba(135, 28, 254, 0.3);
    width: 100%;
    height: 100%;
    border-radius: 50%;
    animation: rtc-wave 3s linear infinite;
  }

  .wave-container::after {
    animation-delay: 1.5s;
  }

  .inner-circle {
    width: 77px;
    height: 77px;
    background-color: rgba(135, 28, 254, 1);
    border-radius: 50%;
  }
}

@keyframes rtc-wave {
  0% {
    transform: scale(0.6);
    opacity: 1;
  }
  100% {
    transform: scale(1.1);
    opacity: 0;
  }
}
