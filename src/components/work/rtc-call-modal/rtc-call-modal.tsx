import { callWaitBackground } from "@/components/image-list";
import { UserAvatar } from "@/components/user-avatar";
import { RtcRoomResult } from "@/type/rtc-room-result";
import { RtcCallState, RtcRoomType } from "@/utils/enums";
import { Image, Mask } from "antd-mobile";
import { forwardRef, useImperativeHandle, useMemo, useState } from "react";
import { RtcC2CWait } from "./rtc-c2c-wait";
import "./rtc-call-modal.less";
import { RtcC2CTalking } from "./rtc-c2c-talking";
import rtcLogic from "@/utils/rtc-logic";
import { mainVideoId, secondaryVideoId } from "@/utils/constant";
import { LocalUser } from "agora-rtc-react";

interface Props {}

export interface RtcCallModalRef {
  show: () => void;
  close: () => void;
  setRoom: (room: RtcRoomResult | null) => void;
  setSponsor: (sponsor: boolean) => void;
  setState: (state: RtcCallState) => void;
  setDuration: (duration: number) => void;
  setExpandInfo: (expandInfo: { tips: string } | null) => void;
  setEnabledMicrophone: (enabledMicrophone: boolean) => void;
  setEnabledSpeaker: (enabledSpeaker: boolean) => void;
  setFrontCamera: (frontCamera: boolean) => void;
}

export const RtcCallModal = forwardRef<RtcCallModalRef, Props>(
  (_props, ref) => {
    const [visible, setVisible] = useState(false);

    const [room, setRoom] = useState<RtcRoomResult | null>(null);
    const [state, setState] = useState<RtcCallState>(RtcCallState.Idle);
    const [sponsor, setSponsor] = useState(false);
    const [duration, setDuration] = useState(0);
    const [expandInfo, setExpandInfo] = useState<{ tips: string } | null>(null);
    const [enabledMicrophone, setEnabledMicrophone] = useState(false);
    const [enabledSpeaker, setEnabledSpeaker] = useState(false);
    const [frontCamera, setFrontCamera] = useState(false);
    const [mainVideo, setMainVideo] = useState<"local" | "remote">("local");

    const user = sponsor ? room?.calledPartyUser : room?.callingPartyUser;

      const audioTrack = useLocalAudioTrack();
  const videoTrack = useLocalCameraTrack();

        const renderMainVideo = useMemo(() => <LocalUser
          audioTrack={audioTrack}
          cameraOn
          micOn
          playAudio
          playVideo
          videoTrack={videoTrack}
        />
    , []);

    useImperativeHandle(ref, () => ({
      show: () => {
        setVisible(true);
      },
      setState: (state: RtcCallState) => {
        setState(state);
      },
      close: () => {
        setVisible(false);
      },
      setDuration: (duration: number) => {
        setDuration(duration);
      },
      setExpandInfo: (expandInfo: { tips: string } | null) => {
        setExpandInfo(expandInfo);
      },
      setRoom: (room: RtcRoomResult | null) => {
        setRoom(room);
      },
      setSponsor: (sponsor: boolean) => {
        setSponsor(sponsor);
      },
      setEnabledMicrophone: (enabledMicrophone: boolean) => {
        setEnabledMicrophone(enabledMicrophone);
      },
      setEnabledSpeaker: (enabledSpeaker: boolean) => {
        setEnabledSpeaker(enabledSpeaker);
      },
      setFrontCamera: (frontCamera: boolean) => {
        setFrontCamera(frontCamera);
      },
    }));

    const renderContent = useMemo(() => {
      if (room) {
        if ([RtcCallState.Wait, RtcCallState.Connecting].includes(state)) {
          return (
            <RtcC2CWait
              state={state}
              sponsor={sponsor}
              room={room}
              frontCamera={frontCamera}
            />
          );
        }
        if (state === RtcCallState.Talking) {
          return (
            <RtcC2CTalking
              room={room}
              expandInfo={expandInfo}
              duration={duration}
              enabledMicrophone={enabledMicrophone}
              enabledSpeaker={enabledSpeaker}
              frontCamera={frontCamera}
            />
          );
        }
      }

      return <div className="flex justify-center">通话已结束</div>;
    }, [
      duration,
      enabledMicrophone,
      enabledSpeaker,
      expandInfo,
      frontCamera,
      room,
      sponsor,
      state,
    ]);

    return (
      <Mask
        visible={visible}
        afterShow={() => {
          if (visible && room?.type === RtcRoomType.Video) {
            rtcLogic.startLocalVideo();
          }
        }}
        onMaskClick={() => setVisible(false)}
      >
        <div className="rtc-call-modal absolute top-0 left-0 w-full h-full bg-scaffold-background flex flex-col">
          {room?.type === RtcRoomType.Audio ? (
            <div className="absolute top-0 left-0 w-full h-full">
              <Image src={callWaitBackground} width="100%" height="100%" />
            </div>
          ) : null}
          {room?.type === RtcRoomType.Video ? (
            <>
              <div className="absolute top-0 left-0 w-full h-full">
                <div className="w-full h-full" id={mainVideoId} />
              </div>
              <div className="absolute top-4 right-4 w-[40%] aspect-[9/16] z-10 rounded-sm">
                <div
                  className="w-full h-full"
                  id={secondaryVideoId}
                  onClick={() => rtcLogic.switchVideoView()}
                />
              </div>
            </>
          ) : null}
          {room?.type === RtcRoomType.Audio ? (
            <>
              <div className="h-[140px]" />
              <div className="flex flex-col gap-5">
                <div className="relative z-10 flex justify-center">
                  <UserAvatar src={user?.avatarUrl || ""} size={116} />
                  <div className="wave-container absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[160px] h-[160px] z-[-1]">
                    <div className="inner-circle"></div>
                  </div>
                </div>
                <span className="text-[18px] text-white flex justify-center">
                  {user?.nickname}
                </span>
              </div>
            </>
          ) : null}
          {renderContent}
        </div>
      </Mask>
    );
  }
);
