import { RtcCallState, RtcRoomType } from "@/utils/enums";
import { RtcRoomResult } from "@/type/rtc-room-result";
import { CommonIcon } from "@/components/common-icon";
import rtcLogic from "@/utils/rtc-logic";

interface Props {
  state: RtcCallState;
  sponsor: boolean;
  frontCamera: boolean;
  room: RtcRoomResult;
}

export const RtcC2CWait = (props: Props) => {
  const { state, sponsor, room, frontCamera } = props;

  return (
    <div className="absolute bottom-[70px] w-full">
      <div className="flex flex-col gap-[10px] items-center text-sm text-[#9d64ff]">
        {state === RtcCallState.Connecting ? <span>正在连接...</span> : null}
        {state === RtcCallState.Wait && !sponsor ? (
          <span>
            邀请您进入{room.type == RtcRoomType.Video ? "视频" : "语音"}通话
          </span>
        ) : null}
        {state === RtcCallState.Wait && sponsor ? (
          <span>等待对方接听...</span>
        ) : null}
        <div className="flex justify-evenly w-full">
          {sponsor ? (
            <div
              className="flex flex-col gap-[10px] items-center"
              onClick={() => rtcLogic.cancel()}
            >
              <CommonIcon name="rtcHangUp" w={64} h={64} />
              <span className="text-white text-[15px]">取消</span>
            </div>
          ) : null}
          {!sponsor ? (
            <div
              className="flex flex-col gap-[10px] items-center"
              onClick={() => rtcLogic.reject()}
            >
              <CommonIcon name="rtcHangUp" w={64} h={64} />
              <span className="text-white text-[15px]">拒绝</span>
            </div>
          ) : null}
          {room.type === RtcRoomType.Video ? (
            <div
              className="flex flex-col gap-[10px] items-center"
              onClick={() => rtcLogic.switchCamera(!frontCamera)}
            >
              <CommonIcon name="rtcFlip" w={64} h={64} />
              <span className="text-white text-[15px]">翻转</span>
            </div>
          ) : null}
          {!sponsor ? (
            <div
              className="flex flex-col gap-[10px] items-center"
              onClick={() => rtcLogic.accepted()}
            >
              <CommonIcon name="rtcAnswer" w={64} h={64} />
              <span className="text-white text-[15px]">接听</span>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};
