import { CommonIcon } from "@/components/common-icon";
import { RtcRoomResult } from "@/type/rtc-room-result";
import { RtcRoomType } from "@/utils/enums";
import { formatDuration } from "@/utils/format-duration";
import rtcLogic from "@/utils/rtc-logic";

interface Props {
  room: RtcRoomResult;
  expandInfo: { tips: string } | null;
  duration: number;
  enabledMicrophone: boolean;
  enabledSpeaker: boolean;
  frontCamera: boolean;
}

export const RtcC2CTalking = (props: Props) => {
  const { room, expandInfo, duration, frontCamera } = props;

  return (
    <div className="absolute bottom-[70px] w-full">
      <div className="flex flex-col gap-[15px] items-center">
        <span className="text-sm text-white">{expandInfo?.tips}</span>
        <span className="text-sm text-white">{formatDuration(duration)}</span>
        <div className="flex justify-evenly w-full">
          <div
            className="flex flex-col gap-[10px] items-center"
            onClick={() => rtcLogic.switchMicrophone(!props.enabledMicrophone)}
          >
            <CommonIcon
              name={
                props.enabledMicrophone ? "rtcMicrophoneOn" : "rtcMicrophoneOff"
              }
              w={64}
              h={64}
            />
            <span className="text-white text-[15px]">静音</span>
          </div>
          <div
            className="flex flex-col gap-[10px] items-center"
            onClick={() => rtcLogic.hangUp()}
          >
            <CommonIcon name="rtcHangUp" w={64} h={64} />
            <span className="text-white text-[15px]">挂断</span>
          </div>
          {room.type === RtcRoomType.Audio ? (
            <div
              className="flex flex-col gap-[10px] items-center"
              onClick={() => rtcLogic.switchSpeaker(!props.enabledSpeaker)}
            >
              <CommonIcon
                name={props.enabledSpeaker ? "rtcSpeakerOn" : "rtcSpeakerOff"}
                w={64}
                h={64}
              />
              <span className="text-white text-[15px]">扬声器</span>
            </div>
          ) : null}
          {room.type === RtcRoomType.Video ? (
            <div
              className="flex flex-col gap-[10px] items-center"
              onClick={() => rtcLogic.switchCamera(!frontCamera)}
            >
              <CommonIcon name="rtcFlip" w={64} h={64} />
              <span className="text-white text-[15px]">翻转</span>
            </div>
          ) : null}
          {/* 美颜暂时没处理 */}
          {/* {room.type === RtcRoomType.Video ? (
            <div className="flex flex-col gap-[10px] items-center">
              <CommonIcon name="rtcBeauty" w={64} h={64} />
              <span className="text-white text-[15px]">美颜</span>
            </div>
          ) : null} */}
        </div>
      </div>
    </div>
  );
};
