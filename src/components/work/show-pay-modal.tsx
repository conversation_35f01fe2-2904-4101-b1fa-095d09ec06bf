import { Popup } from "antd-mobile";
import { PayChannelResult } from "@/type/pay-channel-result";
import { PayModal } from "./pay-modal";
import { createRoot, Root } from "react-dom/client";

export const showPayModal = (options: {
  data: PayChannelResult[];
  onSelect: (channel: PayChannelResult) => void;
  onClose?: () => void;
}) => {
  const div = document.createElement("div");
  document.body.appendChild(div);
  const root: Root = createRoot(div);

  const destroy = () => {
    root.unmount();
    if (div.parentNode) {
      div.parentNode.removeChild(div);
    }
  };

  root.render(
    <Popup
      visible={true}
      onClose={() => {
        options?.onClose?.();
        destroy();
      }}
      bodyClassName="bg-transparent"
    >
      <PayModal
        data={options.data ?? []}
        onClose={() => {
          options.onClose?.();
          destroy();
        }}
        onSelect={(channel) => {
          options.onSelect(channel);
          destroy();
        }}
      />
    </Popup>
  );
};
