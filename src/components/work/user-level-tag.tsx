import { useUserLevelConfig } from "@/hooks/use-user-level-config";
import { User } from "@/type/user";
import { UserGender, UserLevelType, userLevelTypeConfig } from "@/utils/enums";
import { Image } from "antd-mobile";

export const UserLevelTag = ({ user }: { user: User }) => {
  const { getByLevelAndType } = useUserLevelConfig();

  const levelType =
    user.gender == UserGender.male
      ? UserLevelType.heroism
      : UserLevelType.charm;
  const level =
    user.gender === UserGender.male ? user.heroismGrade : user.charmGrade;
  const levelConfig = getByLevelAndType(level ?? 0, levelType);
  const { colors } = userLevelTypeConfig[levelType];

  if (!levelConfig) return null;

  return (
    <div
      className="h-[18px] rounded-[10px] flex items-center relative flex-none"
      style={{
        background: `linear-gradient(90deg, ${colors[0]} 0%, ${colors[1]} 100%)`,
      }}
    >
      <div className="absolute h-[25px] left-[-8px] top-[-5px]">
        <Image
          src={levelConfig.icon ?? ""}
          width="auto"
          height="100%"
          fit="cover"
          placeholder={null}
        />
      </div>
      <span className="rounded-[20px] flex items-center bg-black/10 pl-[28px] pr-[5px]">
        <span className="text-[18px]" style={{ zoom: 0.5 }}>
          {levelConfig.level}
        </span>
      </span>
      <span className="rounded-[20px] flex items-center text-white px-[5px]">
        <span className="text-[20px]" style={{ zoom: 0.5 }}>
          {levelConfig.name}
        </span>
      </span>
    </div>
  );
};
