import { Filter } from "@/type/layout-config";
import { <PERSON><PERSON> } from "@/ui/button";
import { ScrollArea } from "@/ui/scroll-area";
import { Popup } from "antd-mobile";
import { useEffect, useState } from "react";

interface Props {
  visible: boolean;
  title: string;
  data: Filter[];
  value: Record<string, any>;
  onClose: () => void;
  onChange: (data: Record<string, any>) => void;
}

export const FilterPopup = (props: Props) => {
  const { visible, onClose, data, value: initValue, onChange, title } = props;
  const [value, setValue] = useState<Record<string, any>>({});

  useEffect(() => {
    if (visible) {
      setValue(initValue);
    }
  }, [visible, initValue]);

  const handleOk = () => {
    onChange(value);
    onClose();
  };

  return (
    <Popup
      visible={visible}
      bodyStyle={{
        borderTopLeftRadius: "10px",
        borderTopRightRadius: "10px",
        height: "60vh",
      }}
      bodyClassName="bg-app-bar-background"
      onMaskClick={onClose}
    >
      <div className="flex flex-col mx-[15px] h-full">
        <div className="flex flex-none justify-center items-center py-[15px] text-[17px] font-bold">
          {title}
        </div>
        <div className="flex-1 h-0">
          <ScrollArea className="h-full">
            <div className="flex flex-col gap-[10px]">
              {data.map((item, index) => (
                <div key={index} className="flex flex-col gap-2">
                  <span className="text-[13px]">{item.label}</span>
                  <div className="flex gap-2 flex-wrap">
                    {item.value.map((v, i) => (
                      <div
                        key={i}
                        style={
                          v.value === value[item.key]
                            ? { background: "#4E3DA8", color: "#fff" }
                            : {}
                        }
                        className="flex justify-center bg-scaffold-background text-[15px] rounded-[5px] py-2 px-[5px] min-w-[80px] text-foreground"
                        onClick={() =>
                          setValue({ ...value, [item.key]: v.value })
                        }
                      >
                        {v.label}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
        <div className="flex flex-none gap-[25px] pt-[10px] pb-[15px]">
          <Button
            variant="secondary"
            className="flex-1 h-[44px] text-[15px]"
            onClick={onClose}
          >
            取消
          </Button>
          <Button
            variant="primary"
            className="flex-1 h-[44px] text-[15px]"
            onClick={handleOk}
          >
            完成
          </Button>
        </div>
      </div>
    </Popup>
  );
};
