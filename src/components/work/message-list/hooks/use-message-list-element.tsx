import { PropsWithChildren, useMemo } from "react";
import { TUIMessageProps, UnknowPorps } from "@tencentcloud/chat-uikit-react";
import { getTimeForMessage } from "@/utils/format-time";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { IMessageModel } from "@tencentcloud/chat-uikit-engine";

interface MessageListElementProps {
  enrichedMessageList: IMessageModel[];
  TUIMessage?: React.ComponentType<TUIMessageProps | UnknowPorps>;
  intervalsTimer?: number;
}

function useMessageListElement<T extends MessageListElementProps>(
  props: PropsWithChildren<T>
) {
  const { enrichedMessageList, TUIMessage, intervalsTimer } = props;

  const { data: userInfo } = useFullUserInfo();

  return useMemo(() => {
    const formatMessageList = enrichedMessageList?.filter(
      (item: IMessageModel) => {
        try {
          const { viewableUserList } = JSON.parse(item.payload.data ?? "{}");

          if (viewableUserList?.length) {
            return viewableUserList.includes(userInfo?.id ?? "");
          }
        } catch (error) {
          console.error(error);
        }

        return true;
      }
    );

    return formatMessageList.map((item: IMessageModel, index: number) => {
      const key = `${item.ID}`;

      const preMessageTImer =
        index > 0 ? formatMessageList[index - 1]?.time : -1;

      const currrentTimer = item?.time || 0;

      let isShowIntervalsTimer =
        intervalsTimer && preMessageTImer !== -1
          ? currrentTimer - preMessageTImer >= intervalsTimer
          : false;

      isShowIntervalsTimer =
        preMessageTImer === -1 ? true : isShowIntervalsTimer;

      return (
        <li key={key} className="flex flex-col" data-message-id={item.ID}>
          {isShowIntervalsTimer ? (
            <div
              className="py-[10px] text-center"
              key={`${currrentTimer + index}`}
            >
              {currrentTimer ? getTimeForMessage(currrentTimer) : 0}
            </div>
          ) : null}
          {TUIMessage ? <TUIMessage message={item} /> : null}
        </li>
      );
    });
  }, [TUIMessage, enrichedMessageList, intervalsTimer, userInfo?.id]);
}

export default useMessageListElement;
