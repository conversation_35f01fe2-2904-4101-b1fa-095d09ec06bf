import {
  PropsWithChildren,
  useState,
  useEffect,
  memo,
  ReactElement,
  useRef,
  useLayoutEffect,
} from "react";
import { InfiniteScroll, InfiniteScrollProps } from "../infinite-scroll";
import useMessageListElement from "./hooks/use-message-list-element";
import {
  useComponentContext,
  useTUIChatActionContext,
  useTUIChatStateContext,
} from "@tencentcloud/chat-uikit-react";
import { IMessageModel } from "@tencentcloud/chat-uikit-engine";

export interface MessageListProps extends InfiniteScrollProps {
  className?: string;
  messageList?: IMessageModel[];
  highlightedMessageId?: string;
  intervalsTimer?: number;
}

function TUIMessageListWithContext<T extends MessageListProps>(
  props: PropsWithChildren<T>
): React.ReactElement {
  const {
    highlightedMessageId: propsHighlightedMessageId,
    loadMore: propsLoadMore,
    intervalsTimer: propsIntervalsTimer,
  } = props;

  const [ulElement, setUlElement] = useState<HTMLUListElement | null>(null);
  const [firstRender, setFirstRender] = useState<boolean>(false);

  const {
    messageList: contextMessageList,
    highlightedMessageId: contextHighlightedMessageId,
    isCompleted,
    messageListRef,
    noMore,
    TUIMessageListConfig,
  } = useTUIChatStateContext("TUIMessageList");
  const { loadMore: contextLoadMore, setHighlightedMessageId } =
    useTUIChatActionContext("TUIMessageList");
  const { TUIMessage } = useComponentContext("TUIMessageList");

  const highlightedMessageId =
    propsHighlightedMessageId ||
    TUIMessageListConfig?.highlightedMessageId ||
    contextHighlightedMessageId;

  const intervalsTimer =
    (propsIntervalsTimer || TUIMessageListConfig?.intervalsTimer || 5) * 60;

  const oldHeightRef = useRef(0);

  const loadMore = async () => {
    await (
      propsLoadMore ||
      TUIMessageListConfig?.loadMore ||
      contextLoadMore
    )?.();

    oldHeightRef.current = messageListRef?.current?.scrollHeight || 0;
  };

  const elements = useMessageListElement({
    enrichedMessageList: (contextMessageList as IMessageModel[]) || [],
    TUIMessage,
    intervalsTimer,
  });

  useEffect(() => {
    (async () => {
      const parentElement = ulElement?.parentElement?.parentElement;
      if (
        !isCompleted &&
        parentElement &&
        parentElement?.clientHeight >= ulElement?.clientHeight
      ) {
        loadMore && (await loadMore());
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [elements, firstRender]);

  useLayoutEffect(() => {
    if (ulElement?.children) {
      if (messageListRef?.current) {
        messageListRef.current.scrollTop =
          messageListRef.current.scrollHeight - oldHeightRef.current;
      }
      setFirstRender(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [elements]);

  useEffect(() => {
    if (highlightedMessageId) {
      const element = ulElement?.querySelector(
        `[data-message-id='${highlightedMessageId}']`
      );
      if (!element) {
        return;
      }
      const { children } = element;
      children[children.length - 1].classList.add("high-lighted");
      if (messageListRef?.current) {
        const highlightedMessageRect = element.getBoundingClientRect();
        const messageListRect = messageListRef.current.getBoundingClientRect();
        const finalScrollTop =
          highlightedMessageRect.top -
          messageListRect.top +
          messageListRef.current.scrollTop;
        messageListRef.current.scrollTop = finalScrollTop;
      }
      const timer = setTimeout(() => {
        children[children.length - 1].classList.remove("high-lighted");
        clearTimeout(timer);
        setHighlightedMessageId && setHighlightedMessageId("");
      }, 1000);
    }
  }, [
    highlightedMessageId,
    messageListRef,
    setHighlightedMessageId,
    ulElement,
  ]);

  return (
    <div
      className={`flex-1 h-0 py-[15px] overflow-y-auto ${
        !firstRender ? "hide" : ""
      }`}
      ref={messageListRef}
    >
      {noMore}
      <InfiniteScroll hasMore loadMore={loadMore} threshold={1}>
        <ul ref={setUlElement}>
          {elements?.length && elements.length > 0 ? elements : null}
        </ul>
      </InfiniteScroll>
    </div>
  );
}

const MemoizedTUIMessageListContext = memo(
  TUIMessageListWithContext
) as typeof TUIMessageListWithContext;

export function MessageList(props: MessageListProps): ReactElement {
  return <MemoizedTUIMessageListContext {...props} />;
}
