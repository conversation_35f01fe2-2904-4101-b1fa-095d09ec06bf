import dayjs from "dayjs";
import { UserInfo } from "./user-info";
import { Ellipsis } from "../ellipsis";
import { CommonIcon } from "../common-icon";
import { UserAvatar } from "../user-avatar";
import { ResourceContent } from "../resource-content";
import { DynamicResult } from "@/type/dynamic-result";
import { formatTimeLine } from "@/utils/format-time-line";
import { RichContent } from "../rich-content";
import {
  DynamicState,
  ShareTargetType,
  ShareType,
  userWalletType,
} from "@/utils/enums";
import { AspectRatio } from "@/ui/aspect-ratio";
import { Image, Toast } from "antd-mobile";
import { Button } from "@/ui/button";
import { ScrollArea, ScrollBar } from "@/ui/scroll-area";
import { DefaultLoadImage } from "../default-load-image";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import classNames from "classnames";
import { useMutation } from "@tanstack/react-query";
import { dynamicBuy, dynamicCollect, dynamicLike } from "@/lib/api/dynamic";
import { useState } from "react";
import { produce } from "immer";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { cancelCollection } from "@/lib/api/user";
import { useModal } from "@/hooks/use-modal";
import { ShareModal } from "./share-modal";
import { confirmDialog } from "./confirm-dialog";
import { UserInfoModal, UserInfoModalRef } from "./user-info-modal";
import { getRoute } from "@/router/route-map";
import { VoicePlayer } from "./voice-player";
import { useConfigList } from "@/hooks/use-config-list";
import { useRef } from "react";

interface Props {
  showComment?: boolean;
  canJump?: boolean;
  data: DynamicResult;
  secondaryColor?: string;
}

/**
 * 避免接口请求过于频繁。本地处理状态更新结果
 */
export const DynamicItem = (props: Props) => {
  const {
    showComment = true,
    canJump = true,
    data: propsData,
    secondaryColor,
  } = props;

  const [data, setData] = useState(propsData);
  const { user, publicContent } = data;

  const { navigateRoute } = useNavigateRoute();
  const { data: userInfo } = useFullUserInfo();
  const { getSystemConfig } = useConfigList();

  const {
    open: openShare,
    openModal: openShareModal,
    closeModal: closeShareModal,
  } = useModal();

  const dynamicLikeMutations = useMutation({
    mutationFn: dynamicLike,
  });

  const handleClickLike = async () => {
    const { ok } = await dynamicLikeMutations.mutateAsync({
      id: data.id,
      like: !data.hasLike,
    });

    if (ok) {
      setData(
        produce((draft) => {
          draft.hasLike = !draft.hasLike;
          if (draft.hasLike) {
            if (!draft.likes) {
              draft.likes = [];
            }
            // 点赞人
            draft.likes.push(userInfo!);
            // 点赞数 +1
            draft.likeNumber = draft.likeNumber! + 1;
          } else {
            draft.likes = draft.likes?.filter(
              (item) => item.id !== userInfo?.id
            );
            draft.likeNumber = draft.likeNumber! - 1;
          }
        })
      );
    }
  };

  const collectionMutations = useMutation({
    mutationFn: dynamicCollect,
  });

  const cancelCollectionMutations = useMutation({
    mutationFn: cancelCollection,
  });

  const handleClickCollect = async () => {
    Toast.show({
      content: "请稍后...",
      duration: 0,
    });

    if (data.hasCollect) {
      const { ok } = await cancelCollectionMutations.mutateAsync({
        targetId: data.id,
      });

      if (ok) {
        setData(
          produce((draft) => {
            draft.hasCollect = false;
          })
        );
      }
    } else {
      const { ok } = await collectionMutations.mutateAsync({
        id: data.id,
      });

      if (ok) {
        setData(
          produce((draft) => {
            draft.hasCollect = true;
          })
        );
      }
    }
  };

  const handleClick = () => {
    if (canJump) {
      navigateRoute("/dynamic-detail", { id: data.id });
    }
  };

  const handleClickLikeList = () => {
    navigateRoute("/dynamic-like-list", { dynamicId: data.id });
  };

  const handleShareSuccess = () => {
    setData(
      produce((draft) => {
        draft.shareNumber = draft.shareNumber! + 1;
      })
    );
  };

  const dynamicBuyMutations = useMutation({
    mutationFn: dynamicBuy,
  });

  const handleBuyClick = async () => {
    const result = await confirmDialog.show(
      "（购买后可无限次阅读）",
      `购买该动态需要支付${data.charge?.price ?? 0}${
        userWalletType.currency.label
      }`
    );

    if (result) {
      Toast.show({
        icon: "loading",
        content: "请稍后...",
        duration: 0,
      });

      const { ok } = await dynamicBuyMutations.mutateAsync({
        id: data.id,
      });

      if (ok) {
        Toast.show({
          content: "购买成功",
        });

        setData(
          produce((draft) => {
            if (!draft.charge) {
              draft.charge = {};
            }

            draft.charge.buy = true;
          })
        );
      }
    }
  };

  const handleTopicClick = (topic: string) => {
    const match = topic.match(/#([^#]+)#/);

    navigateRoute("/topic-detail", {
      name: match?.[1] ?? topic,
    });
  };

  const userInfoModalRef = useRef<UserInfoModalRef>(null);

  const handleAtUserClick = (text: string) => {
    const match = text.match(/@([^#]+)\s/);
    const name = match?.[1];

    const target = Object.entries(data.ats ?? {}).find(
      ([, value]) => value === name
    );

    if (target) {
      userInfoModalRef.current?.open(target[0]);
    }
  };

  const handleMentionClick = (uri?: string) => {
    if (uri) {
      navigateRoute(getRoute(uri));
    }
  };

  // 试听--存在收费音频 & 未付费
  const isAudition =
    data.charge?.resources?.includes(data.voice?.id!) || data.charge?.buy;

  return (
    <div className="flex flex-col gap-3 relative" onClick={handleClick}>
      {user ? <UserInfo user={user} /> : null}
      {/* 语音相关 */}
      {data.voice?.url ? (
        <div className="w-[200px]">
          <VoicePlayer
            url={data.voice.url}
            duration={data.voice.duration ?? 0}
            maxPlayerDuration={
              isAudition
                ? Number(getSystemConfig("DYNAMIC_VOICE_AUDITION_DURATION"))
                : undefined
            }
            suffix={
              isAudition ? (
                <span className="text-xs">试听(购买后解锁)</span>
              ) : null
            }
          />
        </div>
      ) : null}
      {/* 文字内容 */}
      <div>
        <Ellipsis
          rows={2}
          expandText={<div className="text-[#9D64FF]">全文</div>}
          collapseText={<div className="text-[#9D64FF]">收起</div>}
          content={
            <RichContent
              content={publicContent ?? ""}
              builds={[
                "atTextBuild",
                "topicTextBuild",
                "emailTextBuild",
                "ipTextBuild",
                "phoneTextBuild",
                "urlTextBuild",
              ]}
              onTopicClick={handleTopicClick}
              onAtClick={handleAtUserClick}
            />
          }
        />
      </div>
      {data.charge && data.paidContent ? (
        <div
          className={classNames(
            "rounded-[10px] p-[10px]",
            secondaryColor ? secondaryColor : "bg-app-bar-background"
          )}
        >
          {data.charge.buy ? (
            <Ellipsis
              rows={2}
              expandText={<div className="text-[#9D64FF]">全文</div>}
              collapseText={<div className="text-[#9D64FF]">收起</div>}
              content={
                <RichContent
                  content={data.paidContent ?? ""}
                  builds={[
                    "atTextBuild",
                    "topicTextBuild",
                    "emailTextBuild",
                    "ipTextBuild",
                    "phoneTextBuild",
                    "urlTextBuild",
                  ]}
                />
              }
            />
          ) : (
            <span className="flex justify-center items-center text-[#734EC4]">
              剩余{data.paidContent?.length}个字属于收费内容，购买后即可阅读
            </span>
          )}
        </div>
      ) : null}
      {/* 图片 ｜ 视频 */}
      {data.resources?.length ? (
        <ResourceContent resources={data.resources} charge={data.charge} />
      ) : null}
      {/* 提及 */}
      {data.mention ? (
        <div
          className={classNames(
            "rounded-[10px] p-[10px] flex gap-2",
            secondaryColor ? secondaryColor : "bg-app-bar-background"
          )}
          onClick={() => handleMentionClick(data.mention?.targetUrl)}
        >
          {data.mention?.cover ? (
            <div className="w-[60px] flex-none">
              <AspectRatio ratio={1}>
                <Image
                  width="100%"
                  height="100%"
                  src={data.mention.cover}
                  fit="cover"
                  placeholder={<DefaultLoadImage />}
                  fallback={<DefaultLoadImage />}
                  lazy
                  className="rounded-[5px]"
                />
              </AspectRatio>
            </div>
          ) : null}
          <div className="flex-1 w-0 py-1">
            <div className="flex gap-[5px]">
              <span className="flex-none bg-[#FF595D] rounded-[3px] px-[5px] flex items-center">
                <span className="text-[22px]" style={{ zoom: 0.5 }}>
                  {data.mention.targetTitle}
                </span>
              </span>
              <span className="text-xs line-clamp-1">{data.mention.title}</span>
            </div>
            <div className="text-[13px] text-hint-color line-clamp-2">
              {data.mention.subtitle}
            </div>
          </div>
        </div>
      ) : null}
      {/* 付费标识 */}
      {data.charge ? (
        <div className="flex justify-center py-1">
          {data.charge.buy ? (
            <span className="flex justify-center items-center text-[#734EC4]">
              你已购买该动态，可无限次阅读
            </span>
          ) : (
            <Button
              variant="primary"
              size="lg"
              className="text-base font-normal px-5 h-[44px] w-full"
              onClick={(e) => {
                e.stopPropagation();
                handleBuyClick();
              }}
            >
              支付{data.charge.price ?? 0}
              {userWalletType.currency.label}，解锁全部内容
            </Button>
          )}
        </div>
      ) : null}
      {/* 动态底部信息 */}
      <div className="text-xs flex flex-col gap-1 text-hint-color">
        {data.address ? (
          <div className="flex items-center gap-1">
            <CommonIcon name="location" w={12} h={12} />
            <span>{data.address}</span>
          </div>
        ) : null}
        <div className="flex justify-between">
          <div className="flex gap-2">
            <span>{formatTimeLine(dayjs(data.releaseTime).valueOf())}</span>
            <span>阅读 {data.readNumber}</span>
          </div>
          <div className="flex gap-3">
            <div
              className="flex gap-1 items-center"
              onClick={(e) => {
                e.stopPropagation();
                openShareModal();
              }}
            >
              <CommonIcon name="forward" w={12} h={12} />
              <span>{data.shareNumber || "分享"}</span>
            </div>
            <div className="flex gap-1 items-center">
              <CommonIcon name="comment" w={12} h={12} />
              <span>{data.commentNumber || "评论"}</span>
            </div>
            <div
              className="flex gap-1 items-center"
              onClick={(e) => {
                e.stopPropagation();
                handleClickLike();
              }}
            >
              <CommonIcon
                name={data.hasLike ? "like-selected" : "like"}
                w={12}
                h={12}
              />
              <span>{data.likeNumber || "点赞"}</span>
            </div>
            <div
              className="flex gap-1 items-center"
              onClick={(e) => {
                e.stopPropagation();
                handleClickCollect();
              }}
            >
              <CommonIcon
                name={data.hasCollect ? "collection-selected" : "collection"}
                w={12}
                h={12}
              />
            </div>
          </div>
        </div>
      </div>
      {/* 点赞 list */}
      {data.likes ? (
        <div
          className="flex items-center gap-3"
          onClick={(e) => {
            e.stopPropagation();
            handleClickLikeList();
          }}
        >
          <CommonIcon name="like" w={12} h={12} />
          <div className="flex-1 w-0 flex items-center gap-1">
            <ScrollArea>
              <div className="flex gap-[5px]">
                {data.likes.map((item) => (
                  <UserAvatar
                    key={item.id}
                    src={item.avatarUrl ?? ""}
                    isVip={item.vip}
                    official={item.official}
                    anchor={item.liveAnchor}
                    size={30}
                  />
                ))}
              </div>
              <ScrollBar orientation="horizontal" />
            </ScrollArea>
            <div className="flex-none">等{data.likeNumber}人觉得很赞</div>
          </div>
        </div>
      ) : null}
      {/* 动态状态 */}
      {data.state !== undefined && data.state !== DynamicState.normal ? (
        <div className="absolute top-[6px] right-[20px]">
          <CommonIcon name={`state_${data.state}`} w={50} h={45} />
        </div>
      ) : null}
      {/* 精华状态 */}
      {data.featured ? (
        <div className="absolute top-[6px] right-[20px]">
          <CommonIcon name="featured" w={50} h={28} />
        </div>
      ) : null}
      {/* 置顶 */}
      {data.stickie ? (
        <div className="absolute top-[6px] right-[20px]">
          <CommonIcon name="stickie" w={50} h={50} />
        </div>
      ) : null}
      {/** 评论 */}
      {showComment && data.comments?.length ? (
        <div className="flex gap-3 my-1">
          <CommonIcon name="comment" w={12} h={12} className="mt-[9px]" />
          <div className="flex flex-col gap-4 flex-1">
            {data.comments.map((comment) => (
              <div key={comment.id} className="flex gap-3">
                <div>
                  <UserAvatar
                    src={comment.user?.avatarUrl ?? ""}
                    size={30}
                    isVip={comment.user?.vip}
                  />
                </div>
                <div className="flex flex-col flex-1 justify-between">
                  <div className="flex items-center text-xs">
                    <span className="flex-1 text-[#9F89C7] line-clamp-1">
                      {comment.user?.nickname}
                    </span>
                    <span className="text-hint-color">
                      {formatTimeLine(dayjs(comment.releaseTime).valueOf())}
                    </span>
                  </div>
                  <div className="line-clamp-2 text-[13px]">
                    {comment.voice ? "[语音]" : ""}
                    {comment.resources?.some((r) => r.duration) ? "[视频]" : ""}
                    {comment.resources?.some((r) => !r.duration)
                      ? "[图片]"
                      : ""}
                    {comment.content}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : null}
      <ShareModal
        type={ShareTargetType.Dynamic}
        shareTypes={
          Object.values(ShareType).filter(
            (item) => item !== ShareType.Community
          ) as ShareType[]
        }
        id={data.id}
        open={openShare}
        onClose={closeShareModal}
        onSuccess={handleShareSuccess}
      />
      <UserInfoModal ref={userInfoModalRef} />
    </div>
  );
};
