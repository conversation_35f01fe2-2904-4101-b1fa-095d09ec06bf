import { UserOrderRefundResult } from "@/type/user-order-refund-result";
import {
  UserOrderRefundState,
  userOrderRefundStateConfig,
} from "@/utils/enums";
import { Divider } from "antd-mobile";
import { UserOrderMerchandiseItem } from "./user-order-merchandise-item";
import { useMemo } from "react";
import dayjs from "dayjs";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useCountDownTimer } from "@/hooks/use-count-down-timer";
import { Button } from "@/ui/button";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

interface Props {
  data: UserOrderRefundResult;
}

export default function UserOrderRefundItem(props: Props) {
  const { data } = props;

  const { data: userInfo } = useFullUserInfo();

  const isMerchant = data.merchant?.id && userInfo?.id !== data.merchant.id;

  const duration = useMemo(() => {
    if (!isMerchant && data.state === UserOrderRefundState.Wait) {
      return dayjs(data.autoReasonDate).diff(dayjs(), "second");
    }
    return 0;
  }, [data.autoReasonDate, data.state, isMerchant]);

  const { seconds, isRunning } = useCountDownTimer(duration, true);

  const { navigateRoute } = useNavigateRoute();

  const handleClick = () => {
    navigateRoute("/user-order-refund-detail", {
      id: data.id,
    });
  };

  return (
    <div className="bg-app-bar-background m-[15px] rounded-[10px] flex flex-col">
      <div className="flex text-sm">
        <span className="flex-1 text-hint-color">
          订单号：{data.order?.orderNumber}
        </span>
        <span className="flex-none text-[#FF9212]">
          {userOrderRefundStateConfig[data.state!] ?? "未知状态"}
        </span>
      </div>
      <Divider className="border-divider my-[30px]" />
      <UserOrderMerchandiseItem data={data.order!} />
      <div className="flex">
        {!isMerchant && data.state === UserOrderRefundState.Wait ? (
          isRunning ? (
            <span className="text-[#FF9212]">
              等待商家处理，系统将于
              {dayjs.duration(seconds, "seconds").format("HH:mm:ss")}
              后自动通过审核
            </span>
          ) : (
            <span className="text-[#FF9212] text-center">
              请刷新页面查看最新状态
            </span>
          )
        ) : null}
        {data.state !== UserOrderRefundState.Wait ? (
          <span className="text-hint-color text-[13px] text-center">
            该售后已处理完毕，点击查看详细
          </span>
        ) : null}
        {isMerchant && data.state === UserOrderRefundState.Wait ? (
          <div className="flex">
            <div className="flex-1" />
            <Button
              variant="secondary"
              className="text-[13px] w-[73px] h-[29px]"
              onClick={handleClick}
            >
              立即处理
            </Button>
          </div>
        ) : null}
      </div>
    </div>
  );
}
