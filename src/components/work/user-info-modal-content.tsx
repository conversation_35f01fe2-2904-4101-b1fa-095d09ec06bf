import { Image, Toast } from "antd-mobile";
import { userInfoBackground } from "../image-list";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  follow,
  getUserDetailed,
  joinBlacklist,
  removeBlacklist,
  unfollow,
} from "@/lib/api/user";
import { StateView } from "../state-view";
import { useCallback, useMemo } from "react";
import { UserAvatar } from "../user-avatar";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { PersonAddAltIcon, PersonRemoveIcon, SmsIcon } from "../svg-icon";
import { UserLevelTag } from "./user-level-tag";
import { UserTagList } from "./user-tag-list";
import { CommonIcon } from "../common-icon";
import { ReportTargetType, userWalletType } from "@/utils/enums";
import { ScrollArea, ScrollBar } from "@/ui/scroll-area";
import { AlbumResourceBox } from "./album-resource-box";
import { But<PERSON> } from "@/ui/button";
import classNames from "classnames";
import { queryClient } from "@/provider/query-client";
import { produce } from "immer";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

export const UserInfoModalContent = (props: {
  id: string;
  close: () => void;
}) => {
  const { id, close } = props;

  const queryKey = useMemo(() => ["user-detailed", id], [id]);
  const { data, isLoading } = useQuery({
    queryKey,
    queryFn: () => getUserDetailed(id),
  });

  const { data: userInfo } = useFullUserInfo();
  const { navigateRoute } = useNavigateRoute();

  const userDetailed = data?.data;

  const isSelf = userInfo?.id === userDetailed?.id;

  const resources = useMemo(
    () => [
      ...(userDetailed?.album?.publicResource ?? []),
      ...(userDetailed?.album?.paidResource ?? []),
    ],
    [userDetailed]
  );

  const needBuyIndex = userDetailed?.album?.publicResource?.length ?? 0;

  const followMutation = useMutation({
    mutationFn: follow,
  });
  const unfollowMutation = useMutation({
    mutationFn: unfollow,
  });

  const handleFollowChange = useCallback(async () => {
    Toast.show({
      icon: "loading",
      content: "加载中...",
      duration: 0,
    });

    const mutation = userDetailed?.hasFollow
      ? unfollowMutation
      : followMutation;
    const { ok } = await mutation.mutateAsync(userDetailed?.id!);

    if (ok) {
      queryClient.setQueryData(queryKey, (oldData: typeof data) => {
        if (!oldData?.data) {
          return oldData;
        }

        return produce(oldData, (draft) => {
          if (draft.data) {
            draft.data.hasFollow = !draft.data.hasFollow;
          }
        });
      });
    }
  }, [
    followMutation,
    queryKey,
    unfollowMutation,
    userDetailed?.hasFollow,
    userDetailed?.id,
  ]);

  const joinBlacklistMutation = useMutation({
    mutationFn: joinBlacklist,
  });
  const removeBlacklistMutation = useMutation({
    mutationFn: removeBlacklist,
  });

  const handleBlacklistChange = useCallback(async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const mutation = userDetailed?.block
      ? removeBlacklistMutation
      : joinBlacklistMutation;
    const { ok } = await mutation.mutateAsync(userDetailed?.id!);

    if (ok) {
      queryClient.setQueryData(queryKey, (oldData: typeof data) => {
        if (!oldData?.data) {
          return oldData;
        }

        return produce(oldData, (draft) => {
          if (draft.data) {
            draft.data.block = !draft.data.block;
          }
        });
      });
    }
  }, [
    joinBlacklistMutation,
    queryKey,
    removeBlacklistMutation,
    userDetailed?.block,
    userDetailed?.id,
  ]);

  const handleFollowList = useCallback(() => {
    close();
    navigateRoute("/follow", {
      id: userDetailed?.id,
    });
  }, [close, navigateRoute, userDetailed?.id]);

  const handleFansList = useCallback(() => {
    close();
    navigateRoute("/fans", {
      id: userDetailed?.id,
    });
  }, [close, navigateRoute, userDetailed?.id]);

  const handleJoinUserHome = useCallback(() => {
    close();
    navigateRoute("/user-home", {
      id: userDetailed?.id,
    });
  }, [close, navigateRoute, userDetailed?.id]);

  const handleChatClick = useCallback(() => {
    close();
    navigateRoute("/chat", {
      uid: userDetailed?.id,
    });
  }, [close, navigateRoute, userDetailed?.id]);

  const handleReportClick = useCallback(() => {
    close();
    navigateRoute("/report", {
      id: userDetailed?.id,
      type: ReportTargetType.User,
    });
  }, [close, navigateRoute, userDetailed?.id]);

  const renderContent = useMemo(() => {
    if (!userDetailed) return null;

    return (
      <div className="flex flex-col w-full text-[#666] pb-[10px]">
        <div className="p-4 flex justify-between items-center">
          <div>
            {!isSelf ? (
              <div
                className="text-sm text-[#FF5252] flex gap-[5px] items-center"
                onClick={handleBlacklistChange}
              >
                {userDetailed.block ? (
                  <PersonRemoveIcon size={15} />
                ) : (
                  <PersonAddAltIcon size={15} />
                )}
                <span>{userDetailed.block ? "移出黑名单" : "加入黑名单"}</span>
              </div>
            ) : null}
          </div>
          <span className="text-sm text-[#865AFF]" onClick={handleJoinUserHome}>
            进入主页&gt;
          </span>
        </div>
        <div className="px-5 flex flex-col items-center">
          <div className="flex items-center gap-3">
            <span className="text-base text-black line-clamp-1">
              {userDetailed.nickname}
            </span>
            <UserLevelTag user={userDetailed} />
          </div>
          <div className="py-2">
            <UserTagList user={userDetailed} />
          </div>
          <div className="flex items-center gap-[10px]">
            <span>ID: {userDetailed.userid}</span>
            <span className="flex gap-[5px] items-center">
              <CommonIcon name="location" w={12} h={12} />
              <span className="flex-1 text-xs line-clamp-1">
                {userDetailed.realAddress ?? "未知"}
              </span>
            </span>
          </div>
        </div>
        {userDetailed.chargeConfig ? (
          <div className="pt-2 px-[15px]">
            <div className="flex justify-evenly">
              <div className="flex items-center gap-[5px]">
                <CommonIcon name="chargeConfigChat" w={12} h={12} />
                <span className="text-[13px]">
                  {userDetailed.chargeConfig.chatPrice ?? 0}
                  {userWalletType.currency.label}/条
                </span>
              </div>
              <div className="flex items-center gap-[5px]">
                <CommonIcon name="chargeConfigVoice" w={12} h={12} />
                <span className="text-[13px]">
                  {userDetailed.chargeConfig.voicePrice ?? 0}
                  {userWalletType.currency.label}/分钟
                </span>
              </div>
              <div className="flex items-center gap-[5px]">
                <CommonIcon name="chargeConfigVideo" w={12} h={12} />
                <span className="text-[13px]">
                  {userDetailed.chargeConfig.videoPrice ?? 0}
                  {userWalletType.currency.label}/分钟
                </span>
              </div>
            </div>
          </div>
        ) : null}
        <div className="py-[10px] px-[15px]">
          <div className="h-[55px] bg-[#F1E8FA] rounded-full flex py-[10px] items-center">
            <div
              className="text-[17px] text-[#B68DE7] gap-1 flex-1 flex justify-center"
              onClick={handleFollowList}
            >
              <span className="text-[#865AFF] font-bold">
                {userDetailed.followNumber ?? 0}
              </span>
              关注
            </div>
            <div className="h-full border-r border-solid border-[#E0CEF5]" />
            <div
              className="text-[17px] text-[#B68DE7] flex gap-1 flex-1 flex justify-center"
              onClick={handleFansList}
            >
              <span className="text-[#865AFF] font-bold">
                {userDetailed.fansNumber ?? 0}
              </span>
              粉丝
            </div>
          </div>
        </div>
        <div className="px-[15px] flex justify-center">
          <span className="text-sm text-black whitespace-pre-wrap line-clamp-2">
            {userDetailed.signature}
          </span>
        </div>
        {resources.length ? (
          <div className="pt-[10px] px-[15px]">
            <ScrollArea>
              <div className="flex gap-2" onClick={handleJoinUserHome}>
                {resources.map((resource, index) => (
                  <AlbumResourceBox
                    key={resource.id}
                    data={resource}
                    size={100}
                    buy={index < needBuyIndex || !!userDetailed.album?.buy}
                  />
                ))}
              </div>
              <ScrollBar orientation="horizontal" />
            </ScrollArea>
          </div>
        ) : null}
        {!isSelf ? (
          <div className="pt-[10px] px-[15px]">
            <div className="flex justify-between gap-2">
              {userDetailed.hasFollow !== undefined ? (
                <Button
                  variant="primary"
                  size="lg"
                  className={classNames(
                    "text-white rounded-[5px] flex-initial w-[122px]",
                    userDetailed.hasFollow ? "bg-[#E6E6E6] text-black" : ""
                  )}
                  onClick={handleFollowChange}
                >
                  {userDetailed.hasFollow ? "取消关注" : "立即关注"}
                </Button>
              ) : null}
              <Button
                variant="secondary"
                size="lg"
                className="rounded-[5px] flex-initial w-[122px]"
                onClick={handleChatClick}
              >
                <div className="flex items-center gap-[5px]">
                  <SmsIcon size={19} color="#FF3460" />
                  <span>私信</span>
                </div>
              </Button>
              <Button
                variant="primary"
                size="lg"
                className="rounded-[5px] flex-initial w-[71px] bg-[#E6E6E6] text-black"
                onClick={handleReportClick}
              >
                举报
              </Button>
            </div>
          </div>
        ) : null}
      </div>
    );
  }, [
    handleBlacklistChange,
    handleChatClick,
    handleFansList,
    handleFollowChange,
    handleFollowList,
    handleJoinUserHome,
    handleReportClick,
    isSelf,
    needBuyIndex,
    resources,
    userDetailed,
  ]);

  return (
    <div className="relative w-full z-10">
      <div
        className="w-full h-[400px]"
        style={!isLoading ? { height: "auto" } : undefined}
      >
        <StateView isLoading={isLoading} isEmpty={!data}>
          {renderContent}
        </StateView>
      </div>
      {userDetailed ? (
        <div className="absolute top-[-36px] left-0 w-full flex justify-center z-[-1]">
          <UserAvatar
            src={userDetailed?.avatarUrl!}
            size={73}
            isVip={userDetailed.vip}
            official={userDetailed.official}
            anchor={userDetailed.liveAnchor}
            borderColor="#fff"
          />
        </div>
      ) : null}
      <div className="absolute top-0 left-0 w-full h-full z-[-2]">
        <Image
          src={userInfoBackground}
          width="100%"
          height="100%"
          placeholder={null}
          fallback={null}
          className="object-cover"
        />
      </div>
    </div>
  );
};
