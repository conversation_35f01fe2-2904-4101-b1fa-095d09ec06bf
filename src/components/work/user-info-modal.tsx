import { Popup } from "antd-mobile";
import { UserInfoModalContent } from "./user-info-modal-content";
import { forwardRef } from "react";
import { useImperativeHandle } from "react";
import { useState } from "react";

export interface UserInfoModalRef {
  open: (id: string) => void;
  close: () => void;
}

interface Props {}

export const UserInfoModal = forwardRef<UserInfoModalRef, Props>((_, ref) => {
  const [open, setOpen] = useState(false);
  const [id, setId] = useState("");

  const onOpen = (id: string) => {
    setId(id);
    setOpen(true);
  };

  const onClose = () => {
    setId("");
    setOpen(false);
  };

  useImperativeHandle(ref, () => ({
    open: onOpen,
    close: onClose,
  }));

  if (!id) return null;

  return (
    <Popup
      destroyOnClose
      visible={open}
      bodyClassName="rounded-t-[10px]"
      onMaskClick={onClose}
      onClose={onClose}
    >
      <UserInfoModalContent id={id} close={onClose} />
    </Popup>
  );
});
