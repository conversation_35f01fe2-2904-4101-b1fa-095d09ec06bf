import classNames from "classnames";

interface Props {
  value: number;
  tabs: Array<{ key: number; label: string }>;
  itemClassName?: string;
  onChange: (value: number) => void;
}

export const FilterTabs = (props: Props) => {
  const { value, tabs, itemClassName, onChange } = props;

  return (
    <div className="flex gap-2 pb-[5px]">
      {tabs.map((tab) => (
        <div
          key={tab.key}
          className={classNames(
            "filter-tab flex relative flex-none",
            value === tab.key ? "filter-tab-bg-arrow" : ""
          )}
          onClick={() => onChange(tab.key)}
        >
          <span
            className={classNames(
              "px-[6px] h-[25px] flex-none leading-[25px] text-xs rounded-[13px] bg-filter-tab text-filter-tab-foreground",
              itemClassName,
              value === tab.key
                ? "!bg-filter-tab-active !text-filter-tab-active-foreground"
                : ""
            )}
          >
            {tab.label}
          </span>
        </div>
      ))}
    </div>
  );
};
