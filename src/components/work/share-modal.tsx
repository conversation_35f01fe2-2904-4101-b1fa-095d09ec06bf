import { useShareList } from "@/hooks/use-share-list";
import { Popup, Toast } from "antd-mobile";
import { StateView } from "../state-view";
import { AspectRatio } from "@/ui/aspect-ratio";
import { ShareTargetType, ShareType } from "@/utils/enums";
import { useMemo, useState } from "react";
import { ShareItem } from "@/type/layout-config";
import { CommonIcon } from "../common-icon";
import { useTheme } from "@/provider/useTheme";
import { useMutation } from "@tanstack/react-query";
import { share } from "@/lib/api/user";
import { PosterShareDialog } from "./poster-share-dialog";
import { PosterShareData } from "@/type/poster-share-data";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

const formatIcon = (icon: ShareItem["icon"], theme: string) => {
  const url = theme === "light" ? icon?.light : icon?.dark;

  // 通用格式化为 menus/xxx，支持字母、数字、下划线和连字符
  return url?.replace(/.*\/(share\/[\w-]+)\.png$/, "$1");
};

interface Props {
  type: ShareTargetType;
  // 支持的分享类型
  shareTypes?: ShareType[];
  id?: string;
  rowSize?: number;
  open: boolean;
  customItem?: Array<{ icon: string; label: string; onClick: () => void }>;

  onSuccess?: () => void;
  onClose: () => void;
}

export const ShareModal = (props: Props) => {
  const {
    type,
    open,
    id,
    shareTypes,
    rowSize = 4,
    customItem,
    onClose,
    onSuccess,
  } = props;

  const [posterShareData, setPosterShareData] = useState<PosterShareData>();

  const { navigateRoute } = useNavigateRoute();

  const { data: shareList, isLoading } = useShareList();
  const theme = useTheme();

  const list = useMemo(() => {
    if (!shareTypes) return shareList;

    return shareList.filter((item) => shareTypes.includes(item.type));
  }, [shareList, shareTypes]);

  const shareMutations = useMutation({
    mutationFn: share,
  });

  const handleShare = async (item: ShareItem) => {
    Toast.show({
      icon: "loading",
      content: "加载中...",
      duration: 0,
    });

    const { ok, data } = await shareMutations.mutateAsync({
      type: item.type,
      targetType: type,
      id,
    });

    if (ok) {
      onSuccess?.();

      switch (item.type) {
        case ShareType.Link:
          navigator.clipboard.writeText(data?.content ?? "").then(() => {
            Toast.show({
              content: "复制链接成功",
            });
          });
          break;

        case ShareType.Poster:
          setPosterShareData(data);
          break;
        case ShareType.Community:
          navigateRoute("/dynamic-publish", {
            targetType: type,
            targetId: id,
          });
          break;
        case ShareType.Friend:
          navigateRoute("/conversation-select", {
            type,
            id,
            title: "分享到",
          });
          break;
        default:
          // todo: 未完成
          Toast.show({
            content: "此功能暂不支持",
          });
      }

      onClose();
    }
  };

  return (
    <>
      <Popup
        destroyOnClose
        visible={open}
        bodyClassName="rounded-t-[10px] bg-app-bar-background"
        onMaskClick={onClose}
        onClose={onClose}
      >
        <StateView isLoading={isLoading}>
          <div className="flex flex-col p-[15px] mb-[15px]">
            <span className="text-[17px] text-center">分享</span>
            <div
              className="py-[15px] grid gap-5"
              style={{
                gridTemplateColumns: `repeat(${rowSize}, minmax(0, 1fr))`,
              }}
            >
              {list.map((item) => (
                <AspectRatio key={item.label} ratio={0.7}>
                  <div
                    className="w-full h-full flex flex-col justify-center items-center"
                    onClick={() => handleShare(item)}
                  >
                    <CommonIcon
                      name={formatIcon(item.icon, theme)!}
                      className="!w-full !h-auto"
                    />
                    <span className="text-[15px]">{item.label}</span>
                  </div>
                </AspectRatio>
              ))}
              {customItem?.map((item) => (
                <AspectRatio key={item.label} ratio={0.7}>
                  <div
                    className="w-full h-full flex flex-col justify-center items-center"
                    onClick={item.onClick}
                  >
                    <CommonIcon name={item.icon} className="!w-full !h-auto" />
                    <span className="text-[15px]">{item.label}</span>
                  </div>
                </AspectRatio>
              )) ?? null}
            </div>
          </div>
        </StateView>
      </Popup>
      {posterShareData ? <PosterShareDialog data={posterShareData} /> : null}
    </>
  );
};
