import { getBackpackGiftList } from "@/lib/api/user";
import { PageParams } from "@/type";
import { ScrollArea } from "@/ui/scroll-area";
import { ScrollLoadData } from "../scroll-load-data";
import { UserBackpackGiftResult } from "@/type/user-backpack-gift-result";
import Batter from "../batter";
import { Button } from "@/ui/button";
import { Image, ProgressCircle } from "antd-mobile";
import classNames from "classnames";
import { DefaultLoadImage } from "../default-load-image";
import { useState } from "react";

interface Props {
  multi?: boolean;
  onSendGift: (gift: UserBackpackGiftResult, number?: number) => void;
}

export const BackpackGiftPanel = (props: Props) => {
  const { multi, onSendGift } = props;

  const [selectedGift, setSelectedGift] = useState<
    UserBackpackGiftResult | undefined
  >();

  const queryKey = ["backpack-gift-list"];
  const queryFn = (params: PageParams) => getBackpackGiftList(params);

  const handleSendGift = (gift: UserBackpackGiftResult, number?: number) => {
    if (selectedGift?.targetId === gift.targetId) {
      onSendGift(gift, number);

      return;
    }

    setSelectedGift(gift);
  };

  return (
    <ScrollArea className="h-full">
      <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
        {(data) => (
          <div className="p-[15px] grid grid-cols-4">
            {data.map((item) => (
              <div key={item.targetId} onClick={() => handleSendGift(item)}>
                <Batter
                  enabled={multi && selectedGift?.targetId === item.targetId}
                  onFinish={(number) => onSendGift(item, number)}
                  builder={(current, maxWait, number) => (
                    <div
                      className="flex justify-center items-center relative"
                      style={{ width: "80px", height: "125px" }}
                    >
                      {/* 内容圆形区域 */}
                      <div
                        className="flex flex-col justify-center items-center relative rounded-full bg-gradient-to-br from-[#FF3460] to-[#FF7B57]"
                        style={{
                          width: "80px",
                          height: "80px",
                        }}
                      >
                        {/* 连击数字 */}
                        <div className="flex gap-[10px] items-center text-white">
                          <span>x</span>
                          <span
                            className="text-[30px] font-bold"
                            style={{ transform: "skewX(-17deg)" }}
                          >
                            {number}
                          </span>
                        </div>
                        {/* 连击文字 */}
                        <div
                          className="text-white text-xl"
                          style={{ zoom: 0.5 }}
                        >
                          连击中
                        </div>
                      </div>

                      {/* 进度条 */}
                      <div
                        className="absolute top-0 left-0"
                        style={{
                          width: "81px",
                          height: "81px",
                        }}
                      >
                        <ProgressCircle
                          percent={current / maxWait}
                          style={{
                            "--fill-color": "#fff",
                            "--track-color": "transparent",
                            "--size": "81px",
                            "--track-width": "2px",
                          }}
                        />
                      </div>
                    </div>
                  )}
                >
                  <div
                    className={classNames(
                      "flex flex-col relative justify-evenly h-[125px] border-2 border-solid rounded-[15px]",
                      selectedGift?.targetId === item.targetId
                        ? "border-[#B573FF]"
                        : "border-app-bar-background"
                    )}
                  >
                    <div className="w-full h-[70px] flex justify-center items-center">
                      <div className="h-[30px]">
                        <Image
                          src={item.image}
                          width="auto"
                          height="100%"
                          fit="contain"
                          placeholder={<DefaultLoadImage />}
                          fallback={<DefaultLoadImage />}
                        />
                      </div>
                    </div>
                    {selectedGift?.targetId !== item.targetId ? (
                      <div className="flex flex-col">
                        <span className="text-[13px] line-clamp-1">
                          {item.name}
                        </span>
                        <span className="text-[13px] text-[#6712FF] line-clamp-1">
                          剩余：
                          {item.number}
                        </span>
                      </div>
                    ) : (
                      <div className="flex justify-center px-[5px]">
                        <Button
                          variant="primary"
                          className="text-[13px] h-[30px] w-full"
                        >
                          赠送
                        </Button>
                      </div>
                    )}
                  </div>
                </Batter>
              </div>
            ))}
          </div>
        )}
      </ScrollLoadData>
    </ScrollArea>
  );
};
