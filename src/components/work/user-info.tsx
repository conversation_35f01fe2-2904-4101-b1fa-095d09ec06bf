import { User } from "@/type/user";
import { ReactNode, useMemo } from "react";
import { <PERSON>rollArea, ScrollBar } from "@/ui/scroll-area";
import { UserAvatar } from "../user-avatar";
import { UserLevelTag } from "./user-level-tag";
import { UserTagList } from "./user-tag-list";
import classNames from "classnames";
import { UserInfoModal, UserInfoModalRef } from "./user-info-modal";
import { useRef } from "react";

interface Props {
  user: User;
  className?: string;
  size?: number;
  transparent?: boolean;
  tail?: ReactNode;
  onClick?: () => void;
  onAvatarClick?: () => void;
}

export const UserInfo = (props: Props) => {
  const {
    user,
    transparent = true,
    tail,
    size = 47,
    className,
    onClick,
    onAvatarClick,
  } = props;
  const { avatarUrl, vip, official, liveAnchor, nickname } = user;

  // 昵称根据 官方、vip 不同的颜色
  const nicknameColor = useMemo(() => {
    if (official) {
      return "#1E90FF";
    }

    if (vip) {
      return "#EB5280";
    }
  }, [vip, official]);

  const userInfoModalRef = useRef<UserInfoModalRef>(null);

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();

    if (onClick) {
      onClick();
    } else {
      userInfoModalRef.current?.open(user.id!);
    }
  };

  return (
    <div className="w-full flex gap-4" onClick={handleClick}>
      <UserAvatar
        src={avatarUrl ?? ""}
        size={size}
        isVip={vip}
        official={official}
        anchor={liveAnchor}
        onClick={onAvatarClick}
      />
      <div className="flex flex-col justify-evenly flex-1">
        <div className="flex gap-3 items-center">
          <span
            className={classNames("text-base font-medium", className)}
            style={{ color: nicknameColor }}
          >
            {nickname}
          </span>
          <UserLevelTag user={user} />
        </div>
        <div className="w-full flex">
          <div className="flex-1 w-0">
            {user.tags?.length && !tail ? (
              <ScrollArea>
                <UserTagList user={user} transparent={transparent} />
                <ScrollBar orientation="horizontal" />
              </ScrollArea>
            ) : null}
            {tail ? tail : null}
          </div>
        </div>
      </div>
      <UserInfoModal ref={userInfoModalRef} />
    </div>
  );
};
