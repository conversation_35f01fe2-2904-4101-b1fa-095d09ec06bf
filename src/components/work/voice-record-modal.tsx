import { Popup, Toast } from "antd-mobile";
import { CommonIcon } from "../common-icon";
import { useEffect, useRef, useState } from "react";
import { Button } from "@/ui/button";
import { DeleteIcon, PlayRoundedIcon, StopRoundedIcon } from "../svg-icon";
import "./voice-record-modal.less";

interface Props {
  open: boolean;
  minDuration: number;
  maxDuration: number;
  value?: File | null;
  onClose: () => void;
  onFinish?: (file: File | null, duration: number) => void;
}

export const VoiceRecordModal = (props: Props) => {
  const {
    open,
    minDuration = 1,
    maxDuration = 10,
    value,
    onClose,
    onFinish,
  } = props;

  const [recording, setRecording] = useState(false);
  const [duration, setDuration] = useState(0);
  const [hasPermission, setHasPermission] = useState(false);
  const [cancelling, setCancelling] = useState(false);
  const [playing, setPlaying] = useState(false);
  const [file, setFile] = useState<File | null>(null);

  const durationRef = useRef(0);
  const cancellingRef = useRef(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<BlobPart[]>([]);
  const startYRef = useRef<number | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    if (value) {
      setFile(value);
    }
  }, [value]);

  useEffect(() => {
    if (open) {
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then(() => {
          setHasPermission(true);
        })
        .catch(() => {
          setHasPermission(false);
        });
    }
  }, [open]);

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (recording) {
      timer = setInterval(() => {
        setDuration((prev) => {
          if (prev >= maxDuration - 1) {
            mediaRecorderRef.current?.stop();

            durationRef.current = maxDuration;
            return maxDuration;
          }
          durationRef.current = prev + 1;
          return prev + 1;
        });
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [recording, maxDuration]);

  const reset = () => {
    setRecording(false);
    setDuration(0);
    setCancelling(false);
    setPlaying(false);

    cancellingRef.current = false;
    chunksRef.current = [];
    startYRef.current = null;
  };

  const startRecording = async () => {
    try {
      durationRef.current = 0;
      setFile(null);

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);

      mediaRecorderRef.current = recorder;
      chunksRef.current = [];

      recorder.ondataavailable = (e) => {
        chunksRef.current.push(e.data);
      };

      recorder.onstop = () => {
        // 如果未取消录音且录音时长大于等于最小时长，则将录音转换为 File 对象
        if (!cancellingRef.current) {
          if (durationRef.current >= minDuration) {
            const blob = new Blob(chunksRef.current, {
              type: "audio/ogg; codecs=opus",
            });

            // 将 Blob 转换为 File 对象
            const fileName = `${+new Date()}.ogg`;
            const file = new File([blob], fileName, { type: "audio/ogg" });

            // file.duration = durationRef.current;

            setFile(file);
          } else {
            chunksRef.current = [];

            Toast.show({
              content: `录制时间过短!`,
            });
          }
        }

        // 如果取消录音，则不保存录音
        if (cancellingRef.current) {
          chunksRef.current = [];
        }

        reset();
      };

      recorder.start();

      setRecording(true);
      setDuration(0);
    } catch (error) {
      Toast.show({
        content: "语音录制失败，请重试!",
      });
    }
  };

  const handleFinish = () => {
    onFinish?.(file, durationRef.current);
    onClose();
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    startYRef.current = e.touches[0].clientY;
    startRecording();
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (startYRef.current !== null) {
      const currentY = e.touches[0].clientY;
      const diff = startYRef.current - currentY;
      cancellingRef.current = diff > 50; // 如果上滑超过50px,则视为取消
      setCancelling(diff > 50); // 如果上滑超过50px,则视为取消
    }
  };

  const handleTouchEnd = () => {
    if (mediaRecorderRef.current && recording) {
      mediaRecorderRef.current.stop();
    }
  };

  useEffect(() => {
    audioRef.current = new Audio();

    const endedHandler = () => {
      setPlaying(false);
    };

    audioRef.current.addEventListener("ended", endedHandler);

    return () => {
      audioRef.current?.removeEventListener("ended", endedHandler);
      audioRef.current = null;
    };
  }, []);

  const handlePlay = () => {
    if (file && audioRef.current) {
      audioRef.current.src = URL.createObjectURL(file);
      audioRef.current.play();
      setPlaying(true);
    }
  };

  const handleStop = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setPlaying(false);
    }
  };

  const handleDelete = () => {
    setFile(null);
    audioRef.current = null;
    setPlaying(false);
  };

  const requestPermission = () => {
    navigator.mediaDevices
      .getUserMedia({ audio: true })
      .then(() => {
        setHasPermission(true);
      })
      .catch(() => {
        setHasPermission(false);
        Toast.show({
          content: "你拒绝了麦克风权限!",
        });
      });
  };

  return (
    <Popup
      destroyOnClose
      visible={open}
      bodyClassName="rounded-t-[10px] bg-app-bar-background h-[350px]"
      onMaskClick={onClose}
      onClose={onClose}
    >
      <div className="voice-record-modal p-[15px] h-full flex flex-col items-center justify-center text-[#6712FF] text-sm">
        {hasPermission ? (
          <>
            <div className="relative z-10">
              <span
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
                onContextMenu={(e) => e.preventDefault()}
              >
                <CommonIcon name="voiceRecordStill" w={77} h={77} />
              </span>
              {recording ? (
                <div className="wave-container absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[160px] h-[160px] z-[-1]">
                  <div className="inner-circle"></div>
                </div>
              ) : null}
            </div>
            <div className="pt-[50px]">
              {!recording && !file ? (
                <span>
                  长按录制({minDuration}s - {maxDuration}s)
                </span>
              ) : null}
              {recording && !cancelling ? (
                <span>{duration}s(上滑取消录音)</span>
              ) : null}
              {recording && cancelling ? (
                <span className="text-[#F44336]">松开取消录音</span>
              ) : null}
              {!recording && file ? (
                <div className="w-[70vw] flex gap-[10px]">
                  <Button
                    variant="primary"
                    className="flex-none w-[40px] h-[40px] rounded-full p-0"
                  >
                    {playing ? (
                      <span onClick={handleStop}>
                        <StopRoundedIcon size={20} className="text-white" />
                      </span>
                    ) : (
                      <span onClick={handlePlay}>
                        <PlayRoundedIcon className="text-white" />
                      </span>
                    )}
                  </Button>
                  <Button
                    variant="primary"
                    className="w-full h-[40px]"
                    onClick={handleFinish}
                  >
                    完成
                  </Button>
                  <Button
                    variant="secondary"
                    className="flex-none w-[40px] h-[40px] rounded-full p-0"
                    onClick={handleDelete}
                  >
                    <DeleteIcon />
                  </Button>
                </div>
              ) : null}
            </div>
          </>
        ) : (
          <div
            className="flex w-full h-full items-center justify-center"
            onClick={requestPermission}
          >
            <span>暂无麦克风权限，点击授权</span>
          </div>
        )}
      </div>
    </Popup>
  );
};
