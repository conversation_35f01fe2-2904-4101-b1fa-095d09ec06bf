import {
  AddOutlineIcon,
  KeyboardIcon,
  MicIcon,
  MoodIcon,
} from "@/components/svg-icon";
import { EmojiPicker } from "../emoji-picker/emoji-picker";
import { useRef, useState } from "react";
import { MorePanel } from "../more-panel/more-panel";
import { ChatMoreItem } from "@/type/layout-config";
import { useSendMessage } from "@/hooks/use-send-message";
import { Button } from "@/ui/button";
import { UserChatProfileResult } from "@/type/user-chat-profile-result";
import { VoiceRecordModal } from "../voice-record-modal";
import { useModal } from "@/hooks/use-modal";

// 获取光标位置
const getCursorIndex = () => {
  const selection = window.getSelection();
  return selection?.focusOffset;
};

interface Props {
  list: ChatMoreItem[];
  userChatProfile?: UserChatProfileResult;
}

export const MessageInput = (props: Props) => {
  const { list, userChatProfile } = props;

  const [type, setType] = useState<"text" | "voice" | "emoji" | "more">("text");

  const [text, setText] = useState("");

  const { sendTextMessage, sendAudioMessage } = useSendMessage(
    userChatProfile?.user?.id
  );
  const {
    open: openVoice,
    openModal: openVoiceModal,
    closeModal: closeVoiceModal,
  } = useModal();

  const editorRef = useRef<HTMLDivElement>(null);
  const lastCursorIndexRef = useRef<number>(0);

  const handleChangeType = (value: "text" | "voice" | "emoji" | "more") => {
    if (value === type) {
      setType("text");

      editorRef.current?.focus();
      // 设置光标位置
      const selection = window.getSelection();
      const range = document.createRange();
      const textNode = editorRef.current?.firstChild || editorRef.current;

      if (textNode) {
        range.setStart(textNode, lastCursorIndexRef.current);
        range.setEnd(textNode, lastCursorIndexRef.current);
        selection?.removeAllRanges();
        selection?.addRange(range);
      }
    } else {
      setType(value);
    }
  };

  const handleInputChange = () => {
    setText(editorRef.current?.innerText || "");
  };

  const handleSelectEmoji = (emoji: string) => {
    if (!editorRef.current) return;

    // 使用保存的光标位置
    const cursorPosition = lastCursorIndexRef.current;
    const currentText = text;

    // 在保存的光标位置插入表情
    const newText =
      currentText.slice(0, cursorPosition) +
      emoji +
      currentText.slice(cursorPosition);

    editorRef.current.innerText = newText;

    setText(newText);

    // 更新光标位置
    lastCursorIndexRef.current = cursorPosition + emoji.length;
  };

  const handleAddVoiceRecord = (voice: File | null, second: number) => {
    if (voice) {
      sendAudioMessage(voice, second);
    }

    closeVoiceModal();
  };

  return (
    <div className="flex flex-col">
      <div className="bg-app-bar-background py-1 px-2 flex items-center gap-2">
        <span onClick={() => openVoiceModal()}>
          <MicIcon className="text-[28px]" />
        </span>
        <div className="relative flex-1 w-0">
          <div
            ref={editorRef}
            className="bg-[#8f959e] flex-1 rounded-[6px] text-sm min-h-[32px] max-h-[80px] px-[10px] py-1 focus-visible:outline-none overflow-y-auto"
            contentEditable
            onInput={handleInputChange}
            onKeyUp={() => {
              lastCursorIndexRef.current = getCursorIndex() ?? 0;
            }}
            onMouseUp={() => {
              lastCursorIndexRef.current = getCursorIndex() ?? 0;
            }}
            onBlur={() => {
              // 在失去焦点时保存最后的光标位置
              lastCursorIndexRef.current = getCursorIndex() ?? 0;
            }}
            onFocus={() => {
              setType("text");
            }}
          />
        </div>
        <span onClick={() => handleChangeType("emoji")}>
          {type === "emoji" ? (
            <KeyboardIcon className="text-[28px]" />
          ) : (
            <MoodIcon className="text-[28px]" />
          )}
        </span>
        {text.trim().length ? (
          <Button
            variant="primary"
            className="h-[32px] py-2"
            onClick={() => sendTextMessage(text)}
          >
            发送
          </Button>
        ) : (
          <span onClick={() => handleChangeType("more")}>
            <AddOutlineIcon className="text-[28px]" />
          </span>
        )}
      </div>
      <EmojiPicker
        className={type === "emoji" ? "" : "hidden"}
        onSelectEmoji={handleSelectEmoji}
      />
      <MorePanel
        className={type === "more" ? "" : "hidden"}
        list={list}
        userChatProfile={userChatProfile}
      />
      <VoiceRecordModal
        open={openVoice}
        minDuration={1}
        maxDuration={60}
        onClose={closeVoiceModal}
        onFinish={handleAddVoiceRecord}
      />
    </div>
  );
};
