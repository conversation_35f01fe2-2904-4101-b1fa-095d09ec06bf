import { ThemeIcon } from "@/components/theme-icon";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { useTheme } from "@/provider/useTheme";
import { ChatMoreItem } from "@/type/layout-config";
import { ScrollArea } from "@/ui/scroll-area";
import classNames from "classnames";
import { GiftModal } from "../gift-modal";
import { useModal } from "@/hooks/use-modal";
import { Divider, Popup, Toast } from "antd-mobile";
import { useMutation, useQuery } from "@tanstack/react-query";
import { giveGift } from "@/lib/api/user";
import { GiftResult } from "@/type/gift-result";
import { UserChatProfileResult } from "@/type/user-chat-profile-result";
import { queryClient } from "@/provider/query-client";
import { userWalletQueryKey } from "@/hooks/use-user-wallet";
import { Fragment, useRef, useState } from "react";
import { useSendMessage } from "@/hooks/use-send-message";
import { getChargeConfig } from "@/lib/api/wallet";
import { confirmDialog } from "../confirm-dialog";
import { RtcRoomType, userWalletType } from "@/utils/enums";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import rtcLogic from "@/utils/rtc-logic";

const rtcOptions = [
  {
    value: RtcRoomType.Audio,
    title: "语音通话",
  },
  {
    value: RtcRoomType.Video,
    title: "视频通话",
  },
];

const formatIcon = (icon: ChatMoreItem["icon"], theme: string) => {
  const url = theme === "light" ? icon.light : icon.dark;

  // 通用格式化为 menus/xxx，支持字母、数字、下划线和连字符
  return url.replace(/.*\/(chat\/[\w-]+)\.png$/, "$1");
};

interface Props {
  className?: string;

  list: ChatMoreItem[];

  userChatProfile?: UserChatProfileResult;
}

export const MorePanel = (props: Props) => {
  const { list, className, userChatProfile } = props;

  const takePhotoRef = useRef<HTMLInputElement>(null);
  const chooseImageRef = useRef<HTMLInputElement>(null);

  const [visible, setVisible] = useState(false);

  const theme = useTheme();

  const { navigateRoute } = useNavigateRoute();
  const { sendImageMessage, sendVideoMessage } = useSendMessage();

  const {
    open: openGift,
    openModal: openGiftModal,
    closeModal: closeGiftModal,
  } = useModal();

  const { data: userInfo } = useFullUserInfo();

  const handleClick = (item: ChatMoreItem) => {
    if (item.id === "c_red_packet") {
      navigateRoute("/red-envelope-publish", {
        c2c: 1,
        targetId: "1798147321319981056",
      });
    } else if (item.id === "c_gift") {
      openGiftModal();
    } else if (item.id === "c_interactive_permission") {
      navigateRoute("/user-chat-permission-create", {
        userId: userChatProfile?.user?.id!,
      });
    } else if (item.id === "c_image") {
      chooseImageRef.current?.click();
    } else if (item.id === "c_camera") {
      takePhotoRef.current?.click();
    } else if (item.id === "c_rtc_call") {
      setVisible(true);
    }
  };

  const giveGiftMutation = useMutation({
    mutationFn: giveGift,
  });

  const handleSendGift = async (gift: GiftResult, note?: string) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await giveGiftMutation.mutateAsync({
      userId: userChatProfile?.user?.id!,
      giftId: gift.id!,
      note,
    });

    if (ok) {
      Toast.show({
        content: "礼物赠送成功",
      });

      closeGiftModal();
      queryClient.refetchQueries({
        queryKey: userWalletQueryKey,
      });
    }
  };

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type.startsWith("image/")) {
        sendImageMessage(file);
      } else if (file.type.startsWith("video/")) {
        sendVideoMessage(file);
      }
    }
  };

  const handleCameraSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type.startsWith("image/")) {
        sendImageMessage(file);
      } else if (file.type.startsWith("video/")) {
        sendVideoMessage(file);
      }
    }
  };

  const { refetch: refetchChargeConfig } = useQuery({
    queryKey: ["charge-config", userChatProfile?.user?.id],
    queryFn: () => getChargeConfig({ userId: userChatProfile?.user?.id! }),
    enabled: false,
  });

  const handleOpenRtc = async (type: number) => {
    setVisible(false);
    const { data } = await refetchChargeConfig();

    if (data?.ok) {
      const price =
        type === RtcRoomType.Audio
          ? data.data?.voicePrice
          : data.data?.videoPrice;

      if (price && price > 0) {
        const result = await confirmDialog.show(
          `与该用户进行${
            type === RtcRoomType.Audio ? "语音" : "视频"
          }通话将会收取${price}${userWalletType.currency.label}/分钟，是否继续`,
          "收费提醒"
        );

        if (!result) {
          return false;
        }
      }

      rtcLogic.startC2CCall(type, {
        userInfo: userInfo!,
        targetUser: userChatProfile?.user!,
      });
    }
  };

  return (
    <>
      <div
        className={classNames(
          "w-full h-[200px] flex flex-col bg-app-bar-background p-[10px]",
          className
        )}
      >
        <ScrollArea className="h-full">
          <div className="grid grid-cols-4 gap-y-[10px]">
            {list.map((item) => (
              <div
                key={item.id}
                className="flex flex-col items-center justify-center"
                onClick={() => handleClick(item)}
              >
                <ThemeIcon
                  name={formatIcon(item.icon, theme)}
                  w={item.icon.width / 2}
                  h={item.icon.width / 2}
                />
                <span>{item.label}</span>
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>
      <GiftModal
        open={openGift}
        onClose={closeGiftModal}
        onSend={handleSendGift}
      />
      <input
        type="file"
        id="fileInput"
        accept="image/*,video/*"
        style={{ display: "none" }}
        ref={chooseImageRef}
        onChange={handleImageSelect}
      ></input>
      <input
        type="file"
        id="fileInput"
        accept="image/*,video/*"
        capture="environment"
        style={{ display: "none" }}
        ref={takePhotoRef}
        onChange={handleCameraSelect}
      ></input>
      <Popup
        visible={visible}
        bodyClassName="bg-transparent"
        onMaskClick={() => setVisible(false)}
        onClose={() => setVisible(false)}
      >
        <div className="flex flex-col gap-[10px] mx-[15px]">
          <div className="flex flex-col rounded-[10px] bg-app-bar-background">
            {rtcOptions.map((item, index) => (
              <Fragment key={item.value}>
                <div
                  className="py-[15px] text-center text-base text-[#9d64ff]"
                  onClick={() => handleOpenRtc(item.value)}
                >
                  {item.title}
                </div>
                {index !== rtcOptions.length - 1 ? (
                  <Divider className="border-divider m-0" />
                ) : null}
              </Fragment>
            ))}
          </div>
          <div
            className="mb-[10px] py-[15px] text-center text-base text-[#9d64ff] bg-hint-color rounded-[10px]"
            onClick={() => setVisible(false)}
          >
            取消
          </div>
        </div>
      </Popup>
    </>
  );
};
