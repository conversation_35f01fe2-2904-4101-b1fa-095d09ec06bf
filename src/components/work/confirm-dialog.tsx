import { But<PERSON> } from "@/ui/button";
import { Dialog } from "antd-mobile";
import { ReactNode } from "react";
import { CommonIcon } from "../common-icon";
import "./confirm-dialog.less";

export const confirmDialog = {
  show: (content: ReactNode, title?: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const { close } = Dialog.show({
        className: "confirm-dialog",
        bodyClassName: "dialog-bg",
        closeOnMaskClick: true,
        title: (
          <div className="text-base text-[#672A37] font-normal pt-[10px]">
            {title ?? "提示信息"}
          </div>
        ),
        content: (
          <div>
            <div className="text-[#672A37] text-base text-center">
              {content}
            </div>
            <div className="flex mt-[25px] justify-evenly">
              <Button
                variant="primary"
                className="mx-[5px] text-base w-full h-[44px] bg-[#F0F0F0] text-[#728BA4]"
                onClick={() => {
                  close();
                  resolve(false);
                }}
              >
                取消
              </Button>
              <Button
                variant="primary"
                className="mx-[5px] text-base w-full h-[44px]"
                onClick={() => {
                  close();
                  resolve(true);
                }}
              >
                确认
              </Button>
            </div>
            <div className="absolute w-full top-[-40px] left-0 flex justify-center">
              <CommonIcon name="confirmIcon" h={64} w={231} />
            </div>
          </div>
        ),
      });
    });
  },
};
