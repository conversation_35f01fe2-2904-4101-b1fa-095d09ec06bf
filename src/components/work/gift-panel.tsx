import { useGiftList } from "@/hooks/use-gift-list";
import { ScrollArea } from "@/ui/scroll-area";
import { GiftType, userWalletType } from "@/utils/enums";
import { Image, ProgressCircle } from "antd-mobile";
import classNames from "classnames";
import { DefaultLoadImage } from "../default-load-image";
import { Button } from "@/ui/button";
import { GiftResult } from "@/type/gift-result";
import Batter from "../batter";
import { useState } from "react";

interface Props {
  type: GiftType;
  multi?: boolean;
  onSendGift: (gift: GiftResult, number?: number) => void;
}

export const GiftPanel = (props: Props) => {
  const { type, multi, onSendGift } = props;

  const [selectedGift, setSelectedGift] = useState<GiftResult | undefined>();

  const { getByType } = useGiftList();

  const handleSendGift = (gift: GiftResult, number?: number) => {
    if (selectedGift?.id === gift.id) {
      onSendGift(gift, number);

      return;
    }

    setSelectedGift(gift);
  };

  return (
    <ScrollArea className="h-full">
      <div className="p-[15px] grid grid-cols-4">
        {getByType(type)?.map((item) => (
          <div key={item.id} onClick={() => handleSendGift(item)}>
            <Batter
              enabled={multi && selectedGift?.id === item.id}
              builder={(current, maxWait, number) => (
                <div
                  className="flex justify-center items-center relative"
                  style={{ width: "80px", height: "125px" }}
                >
                  {/* 内容圆形区域 */}
                  <div
                    className="flex flex-col justify-center items-center relative rounded-full bg-gradient-to-br from-[#FF3460] to-[#FF7B57]"
                    style={{
                      width: "80px",
                      height: "80px",
                    }}
                  >
                    {/* 连击数字 */}
                    <div className="flex gap-[10px] items-center text-white">
                      <span>x</span>
                      <span
                        className="text-[30px] font-bold"
                        style={{ transform: "skewX(-17deg)" }}
                      >
                        {number}
                      </span>
                    </div>
                    {/* 连击文字 */}
                    <div className="text-white text-xl" style={{ zoom: 0.5 }}>
                      连击中
                    </div>
                  </div>

                  {/* 进度条 */}
                  <div
                    className="absolute top-0 left-0"
                    style={{
                      width: "81px",
                      height: "81px",
                    }}
                  >
                    <ProgressCircle
                      percent={current / maxWait}
                      style={{
                        "--fill-color": "#fff",
                        "--track-color": "transparent",
                        "--size": "81px",
                        "--track-width": "2px",
                      }}
                    />
                  </div>
                </div>
              )}
              onFinish={(number) => {
                onSendGift(selectedGift!, number);
              }}
            >
              <div
                className={classNames(
                  "flex flex-col relative justify-evenly h-[125px] border-2 border-solid rounded-[15px]",
                  selectedGift?.id === item.id
                    ? "border-[#B573FF]"
                    : "border-app-bar-background"
                )}
              >
                <div className="w-full h-[70px] flex justify-center items-center">
                  <div className="h-[30px]">
                    <Image
                      src={item.image}
                      width="auto"
                      height="100%"
                      fit="contain"
                      placeholder={<DefaultLoadImage />}
                      fallback={<DefaultLoadImage />}
                    />
                  </div>
                </div>
                {selectedGift?.id !== item.id ? (
                  <div className="flex flex-col">
                    <span className="text-[13px] text-center">{item.name}</span>
                    <span className="text-[13px] text-[#6712FF] text-center line-clamp-1">
                      {item.presentPrice}
                      {userWalletType.bonus.label}
                    </span>
                  </div>
                ) : (
                  <div className="flex justify-center px-[5px]">
                    <Button
                      variant="primary"
                      className="text-[13px] h-[30px] w-full"
                    >
                      赠送
                    </Button>
                  </div>
                )}
                {item.vip ? (
                  <div className="absolute right-0 top-0 bg-[#FF5252] inline-flex px-[5px] rounded-l-[10px]">
                    <span
                      className="text-[20px] gift-shine-text"
                      style={{ zoom: 0.5 }}
                    >
                      VIP
                    </span>
                  </div>
                ) : null}
                {item.sign ? (
                  <div className="absolute left-0 top-0 bg-[#FF5252] inline-flex px-[5px] rounded-r-[10px]">
                    <span
                      className="text-[20px] gift-shine-text"
                      style={{ zoom: 0.5 }}
                    >
                      {item.sign}
                    </span>
                  </div>
                ) : null}
                {!item.sign &&
                item.originalPrice &&
                item.originalPrice !== item.presentPrice ? (
                  <div className="absolute left-0 top-0 bg-[#FF5252] inline-flex px-[5px] rounded-r-[10px]">
                    <span
                      className="text-[20px] gift-shine-text"
                      style={{ zoom: 0.5 }}
                    >
                      折扣
                    </span>
                  </div>
                ) : null}
              </div>
            </Batter>
          </div>
        ))}
      </div>
    </ScrollArea>
  );
};
