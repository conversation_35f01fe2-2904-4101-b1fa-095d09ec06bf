import { useModal } from "@/hooks/use-modal";
import { Popup } from "antd-mobile";
import { CommonIcon } from "../common-icon";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { UserTransactionType } from "@/utils/enums";

interface Props {
  trigger: React.ReactNode;
}

export const TradeCreateModal = ({ trigger }: Props) => {
  const { open, openModal, closeModal } = useModal();

  const { navigateRoute } = useNavigateRoute();

  const handleClick = (type: UserTransactionType) => {
    closeModal();
    navigateRoute("/trade-create", { type });
  };

  return (
    <>
      <span onClick={openModal}>{trigger}</span>
      <Popup
        destroyOnClose
        visible={open}
        bodyClassName="rounded-t-[10px]"
        onMaskClick={closeModal}
        onClose={closeModal}
      >
        <div className="flex bg-app-bar-background h-[250px]">
          <div
            className="pt-[25px] flex-1 flex flex-col gap-[5px] items-center"
            onClick={() => handleClick(UserTransactionType.出售)}
          >
            <CommonIcon name="tradeBuy" w={90} h={90} />
            <span className="text-[15px]">我要出售</span>
          </div>
          <div
            className="pt-[25px] flex-1 flex flex-col gap-[5px] items-center"
            onClick={() => handleClick(UserTransactionType.求购)}
          >
            <CommonIcon name="tradeSell" w={90} h={90} />
            <span className="text-[15px]">我要购买</span>
          </div>
        </div>
      </Popup>
    </>
  );
};
