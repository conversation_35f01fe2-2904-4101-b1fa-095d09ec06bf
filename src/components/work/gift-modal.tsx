import { GiftType, giftTypeConfig, userWalletType } from "@/utils/enums";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Swiper, Swiper<PERSON><PERSON>, Ta<PERSON> } from "antd-mobile";
import { useRef, useState } from "react";
import { GiftResult } from "@/type/gift-result";
import { Button } from "@/ui/button";
import { useModal } from "@/hooks/use-modal";
import { Input } from "@/ui/input";
import { useUserWallet } from "@/hooks/use-user-wallet";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import "./gift-modal.less";
import { GiftPanel } from "./gift-panel";

const giftTypeTabItems = [
  {
    key: GiftType.General.toString(),
    title: giftTypeConfig[GiftType.General],
  },
  {
    key: GiftType.Heart.toString(),
    title: giftTypeConfig[GiftType.Heart],
  },
];

interface Props {
  open: boolean;
  onClose: () => void;
  onSend?: (gift: GiftResult, note?: string) => void;
}

export const GiftModal = (props: Props) => {
  const { open, onClose, onSend } = props;

  const [activeIndex, setActiveIndex] = useState(0);
  const [giftNote, setGiftNote] = useState<string>();

  const selectedGiftRef = useRef<GiftResult | undefined>();

  const { open: openNoteModal, openModal, closeModal } = useModal();

  const { getBalanceByType } = useUserWallet();
  const { navigateRoute } = useNavigateRoute();

  const swiperRef = useRef<SwiperRef>(null);

  const handleSendGift = (gift: GiftResult) => {
    // 赠送
    if (gift.type === GiftType.Heart) {
      selectedGiftRef.current = gift;
      openModal();
    } else {
      onSend?.(gift);
      onClose();
    }
  };

  const handleSendNoteGift = () => {
    if (selectedGiftRef.current) {
      onSend?.(selectedGiftRef.current, giftNote);
      onClose();
    }
  };

  const handleCloseNoteModal = () => {
    closeModal();
    setGiftNote(undefined);
  };

  const handleRecharge = () => {
    navigateRoute("/recharge");
  };

  return (
    <>
      <Popup
        destroyOnClose
        visible={open}
        bodyClassName="gift-modal rounded-t-[10px] bg-app-bar-background"
        onMaskClick={onClose}
        onClose={onClose}
      >
        <div className="h-[390px] flex flex-col">
          <div className="flex-1 h-0 flex flex-col">
            <Tabs
              activeKey={giftTypeTabItems[activeIndex].key.toString()}
              onChange={(key) => {
                const index = giftTypeTabItems.findIndex(
                  (item) => item.key === key
                );
                setActiveIndex(index);
                swiperRef.current?.swipeTo(index);
              }}
            >
              {giftTypeTabItems.map((item) => (
                <Tabs.Tab title={item.title} key={item.key} />
              ))}
            </Tabs>
            <Swiper
              direction="horizontal"
              loop
              indicator={() => null}
              ref={swiperRef}
              defaultIndex={activeIndex}
              onIndexChange={(index) => {
                setActiveIndex(index);
              }}
              className="flex-1"
            >
              <Swiper.Item>
                <GiftPanel
                  multi={false}
                  type={GiftType.General}
                  onSendGift={handleSendGift}
                />
              </Swiper.Item>
              <Swiper.Item>
                <GiftPanel
                  multi={false}
                  type={GiftType.Heart}
                  onSendGift={handleSendGift}
                />
              </Swiper.Item>
            </Swiper>
          </div>
          <Divider className="border-divider my-2 mx-[15px]" />
          <div className="flex items-center gap-[15px] px-[15px] h-[40px]">
            <div className="flex-1 flex gap-2">
              <span>
                {getBalanceByType(userWalletType.bonus.value)}
                {userWalletType.bonus.label}
              </span>
              <span>
                {getBalanceByType(userWalletType.currency.value)}
                {userWalletType.currency.label}
              </span>
              <span>
                {getBalanceByType(userWalletType.bean.value)}
                {userWalletType.bean.label}
              </span>
            </div>
            <Button
              variant="secondary"
              className="w-[65px] h-[30px] text-[15px]"
              onClick={handleRecharge}
            >
              充值
            </Button>
          </div>
        </div>
      </Popup>
      <Popup
        destroyOnClose
        visible={openNoteModal}
        bodyClassName="gift-modal rounded-t-[10px] bg-app-bar-background"
        onMaskClick={handleCloseNoteModal}
        onClose={handleCloseNoteModal}
      >
        <div className="h-[350px] p-[15px]">
          <div className="flex flex-col items-center gap-[15px]">
            <span className="text-[17px]">请输入心意详细</span>
            <Input
              className="bg-hint-color/10 rounded-[5px] border-none focus:border-none"
              value={giftNote}
              onChange={(e) => setGiftNote(e.target.value)}
            />
            <Button
              variant="primary"
              size="lg"
              className="w-full rounded-[5px]"
              disabled={!giftNote?.trim()}
              onClick={handleSendNoteGift}
            >
              完成
            </Button>
            <span className="心意礼物会上心意墙，请输入你想对收礼人说的话!">
              赠送礼物后，主播会收到礼物信息
            </span>
          </div>
        </div>
      </Popup>
    </>
  );
};
