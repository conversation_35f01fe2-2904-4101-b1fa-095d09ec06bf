import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { UserOrderDetailedResult } from "@/type/user-order-detailed-result";
import { UserInfo } from "./user-info";
import { Image } from "antd-mobile";
import { DefaultLoadImage } from "../default-load-image";

interface Props {
  data: UserOrderDetailedResult;
  // 显示用户信息(商家则会显示用户信息，用户则会显示商家信息)
  showUserInfo?: boolean;
}

export const UserOrderMerchandiseItem = (props: Props) => {
  const { data, showUserInfo = false } = props;

  const { data: userInfo } = useFullUserInfo();

  const isMerchant = data.merchant?.id && data.merchant.id === userInfo?.id;

  return (
    <div className="flex flex-col">
      {showUserInfo && !isMerchant && data.merchant ? (
        <div className="mb-[15px]">
          <UserInfo user={data.merchant} size={47} />
        </div>
      ) : null}
      {showUserInfo && isMerchant && data.user ? (
        <div className="mb-[15px]">
          <UserInfo user={data.user} size={47} />
        </div>
      ) : null}
      <div className="flex gap-[15px]">
        <div className="w-[69px] h-[69px]">
          <Image
            src={data.cover}
            width="100%"
            height="100%"
            fit="cover"
            className="rounded-[5px]"
            placeholder={<DefaultLoadImage />}
            fallback={<DefaultLoadImage />}
            onLoad={() => {}}
          />
        </div>
        <div className="flex-1 w-0 flex flex-col justify-evenly">
          <div className="w-full flex items-center gap-[2px]">
            <span className="flex-none px-[5px] text-white text-xs rounded-[5px] bg-[#FF3460]">
              {data.targetLabel}
            </span>
            <span className="text-[15px] line-clamp-1">{data.title}</span>
          </div>
          {data.content ? (
            <div className="line-clamp-2">{data.content}</div>
          ) : null}
        </div>
        <div>
          <span className="text-sm text-[#FF005C] font-bold">
            ¥ {isMerchant ? data.totalPrice : data.shouldPayAmount}
          </span>
        </div>
      </div>
    </div>
  );
};
