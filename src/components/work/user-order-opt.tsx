import {
  cancelOrder,
  orderReceipt,
  orderRefund,
  orderShip,
} from "@/lib/api/user";
import { UserOrderDetailedResult } from "@/type/user-order-detailed-result";
import { Button } from "@/ui/button";
import { UserOrderState } from "@/utils/enums";
import { useMutation } from "@tanstack/react-query";
import { confirmDialog } from "./confirm-dialog";
import { Toast } from "antd-mobile";
import { Textarea } from "@/ui/textarea";
import { useRef } from "react";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

interface Props {
  data: UserOrderDetailedResult;
  merchant: boolean;
  onRefresh: () => void;
}

export const UserOrderOpt = (props: Props) => {
  const { data, merchant, onRefresh } = props;

  const trackingNumberRef = useRef<HTMLTextAreaElement>(null);
  const applyReasonRef = useRef<HTMLTextAreaElement>(null);

  const { navigateRoute } = useNavigateRoute();

  const cancelOrderMutation = useMutation({
    mutationFn: cancelOrder,
  });

  const handleCancelOrder = async () => {
    const result = await confirmDialog.show(
      "关闭后此订单无法再次打开!",
      "是否关闭订单?"
    );

    if (result) {
      Toast.show({
        icon: "loading",
        content: "请稍后...",
        duration: 0,
      });

      const { ok } = await cancelOrderMutation.mutateAsync(data.id!);

      if (ok) {
        Toast.show({
          content: "订单关闭成功",
        });
        onRefresh();
      }
    }
  };

  const orderShipMutation = useMutation({
    mutationFn: orderShip,
  });

  const handleOrderShip = async () => {
    const result = await confirmDialog.show(
      <div>
        <Textarea
          ref={trackingNumberRef}
          rows={3}
          placeholder="如果存在快递单号，请在此处输入"
          className="rounded-[5px] bg-scaffold-background px-[15px] py-[10px]"
        />
      </div>,
      "是否确认发货?"
    );

    if (result) {
      const trackingNumber = trackingNumberRef.current?.value;

      if (trackingNumber) {
        Toast.show({
          icon: "loading",
          content: "请稍后...",
          duration: 0,
        });

        const { ok } = await orderShipMutation.mutateAsync({
          id: data.id!,
          trackingNumber,
        });

        if (ok) {
          Toast.show({
            content: "订单发货成功",
          });
          onRefresh();
        }
      }
    }
  };

  const orderReceiptMutation = useMutation({
    mutationFn: orderReceipt,
  });

  const handleOrderReceipt = async () => {
    const result = await confirmDialog.show(
      <div>
        <Textarea
          ref={applyReasonRef}
          rows={3}
          placeholder="如果存在快递单号，请在此处输入"
          className="rounded-[5px] bg-scaffold-background px-[15px] py-[10px]"
        />
      </div>,
      "是否确认收货?"
    );

    if (result) {
      Toast.show({
        icon: "loading",
        content: "请稍后...",
        duration: 0,
      });

      const { ok } = await orderReceiptMutation.mutateAsync(data.id!);

      if (ok) {
        Toast.show({
          content: "订单收货成功",
        });
        onRefresh();
      }
    }
  };

  const orderRefundMutation = useMutation({
    mutationFn: orderRefund,
  });

  const handleOrderRefund = async () => {
    const result = await confirmDialog.show("请输入退款理由", "申请售后");

    if (result) {
      const applyReason = applyReasonRef.current?.value;

      if (applyReason) {
        Toast.show({
          icon: "loading",
          content: "请稍后...",
          duration: 0,
        });

        const { ok, data: refundData } = await orderRefundMutation.mutateAsync({
          id: data.id!,
          applyReason,
        });

        if (ok) {
          Toast.show({
            content: "退款申请成功",
          });
          onRefresh();

          navigateRoute("/user-order-refund-detail", {
            id: refundData?.id,
          });
        }
      }
    }
  };

  const handlePay = () => {
    navigateRoute("/create-order", {
      id: data.id,
    });
  };

  const handleRefund = () => {
    navigateRoute("/user-order-refund");
  };

  const handleContactBuyer = () => {
    navigateRoute("/chat", {
      uid: data.user?.id,
    });
  };

  const handleContactMerchant = () => {
    navigateRoute("/chat", {
      uid: data.merchant?.id,
    });
  };

  return (
    <div className="flex w-full">
      {/* 取消订单：未支付状态用户和商家均可取消、未发货状态仅商家可以取消 */}
      {data.state === UserOrderState.WaitingPay ||
      (merchant && data.state === UserOrderState.WaitForDelivery) ? (
        <span className="px-[2px] text-hint-color" onClick={handleCancelOrder}>
          取消订单
        </span>
      ) : null}
      {/* 售后（买家显示），当处于已发货，但是未申请售后时，可以申请售后 */}
      {!merchant &&
      data.state === UserOrderState.Shipped &&
      !data.existRefund ? (
        <span className="px-[2px] text-hint-color" onClick={handleOrderRefund}>
          申请售后
        </span>
      ) : null}
      {data.existRefund ? (
        <span className="px-[2px] text-hint-color" onClick={handleRefund}>
          售后详细
        </span>
      ) : null}
      <div className="flex-1" />
      <div className="flex">
        {/*  联系买家按钮，仅卖家显示 */}
        {merchant ? (
          <div className="ml-[5px]">
            <Button
              variant="secondary"
              className="w-[73px] h-[29px] text-[13px]"
              onClick={handleContactBuyer}
            >
              联系买家
            </Button>
          </div>
        ) : null}
        {/*  联系商家按钮，仅买家显示 */}
        {!merchant && data.merchant ? (
          <div className="ml-[5px]">
            <Button
              variant="secondary"
              className="w-[73px] h-[29px] text-[13px]"
              onClick={handleContactMerchant}
            >
              联系商家
            </Button>
          </div>
        ) : null}
        {/* 付款按钮，仅买家显示 */}
        {!merchant && data.state === UserOrderState.WaitingPay ? (
          <div className="ml-[5px]">
            <Button
              variant="primary"
              className="w-[73px] h-[29px] text-[13px]"
              onClick={handlePay}
            >
              去付款
            </Button>
          </div>
        ) : null}
        {/* 发货按钮，仅卖家显示 */}
        {merchant && data.state === UserOrderState.WaitForDelivery ? (
          <div className="ml-[5px]">
            <Button
              variant="primary"
              className="w-[73px] h-[29px] text-[13px]"
              onClick={handleOrderShip}
            >
              发货
            </Button>
          </div>
        ) : null}
        {!merchant && data.state === UserOrderState.Shipped ? (
          <div className="ml-[5px]">
            <Button
              variant="primary"
              className="w-[73px] h-[29px] text-[13px]"
              onClick={handleOrderReceipt}
            >
              收货
            </Button>
          </div>
        ) : null}
      </div>
    </div>
  );
};
