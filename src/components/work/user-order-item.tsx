import { UserOrderDetailedResult } from "@/type/user-order-detailed-result";
import { UserAvatar } from "../user-avatar";
import { UserInfoModal, UserInfoModalRef } from "./user-info-modal";
import { UserOrderState, userOrderStateConfig } from "@/utils/enums";
import { useMemo } from "react";
import dayjs from "dayjs";
import { useCountDownTimer } from "@/hooks/use-count-down-timer";
import { UserOrderMerchandiseItem } from "./user-order-merchandise-item";
import { UserOrderOpt } from "./user-order-opt";
import { useRef } from "react";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { formatDuration } from "@/utils/format-duration";

interface Props {
  data: UserOrderDetailedResult;
  merchant: boolean;
  onRefresh: () => void;
}

export const UserOrderItem = (props: Props) => {
  const { data, merchant, onRefresh } = props;

  const countDownTime = useMemo(() => {
    if (
      !data.existRefund &&
      data.state === UserOrderState.WaitingPay &&
      data.expireDate
    ) {
      return dayjs(data.expireDate).diff(dayjs(), "second");
    }

    if (
      !data.existRefund &&
      data.state === UserOrderState.Shipped &&
      data.autoReceiveDate
    ) {
      return dayjs(data.autoReceiveDate).diff(dayjs(), "second");
    }

    return 0;
  }, [data]);

  const { seconds, isRunning } = useCountDownTimer(countDownTime, true);

  const userInfoModalRef = useRef<UserInfoModalRef>(null);

  const openUserInfoModal = (id: string) => {
    userInfoModalRef.current?.open(id);
  };

  const { navigateRoute } = useNavigateRoute();

  const handleClick = () => {
    navigateRoute("/user-order-detail", { id: data.id });
  };

  return (
    <div className="text-[13px] flex flex-col gap-3" onClick={handleClick}>
      <div className="flex">
        {/* 商家信息(仅非商家交易时才显示) */}
        {!merchant && data.merchant ? (
          <>
            <div
              className="flex-1 flex items-center gap-2"
              onClick={() => openUserInfoModal(data?.merchant?.id!)}
            >
              <UserAvatar src={data?.merchant?.avatarUrl!} size={23} />
              <span>{data?.merchant?.nickname}</span>
            </div>
          </>
        ) : null}
        {/* 用户信息(仅商家交易时才显示) */}
        {merchant && data.user ? (
          <>
            <div
              className="flex-1 flex items-center gap-2"
              onClick={() => openUserInfoModal(data?.user?.id!)}
            >
              <UserAvatar src={data?.user?.avatarUrl!} size={23} />
              <span>{data?.user?.nickname}</span>
            </div>
          </>
        ) : null}
        <div>
          {!data.existRefund &&
          data.state !== UserOrderState.WaitingPay &&
          data.state !== UserOrderState.Shipped ? (
            <span className="text-[#FF9212]">
              {userOrderStateConfig[data.state!] ?? "未知状态"}
            </span>
          ) : null}
          {!data.existRefund &&
          data.state === UserOrderState.WaitingPay &&
          data.expireDate ? (
            <span className="text-[#FF9212]">
              {isRunning ? `${formatDuration(seconds)}后失效` : "订单已失效"}
            </span>
          ) : null}
          {!data.existRefund &&
          data.state === UserOrderState.Shipped &&
          data.autoReceiveDate ? (
            <span className="text-[#FF9212]">
              {isRunning
                ? `${formatDuration(seconds)}后自动确认收货`
                : "订单已收货"}
            </span>
          ) : null}
          {data.existRefund ? (
            <span className="text-[#FF9212]">售后中</span>
          ) : null}
        </div>
      </div>
      <UserOrderMerchandiseItem data={data} />
      <UserOrderOpt data={data} merchant={merchant} onRefresh={onRefresh} />
      <UserInfoModal ref={userInfoModalRef} />
    </div>
  );
};
