import { GiftType, userWalletType } from "@/utils/enums";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>wi<PERSON>, <PERSON>wi<PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "antd-mobile";
import { useRef, useState } from "react";
import { GiftResult } from "@/type/gift-result";
import { <PERSON><PERSON> } from "@/ui/button";
import { useUserWallet } from "@/hooks/use-user-wallet";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { GiftPanel } from "./gift-panel";
import { BackpackGiftPanel } from "./backpack-gift-panel";
import { MountsList } from "@/pages/mounts-list/components/mounts-list";
import { JoinSpecialList } from "@/pages/join-special-list/components/join-special-list";
import { UserBackpackGiftResult } from "@/type/user-backpack-gift-result";
import "./gift-modal.less";

const liveGiftTabItems = [
  {
    key: "gift",
    title: "礼物",
  },
  {
    key: "backpack",
    title: "背包",
  },
  {
    key: "mounts",
    title: "坐骑",
  },
  {
    key: "join-special",
    title: "入场名片",
  },
];

interface Props {
  open: boolean;
  onClose: () => void;
  onSend?: (
    gift: GiftResult | undefined,
    backpackGift: UserBackpackGiftResult | undefined,
    number: number
  ) => void;
}

export const LiveGiftModal = (props: Props) => {
  const { open, onClose, onSend } = props;

  const [activeIndex, setActiveIndex] = useState(0);

  const { getBalanceByType } = useUserWallet();
  const { navigateRoute } = useNavigateRoute();

  const swiperRef = useRef<SwiperRef>(null);

  const handleSelectGift = (gift: GiftResult, number?: number) => {
    onSend?.(gift, undefined, number ?? 1);
    onClose();
  };

  const handleSelectBackpackGift = (
    backpackGift: UserBackpackGiftResult,
    number?: number
  ) => {
    onSend?.(undefined, backpackGift, number ?? 1);
    onClose();
  };

  const handleRecharge = () => {
    navigateRoute("/recharge");
  };

  return (
    <>
      <Popup
        destroyOnClose
        visible={open}
        bodyClassName="gift-modal rounded-t-[10px] bg-app-bar-background"
        onMaskClick={onClose}
        onClose={onClose}
      >
        <div className="h-[390px] flex flex-col">
          <div className="flex-1 h-0 flex flex-col">
            <Tabs
              activeKey={liveGiftTabItems[activeIndex].key.toString()}
              onChange={(key) => {
                const index = liveGiftTabItems.findIndex(
                  (item) => item.key === key
                );
                setActiveIndex(index);
                swiperRef.current?.swipeTo(index);
              }}
            >
              {liveGiftTabItems.map((item) => (
                <Tabs.Tab title={item.title} key={item.key} />
              ))}
            </Tabs>
            <Swiper
              direction="horizontal"
              loop
              indicator={() => null}
              ref={swiperRef}
              defaultIndex={activeIndex}
              onIndexChange={(index) => {
                setActiveIndex(index);
              }}
              className="flex-1"
            >
              <Swiper.Item>
                <GiftPanel
                  multi
                  type={GiftType.Live}
                  onSendGift={handleSelectGift}
                />
              </Swiper.Item>
              <Swiper.Item>
                <BackpackGiftPanel
                  multi
                  onSendGift={handleSelectBackpackGift}
                />
              </Swiper.Item>
              <Swiper.Item>
                <MountsList />
              </Swiper.Item>
              <Swiper.Item>
                <JoinSpecialList />
              </Swiper.Item>
            </Swiper>
          </div>
          <Divider className="border-divider my-2 mx-[15px]" />
          <div className="flex items-center gap-[15px] px-[15px] h-[40px]">
            <div className="flex-1 flex gap-2">
              <span>
                {getBalanceByType(userWalletType.bonus.value)}
                {userWalletType.bonus.label}
              </span>
              <span>
                {getBalanceByType(userWalletType.currency.value)}
                {userWalletType.currency.label}
              </span>
              <span>
                {getBalanceByType(userWalletType.bean.value)}
                {userWalletType.bean.label}
              </span>
            </div>
            <Button
              variant="secondary"
              className="w-[65px] h-[30px] text-[15px]"
              onClick={handleRecharge}
            >
              充值
            </Button>
          </div>
        </div>
      </Popup>
    </>
  );
};
