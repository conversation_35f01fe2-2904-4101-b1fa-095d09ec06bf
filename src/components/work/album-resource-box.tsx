import { useConfigList } from "@/hooks/use-config-list";
import { UserAlbumResource } from "@/type/user-album-resource";
import { ResourceBox } from "../resource-box";
import { CommonAuditState, commonAuditStateConfig } from "@/utils/enums";

interface Props {
  data: UserAlbumResource;
  buy: boolean;
  size: number;
  onPreview?: () => void;
}

export const AlbumResourceBox = (props: Props) => {
  const { data, buy, size, onPreview } = props;

  const { getSystemConfig } = useConfigList();

  const blur = buy ? 0 : getSystemConfig("DYNAMIC_CHARGE_RESOURCE_BLUR");

  return (
    <div className="relative">
      <ResourceBox
        w={size}
        h={size}
        data={data.resource!}
        blur={Number(blur)}
        onPreview={onPreview}
      />
      {commonAuditStateConfig.find((item) => item.value === data.state)
        ?.image && data.state !== CommonAuditState.Pass ? (
        <div className="absolute bottom-[5px] right-[10px]">
          <div className="flex items-center justify-center w-full h-full">
            <img
              src={
                commonAuditStateConfig.find((item) => item.value === data.state)
                  ?.image
              }
              style={{
                width: `${size / 16 / 3}rem`,
                height: `${size / 16 / 3}rem`,
              }}
            />
          </div>
        </div>
      ) : null}
    </div>
  );
};
