import { useModal } from "@/hooks/use-modal";
import { CascaderProps, Cascader, Toast } from "antd-mobile";
import { cityData } from "./data";
import "./style.less";
import { useMemo } from "react";

interface Props {
  placeholder?: string;
  value?: string[];
  onChange?: (value?: string[]) => void;
}

export const CityPicker = (props: Props) => {
  const { value, onChange, placeholder = "请选择" } = props;

  const { open, openModal, closeModal } = useModal();

  const finalValue = useMemo(() => {
    if (!value?.length) {
      return undefined;
    }

    const [provinceLabel, cityLabel, areaLabel] = value;

    const provinceValue = cityData.find(
      (province) => province.label === provinceLabel
    );

    const cityValue = provinceValue?.children?.find(
      (city) => city.label === cityLabel
    );

    const areaValue = cityValue?.children?.find(
      (area) => area.label === areaLabel
    );

    if (!provinceValue || !cityValue || !areaValue) {
      return undefined;
    }

    return [provinceValue.value, cityValue.value, areaValue.value];
  }, [value]);

  const handleConfirm: CascaderProps["onConfirm"] = (value, extend) => {
    const { isLeaf, items } = extend;
    if (!isLeaf) {
      Toast.show("请选择到最后一级");
      return;
    }

    onChange?.(items.map((item) => item?.label as string));
    closeModal();
  };

  return (
    <>
      <div
        onClick={openModal}
        className="border-b border-solid border-divider pb-1"
      >
        {value?.length ? (
          <span>{value.join("")}</span>
        ) : (
          <span className=" text-hint-color">{placeholder}</span>
        )}
      </div>
      <Cascader
        className="city-picker bg-app-bar-background"
        value={finalValue}
        options={cityData}
        visible={open}
        onConfirm={handleConfirm}
        onClose={closeModal}
      />
    </>
  );
};
