import { Image, ImageProps } from "antd-mobile";

const defaultSize = 18;

export interface IconProps {
  src: string;
  className?: string;
  w?: number;
  h?: number;
  fit?: "contain" | "cover";
  onClick?: ImageProps["onClick"];
}

export const Icon = (props: IconProps) => {
  const {
    src,
    className,
    w = defaultSize,
    h = defaultSize,
    fit = "cover",
    onClick,
  } = props;

  return (
    <Image
      className={className}
      src={src}
      width={`${w / 16}rem`}
      height={`${h / 16}rem`}
      fit={fit}
      placeholder={null}
      onClick={onClick}
    />
  );
};
