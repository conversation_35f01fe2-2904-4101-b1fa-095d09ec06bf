import classNames from "classnames";
import { ReactNode } from "react";
import { ArrowIcon } from "./svg-icon";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

interface Props {
  showBorder?: boolean;
  canBack?: boolean;
  forceWhiteBackIcon?: boolean;
  title?: string;
  content?: ReactNode;
  className?: string;
  style?: React.CSSProperties;
  action?: ReactNode;
}

export const NavigationBar = (props: Props) => {
  const {
    showBorder = false,
    canBack = false,
    forceWhiteBackIcon = false,
    title = "",
    content,
    className,
    style,
    action,
  } = props;

  const { navigateBack } = useNavigateRoute();

  const handleBack = () => {
    navigateBack();
  };

  return (
    <header
      className={classNames(
        "h-[46px] border-solid border-[#DADADA] dark:border-[#3A3A3A] bg-app-bar-background  text-foreground",
        className
      )}
      style={{
        ...style,
        borderBottomWidth: showBorder ? "1px" : "none",
      }}
    >
      <div className="flex justify-between px-[15px] items-center h-full">
        {canBack ? (
          <span
            className={classNames(forceWhiteBackIcon ? "text-white" : "")}
            onClick={handleBack}
          >
            <ArrowIcon size={18} className="rotate-180" />
          </span>
        ) : (
          <span />
        )}
        {content ? (
          content
        ) : (
          <span className="text-lg font-medium">{title}</span>
        )}
        {action ? action : <span />}
      </div>
    </header>
  );
};
