import { useRef } from "react";
import { PlusIcon } from "./svg-icon";

interface Props {
  accept?: "image/*" | "video/*" | "image/*,video/*";
  maxCount?: number;
  className?: string;
  placeholder?: React.ReactNode;
  onSelected: (files: File[]) => void;
}

export const BaseMediaUpload = (props: Props) => {
  const {
    accept = "image/*,video/*",
    maxCount = 9,
    className,
    placeholder,
    onSelected,
  } = props;

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files ?? []);

    onSelected(files.slice(0, maxCount));

    // 重置文件输入元素的值，确保下次选择相同文件时也能触发onChange事件
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className={className}>
      <input
        type="file"
        accept={accept}
        ref={fileInputRef}
        multiple
        style={{ display: "none" }}
        onChange={handleFileChange}
      />
      <div
        className="w-full h-full flex flex-col items-center justify-center gap-1 rounded-[5px] bg-scaffold-background"
        onClick={() => fileInputRef.current?.click()}
      >
        {placeholder || (
          <>
            <PlusIcon />
            <span>上传</span>
          </>
        )}
      </div>
    </div>
  );
};
