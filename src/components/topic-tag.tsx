import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { DynamicTopic } from "@/type/dynamic-topic";
import { MouseEvent } from "react";

interface Props {
  data: DynamicTopic;
}

export const TopicTag = ({ data }: Props) => {
  const { name } = data;

  const { navigateRoute } = useNavigateRoute();

  const handleClick = (e: MouseEvent<HTMLSpanElement>) => {
    e.stopPropagation();

    navigateRoute("/topic-detail", { id: data.id, name: data.name });
  };

  return (
    <span
      className="h-[20px] px-[3px] flex items-center rounded-[3px] bg-[#9921FF]/20 text-[#9921FF]"
      onClick={handleClick}
    >
      <span
        className="text-[22px]"
        style={{
          zoom: 0.5,
        }}
      >
        #{name}#
      </span>
    </span>
  );
};
