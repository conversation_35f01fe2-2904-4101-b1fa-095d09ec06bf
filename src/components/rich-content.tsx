import { Fragment, ReactNode } from "react";
import {
  type BuildType,
  allBuildTypes,
  getBuildByType,
} from "@/utils/text-regexp-build";

interface Props {
  content: string;
  builds: BuildType[];
  onAtClick?: (text: string) => void;
  onTopicClick?: (text: string) => void;
}

export const RichContent = (props: Props) => {
  const { content, builds, onAtClick, onTopicClick } = props;

  const sortBuilds = allBuildTypes.filter((build) => builds.includes(build));
  const combinedRegexp = new RegExp(
    sortBuilds.map((build) => getBuildByType(build).regexp.source).join("|"),
    "ig"
  );

  let lastIndex = 0;
  const parts: ReactNode[] = [];

  for (const item of content.matchAll(combinedRegexp)) {
    const match = item[0];
    const { index = -1 } = item;

    if (index > lastIndex) {
      //  前置内容插入
      parts.push(<span>{content.slice(lastIndex, index)}</span>);
    }

    const targetBuildType = sortBuilds.find((build) => {
      const reg = new RegExp(`^${getBuildByType(build).regexp.source}$`);

      return reg.test(match);
    });

    if (targetBuildType) {
      const build = getBuildByType(targetBuildType);
      if (targetBuildType === "atTextBuild") {
        parts.push(build.render(match, onAtClick));
      } else if (targetBuildType === "topicTextBuild") {
        parts.push(build.render(match, onTopicClick));
      } else {
        parts.push(build.render(match));
      }
    }

    lastIndex = index + match.length;
  }

  if (lastIndex < content.length) {
    parts.push(<span>{content.slice(lastIndex)}</span>);
  }

  return (
    <div>
      {parts.map((part, i) => (
        <Fragment key={i}>{part}</Fragment>
      ))}
    </div>
  );
};
