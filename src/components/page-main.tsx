import { PropsWithChildren, ReactNode, useMemo } from "react";
import { StateView } from "./state-view";
import { ScrollArea } from "@/ui/scroll-area";
import { PullToRefresh } from "./pull-to-refresh";
import classNames from "classnames";

interface Props {
  className?: string;
  scrollClassName?: string;
  isLoading?: boolean;
  isEmpty?: boolean;
  needScroll?: boolean;
  onRefresh?: () => Promise<void>;
  extra?: ReactNode;
  scrollRef?: React.RefObject<HTMLDivElement>;
}

export const PageMain = (props: PropsWithChildren<Props>) => {
  const {
    className,
    scrollClassName,
    isLoading,
    isEmpty,
    children,
    extra,
    onRefresh,
    needScroll = true,
    scrollRef,
  } = props;

  const renderChildren = useMemo(() => {
    if (needScroll) {
      return (
        <ScrollArea
          className={classNames("h-full", scrollClassName)}
          ref={scrollRef}
        >
          {onRefresh ? (
            <PullToRefresh onRefresh={onRefresh}>{children}</PullToRefresh>
          ) : (
            children
          )}
        </ScrollArea>
      );
    }

    return onRefresh ? (
      <PullToRefresh onRefresh={onRefresh}>{children}</PullToRefresh>
    ) : (
      children
    );
  }, [children, needScroll, onRefresh, scrollClassName, scrollRef]);

  return (
    <main className={classNames("w-full flex-1 h-0", className)}>
      <div className="flex h-full w-full flex-col">
        <StateView isLoading={isLoading} isEmpty={isEmpty}>
          <div className="flex-1 h-0">{renderChildren}</div>
          {extra ? <div className="flex-none">{extra}</div> : null}
        </StateView>
      </div>
    </main>
  );
};
