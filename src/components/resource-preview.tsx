import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import {
  MultiImageViewer,
  MultiImageViewerRef,
} from "./image-viewer/image-viewer";
import { ResourcePreviewData } from "@/type/resource-preview-data";
import { usePreviewUrl } from "@/hooks/use-preview-url";
import { ResourceInfo } from "./image-viewer/slide";

export interface ResourcePreviewRef {
  swipeTo: (index: number) => void;
}

interface ResourcePreviewProps {
  data: ResourcePreviewData[];
  onClose?: () => void;
}

export const ResourcePreview = forwardRef<
  ResourcePreviewRef,
  ResourcePreviewProps
>((props, ref) => {
  const { data, onClose: propsOnClose } = props;

  const [visible, setVisible] = useState(false);
  const [activeIndex, setActiveIndex] = useState(-1);
  const [resourceInfo, setResourceInfo] = useState<ResourceInfo[]>(
    data.map((item) => {
      return {
        thumbnailImage: item.thumbnailImage,
        loading: false,
        url: undefined,
        preview: item.preview,
        type: item.video ? "video" : "image",
      };
    })
  );

  const previewRef = useRef<MultiImageViewerRef>(null);

  const { url, isLoading } = usePreviewUrl(data[activeIndex]);

  useImperativeHandle(ref, () => ({
    swipeTo: (index: number) => {
      setVisible(true);
      setActiveIndex(index);
      previewRef.current?.swipeTo(index);
    },
  }));

  useEffect(() => {
    setResourceInfo((prev) => {
      return prev.map((item, index) => {
        if (index === activeIndex) {
          return {
            ...item,
            loading: isLoading,
            url: index === activeIndex ? url : item.url,
          };
        }
        return item;
      });
    });
  }, [isLoading, url, activeIndex]);

  const onClose = () => {
    setVisible(false);
    propsOnClose?.();
  };

  return (
    <MultiImageViewer
      ref={previewRef}
      visible={visible}
      list={resourceInfo}
      onClose={onClose}
      onIndexChange={(index) => setActiveIndex(index)}
    />
  );
});
