import { Resource } from "@/type/resource";
import { Image } from "antd-mobile";
import { DefaultLoadImage } from "./default-load-image";
import { PlayIcon } from "./svg-icon";
import { formatDuration } from "@/utils/format-duration";

interface Props {
  w: number;
  h: number;
  data: Resource;
  blur?: number;
  onPreview?: () => void;
}

export const ResourceBox = (props: Props) => {
  const { w, h, data, blur = 0, onPreview } = props;

  return (
    <div
      className="relative overflow-hidden rounded-[5px]"
      style={{
        width: `${w / 16}rem`,
        height: `${h / 16}rem`,
      }}
      onClick={onPreview}
    >
      <Image
        src={data.images?.Thumbnail?.url}
        width="100%"
        height="100%"
        fit="cover"
        className="rounded-[5px]"
        placeholder={<DefaultLoadImage />}
        fallback={<DefaultLoadImage />}
        onLoad={() => {}}
        style={{ filter: `blur(${blur}px)` }}
      />
      <div className="absolute top-0 left-0 bg-white/15" />
      {data.duration ? (
        <div className="flex absolute top-0 left-0 w-full h-full justify-center items-center">
          <PlayIcon size={30} className="text-white/50 rounded-full" />
          <div className="text-white absolute bottom-[3px] right-[5px]">
            {formatDuration(data.duration)}
          </div>
        </div>
      ) : null}
    </div>
  );
};
