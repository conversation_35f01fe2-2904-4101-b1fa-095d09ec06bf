import { calcResourceLayout } from "@/utils";
import { Resource } from "@/type/resource";
import { Charge } from "@/type/dynamic-result";
import { ResourceItem } from "./resource-item";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

interface Props {
  resources: Resource[];
  charge?: Charge;
}

export const ResourceContent = ({ resources, charge }: Props) => {
  const { col, ratio, gap } = calcResourceLayout(resources.length);

  const { navigateRoute } = useNavigateRoute();

  const previewList = resources.map((item) => {
    return {
      id: item.id,
      thumbnailImage: item.images?.Thumbnail?.url!,
      url: item.duration == null ? item.images?.BigPicture?.url : item.url,
      video: !!item.duration,
      preview:
        !!charge?.resources &&
        !charge.buy &&
        charge.resources.includes(item.id!),
    };
  });

  const handlePreview = (index: number) => {
    navigateRoute(
      "/resource-preview",
      {},
      {
        state: {
          data: previewList,
          defaultIndex: index,
        },
      }
    );
  };

  return (
    <div
      className="grid"
      style={{
        gridTemplateColumns: `repeat(${col}, minmax(0, 1fr))`,
        gap: `${gap / 16}rem`,
      }}
    >
      {resources.map((item, index) => (
        <ResourceItem
          key={item.id}
          resource={item}
          ratio={ratio}
          charge={charge}
          onPreview={() => handlePreview(index)}
        />
      ))}
    </div>
  );
};
