import { ReactNode, useEffect, useMemo, useRef, useState } from "react";
import classNames from "classnames";

interface Props {
  content: ReactNode;
  /** max 6 */
  rows?: number;
  className?: string;
  expandText?: ReactNode;
  collapseText?: ReactNode;
  defaultExpanded?: boolean;
}

export const Ellipsis = (props: Props) => {
  const {
    content,
    rows,
    className,
    expandText = "展开",
    collapseText = "收起",
  } = props;

  const [exceeded, setExceeded] = useState(false);
  // 这里不能设置默认值哈，默认展开的话无法得知是否已经撑满
  const [expanded, setExpanded] = useState(false);

  const contentRef = useRef<HTMLDivElement>(null);

  let lineClamp = "line-clamp-none";
  if (rows) {
    lineClamp = `line-clamp-${expanded ? "none" : Math.min(rows, 6)}`;
  }

  const renderBtn = useMemo(() => {
    if (expanded) {
      // 当前为展开
      return (
        <a
          className="inline-flex"
          onClick={(e) => {
            e.stopPropagation();
            setExpanded(false);
          }}
        >
          {collapseText}
        </a>
      );
    }
    // 当前为收起
    return (
      <a
        className="inline-flex"
        onClick={(e) => {
          e.stopPropagation();
          setExpanded(true);
        }}
      >
        {expandText}
      </a>
    );
  }, [expanded, expandText, collapseText]);

  useEffect(() => {
    if (!contentRef.current) return;
    if (contentRef.current.scrollHeight > contentRef.current.offsetHeight) {
      setExceeded(true);
      return;
    }

    setExceeded(false);
  }, []);

  return (
    <div className={className}>
      <div
        ref={contentRef}
        className={classNames("w-full whitespace-pre-wrap", lineClamp)}
      >
        {content}
      </div>
      {exceeded ? renderBtn : null}
    </div>
  );
};
