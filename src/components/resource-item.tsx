import { Image } from "antd-mobile";
import { Resource } from "@/type/resource";
import { PlayIcon } from "./svg-icon";
import { formatDuration } from "@/utils/format-duration";
import { CommonIcon } from "./common-icon";
import { Charge } from "@/type/dynamic-result";
import { AspectRatio } from "@/ui/aspect-ratio";
import { useConfigList } from "@/hooks/use-config-list";
import { DefaultLoadImage } from "./default-load-image";
import { MouseEvent } from "react";

interface Props {
  ratio: number;
  resource: Resource;
  charge?: Charge;
  onPreview?: () => void;
}

export const ResourceItem = ({ ratio, resource, charge, onPreview }: Props) => {
  const isCharge = charge?.resources?.includes(resource.id!);

  const { getSystemConfig } = useConfigList();

  const handleClick = (e: MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    onPreview?.();
  };

  // 不是收费 || 已购买 || 是视频
  const blur =
    !isCharge || charge?.buy || resource.duration
      ? 0
      : Number(getSystemConfig("DYNAMIC_CHARGE_RESOURCE_BLUR"));

  return (
    <div
      className="relative overflow-hidden rounded-[10px]"
      onClick={handleClick}
    >
      <AspectRatio ratio={ratio}>
        <Image
          src={resource.images?.Thumbnail?.url ?? ""}
          width="100%"
          height="100%"
          fit="cover"
          className="rounded-[10px]"
          style={{
            filter: `blur(${blur}px)`,
          }}
          lazy
          placeholder={<DefaultLoadImage />}
          fallback={<DefaultLoadImage />}
          onLoad={() => {}}
        />
      </AspectRatio>
      {resource.duration ? (
        <div className="flex absolute top-0 left-0 w-full h-full justify-center items-center">
          <PlayIcon size={30} className="text-white/50 rounded-full" />
          <div className="text-white absolute bottom-[3px] right-[5px]">
            {formatDuration(resource.duration)}
          </div>
        </div>
      ) : null}
      {isCharge ? (
        <div>
          <CommonIcon
            name="charge-sign"
            w={73}
            h={20}
            className="text-white absolute top-[5px] left-[5px]"
          />
        </div>
      ) : null}
    </div>
  );
};
