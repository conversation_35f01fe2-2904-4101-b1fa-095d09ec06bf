/**
 * 注意用于将前端路由与App端路由映射
 */

const routeMap: Record<string, string> = {
  // 底部导航
  "/VideoHomePage": "/video",
  "/CommunityHomePage": "/community",
  "/MessageHomePage": "/message",
  "/MyHomePage": "/my",
  "/LiveHomePage": "/live",
  // 社区
  "/DynamicPage": "/dynamic",
  "/MindWallPage": "/mind-wall",
  "/ChatRoomPage": "/chat-room",
  "/UserPlazaPage": "/user-plaza",
  "/TradePage": "/trade",
  "/DynamicDetailedPage": "/dynamic-detail",
  // 消息
  "/ImPage": "/im",
  "/GroupPage": "/group",

  // 我的
  "/CdkPage": "/cdk",
  "/SelfUnbanPage": "/self-unban",
  "/WalletPage": "/wallet",
  "/VipPage": "/vip",
  "/UserInvitePosterPage": "/user-invite-poster",
  "/UserAgentPage": "/user-agent",
  "/UserCopartnerPage": "/user-copartner",
  "/SignInPage": "/sign-in",
  "/VisitorPage": "/visitor",
  "/TaskPage": "/task",
  "/UserOrderPage": "/user-order",
  "/MerchantOrderPage": "/merchant-order",
  "/UserOrderRefundPage": "/user-order-refund",
  "/MedalPage": "/medal",
  "/FaceCertifiedPage": "/face-certified",
  "/IdcardAuthPage": "/idcard-auth",
  "/UserPublishPage": "/user-publish",
  "/UserLevelHomePage": "/user-level",
  "/UserPurchasedPage": "/user-purchased",
  "/CollectionPage": "/collection",
  "/FeedbackPage": "/feedback",
  "/CamouflageSettingPage": "/camouflage-setting",
  "/MountsListPage": "/mounts-list",
  "/JoinSpecialListPage": "/join-special-list",
  "/IdentifyCardPage": "/identify-card",
  "/SettingPage": "/setting",
  "/UserHomePage": "/user-home",
  "/CommonSettingPage": "/common-setting",
  "/BeautySettingPage": "/beauty-setting",
  "/SecuritySettingPage": "/security-setting",
  "/UpdatePasswordPage": "/update-password",
  "/PrivacySettingPage": "/privacy-setting",
  "/MessageSettingPage": "/message-setting",
  "/BlacklistPage": "/blacklist",
  "/AboutPage": "/about",
  "/SignaturePage": "/signature",
  "/WithdrawalPage": "/withdrawal",
  "/BankListPage": "/bank-list",
  "/BindBankCardPage": "/bind-bank-card",
  "/ExchangePage": "/exchange",
  "/RechargePage": "/recharge",
  "/DynamicGiftGiverPage": "/dynamic-gift-giver",
  "/DynamicLikeListPage": "/dynamic-like-list",
  "/VideoDetailedPage": "/video-detail",
  "/VideoGiftGiverPage": "/video-gift-giver",
  "/DiscountActivityDetailedPage": "/discount-activity-detailed",
  "/CreateOrderPage": "/create-order",
  "/UserOrderDetailedPage": "/user-order-detail",
  "/UserTransactionDetailedPage": "/user-transaction-detailed",
  "/UserTransactionParticipatePage": "/user-transaction-participate",
  "/TradeCreatePage": "/trade-create",
  "/VideoUploaderApplyPage": "/video-uploader-apply",
  "/UserLevelRankingPage": "/user-level-ranking",
  "/ChatPage": "/chat",
  "/ShortVideoDetailedPage": "/short-video-detail",
  "/UserProfilePage": "/user-profile",
  "/UserMindWallPage": "/user-mind-wall",
  "/UserAlbumPage": "/user-album",
  "/ReportPage": "/report",
  "/DynamicPublishPage": "/dynamic-publish",
  "/UserOrderRefundDetailedPage": "/user-order-refund-detail",
  "/UserChatPermissionDetailedPage": "/user-chat-permission-detail",
  "/LivePlayerPage": "/live-player",

  // todo: 待定
  "/TransmissionPage": "/transmission",
  // todo:web 实现可能存在问题
  "/PermissionsSettingPage": "/permissions-setting",
  // todo: 待定
  "/ConversationSelectPage": "/conversation-select",
};

/**
 * 路由映射
 * 用于将前端路由与App端路由映射
 * 支持带参数的路由，只匹配路径的开头部分
 */
export const getRoute = (path: string) => {
  if (!path) {
    return "";
  }

  // 直接匹配完整路径
  if (routeMap[path]) {
    return routeMap[path];
  }

  // 查找匹配的路由前缀
  const matchedRoute = Object.keys(routeMap).find((route) =>
    path.startsWith(route)
  );

  if (matchedRoute) {
    return path.replace(matchedRoute, routeMap[matchedRoute]);
  }

  return path;
};
