/* eslint-disable react-refresh/only-export-components */
import { Navigate, RouteObject, createBrowserRouter } from "react-router-dom";
import GlobalLayout from "@/layouts/global";
import { lazy } from "react";
import LoginLayout from "@/layouts/login";
import HomeLayout from "@/layouts/home";
import CommunityLayout from "@/layouts/community";
import HomeBackgroundLayout from "@/layouts/home-background";
import { loader as dynamicDetailLoader } from "@/pages/dynamic-detail/loader";
import { loader as homeLoader } from "@/pages/home/<USER>";
import { loader as faqLoader } from "@/pages/faq/loader";
import { loader as webPayLoader } from "@/pages/web-pay/loader";
import { loader as TopicLoader } from "@/pages/topic/loader";
import { loader as TopicDetailLoader } from "@/pages/topic-detail/loader";
import { loader as walletLoader } from "@/pages/wallet/loader";
import { loader as selfUnbanLoader } from "@/pages/self-unban/loader";
import { loader as vipLoader } from "@/pages/vip/loader";
import { loader as userAgentLoader } from "@/pages/user-agent/loader";
import { loader as userInvitePosterLoader } from "@/pages/user-invite-poster/loader";
import { loader as userOrderLoader } from "@/pages/user-order/loader";
import { loader as merchantOrderLoader } from "@/pages/merchant-order/loader";
import { loader as userOrderRefundLoader } from "@/pages/user-order-refund/loader";
import { loader as idcardAuthLoader } from "@/pages/idcard-auth/loader";
import { loader as userPublishLoader } from "@/pages/user-publish/loader";
import { loader as userLevelLoader } from "@/pages/user-level/loader";
import { loader as userPurchasedLoader } from "@/pages/user-purchased/loader";
import { loader as identifyCardLoader } from "@/pages/identify-card/loader";
import { loader as settingLoader } from "@/pages/setting/loader";
import { loader as userHomeLoader } from "@/pages/user-home/loader";
import { loader as securitySettingLoader } from "@/pages/security-setting/loader";
import { loader as privacySettingLoader } from "@/pages/privacy-setting/loader";
import { loader as messageSettingLoader } from "@/pages/message-setting/loader";
import { loader as signatureLoader } from "@/pages/signature/loader";
import { loader as exchangeLoader } from "@/pages/exchange/loader";
import { loader as rechargeLoader } from "@/pages/recharge/loader";
import { loader as videoDetailLoader } from "@/pages/video-detail/loader";
import { loader as userSearchDetailedLoader } from "@/pages/user-search-detailed/loader";
import { loader as userLevelRankingLoader } from "@/pages/user-level-ranking/loader";
import { loader as chatLoader } from "@/pages/chat/loader";
import { loader as redEnvelopeDetailLoader } from "@/pages/red-envelope-detail/loader";
import { loader as redEnvelopePublishLoader } from "@/pages/red-envelope-publish/loader";
import { loader as userChatPermissionDetailLoader } from "@/pages/user-chat-permission-detail/loader";
import { loader as discountActivityDetailedLoader } from "@/pages/discount-activity-detailed/loader";
import { loader as playbackVideoDetailLoader } from "@/pages/playback-video-detail/loader";

import ErrorPage from "@/pages/error-page";
import { RouterWrapper } from "./router-wrapper";
import MessageLayout from "@/layouts/message";
import LoggedLayout from "@/layouts/logged";
import KeepAlive from "react-activation";

// 动态详情
const DynamicDetail = lazy(() => import("@/pages/dynamic-detail"));

// 话题
const Topic = lazy(() => import("@/pages/topic"));

// 话题详情
const TopicDetail = lazy(() => import("@/pages/topic-detail"));

// 登录
const Login = lazy(() => import("@/pages/login"));
const LoginBasicInfo = lazy(() => import("@/pages/login/pages/basic-info"));
const LoginCode = lazy(() => import("@/pages/login/pages/code"));
const LoginPwd = lazy(() => import("@/pages/login/pages/pwd"));
const LoginGenderInfo = lazy(() => import("@/pages/login/pages/gender-info"));
const LoginTagInfo = lazy(() => import("@/pages/login/pages/tag-info"));
const LoginBindPhone = lazy(() => import("@/pages/login/pages/bind-phone"));

// home
const Home = lazy(() => import("@/pages/home"));
// community
const Community = lazy(() => import("@/pages/home/<USER>"));
const ChatRoom = lazy(() => import("@/pages/home/<USER>/chat-room"));
const Dynamic = lazy(() => import("@/pages/home/<USER>/dynamic"));
const MindWall = lazy(() => import("@/pages/home/<USER>/mind-wall"));
const Trade = lazy(() => import("@/pages/home/<USER>/trade"));
const UserPlaza = lazy(() => import("@/pages/home/<USER>/user-plaza"));

// 常见问题
const FaqPage = lazy(() => import("@/pages/faq"));

// 支付
const PayPage = lazy(() => import("@/pages/pay"));
const PaySuccess = lazy(() => import("@/pages/pay-success"));
const WebPayPage = lazy(() => import("@/pages/web-pay"));

// 违规列表
const ViolationList = lazy(() => import("@/pages/violation-list"));

// 直播
const Live = lazy(() => import("@/pages/home/<USER>"));

// 消息
const Message = lazy(() => import("@/pages/home/<USER>"));
const Im = lazy(() => import("@/pages/home/<USER>/im"));
const Group = lazy(() => import("@/pages/home/<USER>/group"));

// 我的
const MyPage = lazy(() => import("@/pages/home/<USER>"));

// 视频
const Video = lazy(() => import("@/pages/home/<USER>"));

// 粉丝
const FansPage = lazy(() => import("@/pages/fans"));
// 关注
const FollowPage = lazy(() => import("@/pages/follow"));

// 钱包
const WalletPage = lazy(() => import("@/pages/wallet"));

// cdk
const CDKPage = lazy(() => import("@/pages/cdk"));

// 自助解封
const SelfUnbanPage = lazy(() => import("@/pages/self-unban"));

// vip
const VipPage = lazy(() => import("@/pages/vip"));

// 用户代理
const UserAgent = lazy(() => import("@/pages/user-agent"));

// 用户邀请海报
const UserInvitePoster = lazy(() => import("@/pages/user-invite-poster"));

// 用户合伙人
const UserCopartner = lazy(() => import("@/pages/user-copartner"));

// 签到
const SignIn = lazy(() => import("@/pages/sign-in"));

// 访客记录
const Visitor = lazy(() => import("@/pages/visitor"));

// 任务
const Task = lazy(() => import("@/pages/task"));

// 用户订单
const UserOrder = lazy(() => import("@/pages/user-order"));

// 商户订单
const MerchantOrder = lazy(() => import("@/pages/merchant-order"));

// 用户订单退款
const UserOrderRefund = lazy(() => import("@/pages/user-order-refund"));

// 勋章
const Medal = lazy(() => import("@/pages/medal"));

// 真人认证
const FaceCertified = lazy(() => import("@/pages/face-certified"));

// 身份证认证
const IdcardAuth = lazy(() => import("@/pages/idcard-auth"));

// 用户发布
const UserPublish = lazy(() => import("@/pages/user-publish"));

// 用户等级
const UserLevel = lazy(() => import("@/pages/user-level"));

// 用户等级汇总
const UserLevelSummary = lazy(() => import("@/pages/user-level-summary"));

// 用户等级明细
const UserLevelDetailed = lazy(() => import("@/pages/user-level-detailed"));

// 用户购买
const UserPurchased = lazy(() => import("@/pages/user-purchased"));

// 收藏
const Collection = lazy(() => import("@/pages/collection"));

// 意见反馈
const Feedback = lazy(() => import("@/pages/feedback"));

// 伪装设置
const CamouflageSetting = lazy(() => import("@/pages/camouflage-setting"));

// 我的坐骑
const MountsList = lazy(() => import("@/pages/mounts-list"));

// 我的入场名片
const JoinSpecialList = lazy(() => import("@/pages/join-special-list"));

// 身份卡
const IdentifyCard = lazy(() => import("@/pages/identify-card"));

// 设置
const Setting = lazy(() => import("@/pages/setting"));

// 用户主页
const UserHome = lazy(() => import("@/pages/user-home"));

// 通用设置
const CommonSetting = lazy(() => import("@/pages/common-setting"));

// 美颜设置
const BeautySetting = lazy(() => import("@/pages/beauty-setting"));

// 账号与安全
const SecuritySetting = lazy(() => import("@/pages/security-setting"));

// 修改密码
const UpdatePassword = lazy(() => import("@/pages/update-password"));

// 隐私设置
const PrivacySetting = lazy(() => import("@/pages/privacy-setting"));

// 消息设置
const MessageSetting = lazy(() => import("@/pages/message-setting"));

// 黑名单
const Blacklist = lazy(() => import("@/pages/blacklist"));

// 关于我们
const About = lazy(() => import("@/pages/about"));

// 个性签名
const Signature = lazy(() => import("@/pages/signature"));

// 提现
const Withdrawal = lazy(() => import("@/pages/withdrawal"));

// 银行卡列表
const BankList = lazy(() => import("@/pages/bank-list"));

// 绑定银行卡
const BindBankCard = lazy(() => import("@/pages/bind-bank-card"));

// 兑换
const Exchange = lazy(() => import("@/pages/exchange"));

// 充值
const Recharge = lazy(() => import("@/pages/recharge"));

// 动态点赞列表
const DynamicLikeList = lazy(() => import("@/pages/dynamic-like-list"));

// 动态赠礼人列表
const DynamicGiftGiver = lazy(() => import("@/pages/dynamic-gift-giver"));

// 动态评论详情
const DynamicCommentDetailed = lazy(
  () => import("@/pages/dynamic-comment-detailed")
);

// 视频详情
const VideoDetail = lazy(() => import("@/pages/video-detail"));

// 视频赠礼人列表
const VideoGiftGiver = lazy(() => import("@/pages/video-gift-giver"));

// 视频评论详情
const VideoCommentDetailed = lazy(
  () => import("@/pages/video-comment-detailed")
);

// 用户搜索
const UserSearchHome = lazy(() => import("@/pages/user-search-home"));

// 用户搜索详情
const UserSearchDetailed = lazy(() => import("@/pages/user-search-detailed"));

// 用户等级排行
const UserLevelRanking = lazy(() => import("@/pages/user-level-ranking"));

// 短视频tag
const ShortVideoTag = lazy(() => import("@/pages/short-video-tag"));

// 短视频列表
const ShortVideoList = lazy(() => import("@/pages/short-video-list"));

// 短视频评论详情
const ShortVideoCommentDetailed = lazy(
  () => import("@/pages/short-video-comment-detailed")
);

// 聊天
const Chat = lazy(() => import("@/pages/chat"));

// 红包详情
const RedEnvelopeDetail = lazy(() => import("@/pages/red-envelope-detail"));

// 发红包
const RedEnvelopePublish = lazy(() => import("@/pages/red-envelope-publish"));

// 用户聊天权限创建
const UserChatPermissionCreate = lazy(
  () => import("@/pages/user-chat-permission-create")
);

// 用户聊天权限详情
const UserChatPermissionDetail = lazy(
  () => import("@/pages/user-chat-permission-detail")
);

// 资源预览
const ResourcePreview = lazy(() => import("@/pages/resource-preview"));

// 折扣活动详情
const DiscountActivityDetailed = lazy(
  () => import("@/pages/discount-activity-detailed")
);

// 回放视频详情
const PlaybackVideoDetail = lazy(() => import("@/pages/playback-video-detail"));

// 回放视频评论详情
const PlaybackVideoCommentDetailed = lazy(
  () => import("@/pages/playback-video-comment-detailed")
);

// 心意详情
const MindWallDetailed = lazy(() => import("@/pages/mind-wall-detailed"));

// 心意墙评论详情
const MindWallCommentDetailed = lazy(
  () => import("@/pages/mind-wall-comment-detailed")
);

// 保证金
const UserMargin = lazy(() => import("@/pages/user-margin"));

// 交易详情
const UserTransactionDetailed = lazy(
  () => import("@/pages/user-transaction-detailed")
);

// 创建订单
const CreateOrder = lazy(() => import("@/pages/create-order"));

// 地址管理
const UserAddress = lazy(() => import("@/pages/user-address"));

// 地址编辑
const UserAddressEdit = lazy(() => import("@/pages/user-address-edit"));

// 订单详情
const UserOrderDetailed = lazy(() => import("@/pages/user-order-detail"));

// 交易投标
const UserTransactionParticipate = lazy(
  () => import("@/pages/user-transaction-participate")
);

// 交易创建
const TradeCreate = lazy(() => import("@/pages/trade-create"));

// 短视频发布
const ShortVideoPublish = lazy(() => import("@/pages/short-video-publish"));

// 视频发布
const VideoPublish = lazy(() => import("@/pages/video-publish"));

// UP主申请
const VideoUploaderApply = lazy(() => import("@/pages/video-uploader-apply"));

// 短视频详情
const ShortVideoDetail = lazy(() => import("@/pages/short-video-detail"));

// 用户资料
const UserProfile = lazy(() => import("@/pages/user-profile"));

// 用户心意墙
const UserMindWall = lazy(() => import("@/pages/home/<USER>"));

// 用户颜照库
const UserAlbum = lazy(() => import("@/pages/user-album"));

// 举报
const Report = lazy(() => import("@/pages/report"));

// 动态发布
const DynamicPublish = lazy(() => import("@/pages/dynamic-publish"));

// 用户订单售后
const UserOrderRefundDetail = lazy(
  () => import("@/pages/user-order-refund-detail")
);

// 短视频发布列表
const ShortVideoPublishList = lazy(
  () => import("@/pages/short-video-publish-list")
);

// 直播播放
const LivePlayerPage = lazy(() => import("@/pages/live-player"));

const LiveListPage = lazy(() => import("@/pages/live-list"));

// 收费设置
const ChargeSetting = lazy(() => import("@/pages/charge-setting"));

// 回放修改
const PlaybackModify = lazy(() => import("@/pages/playback-modify"));

// 补签记录
const RePunchInRecord = lazy(() => import("@/pages/re-punch-in-record"));

// 会话选择
const ConversationSelect = lazy(() => import("@/pages/conversation-select"));

const routes: RouteObject[] = [
  {
    path: "/",
    element: <GlobalLayout />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        // 重定向至 home
        element: <Navigate to="/home" replace />,
      },
      {
        path: "",
        element: <LoggedLayout />,
        children: [
          {
            path: "dynamic-detail",
            loader: dynamicDetailLoader,
            element: (
              <RouterWrapper>
                <DynamicDetail />
              </RouterWrapper>
            ),
          },
          {
            path: "topic",
            loader: TopicLoader,
            element: (
              <RouterWrapper>
                <Topic />
              </RouterWrapper>
            ),
          },
          {
            path: "topic-detail",
            loader: TopicDetailLoader,
            element: (
              <RouterWrapper>
                <TopicDetail />
              </RouterWrapper>
            ),
          },
          {
            path: "",
            loader: homeLoader,
            element: (
              <RouterWrapper>
                <HomeLayout />
              </RouterWrapper>
            ),
            children: [
              {
                path: "home",
                element: (
                  <RouterWrapper>
                    <Home />
                  </RouterWrapper>
                ),
              },
              {
                path: "",
                element: <HomeBackgroundLayout />,
                children: [
                  {
                    path: "",
                    element: (
                      <RouterWrapper>
                        <CommunityLayout />
                      </RouterWrapper>
                    ),
                    children: [
                      {
                        path: "community",
                        element: (
                          // <KeepAlive name="community">
                          <RouterWrapper>
                            <Community />
                          </RouterWrapper>
                          // </KeepAlive>
                        ),
                      },
                      {
                        path: "dynamic",
                        element: (
                          <KeepAlive name="dynamic">
                            <RouterWrapper>
                              <Dynamic />
                            </RouterWrapper>
                          </KeepAlive>
                        ),
                      },
                      {
                        path: "mind-wall",
                        element: (
                          <KeepAlive name="mind-wall">
                            <RouterWrapper>
                              <MindWall />
                            </RouterWrapper>
                          </KeepAlive>
                        ),
                      },
                      {
                        path: "chat-room",
                        element: (
                          <KeepAlive name="chat-room">
                            <RouterWrapper>
                              <ChatRoom />
                            </RouterWrapper>
                          </KeepAlive>
                        ),
                      },
                      {
                        path: "user-plaza",
                        element: (
                          <KeepAlive name="user-plaza">
                            <RouterWrapper>
                              <UserPlaza />
                            </RouterWrapper>
                          </KeepAlive>
                        ),
                      },
                      {
                        path: "trade",
                        element: (
                          <KeepAlive name="trade">
                            <RouterWrapper>
                              <Trade />
                            </RouterWrapper>
                          </KeepAlive>
                        ),
                      },
                    ],
                  },
                ],
              },
              {
                path: "live",
                element: <HomeBackgroundLayout />,
                children: [
                  {
                    path: "",
                    element: (
                      <KeepAlive name="live">
                        <RouterWrapper>
                          <Live />
                        </RouterWrapper>
                      </KeepAlive>
                    ),
                  },
                ],
              },
              {
                path: "",
                element: <HomeBackgroundLayout />,
                children: [
                  {
                    path: "",
                    element: (
                      <RouterWrapper>
                        <MessageLayout />
                      </RouterWrapper>
                    ),
                    children: [
                      {
                        path: "message",
                        element: (
                          <RouterWrapper>
                            <Message />
                          </RouterWrapper>
                        ),
                      },
                      {
                        path: "im",
                        element: (
                          // <KeepAlive name="im">
                          <RouterWrapper>
                            <Im />
                          </RouterWrapper>
                          // </KeepAlive>
                        ),
                      },
                      {
                        path: "group",
                        element: (
                          <KeepAlive name="group">
                            <RouterWrapper>
                              <Group />
                            </RouterWrapper>
                          </KeepAlive>
                        ),
                      },
                    ],
                  },
                ],
              },
              {
                path: "my",
                element: (
                  <KeepAlive name="my">
                    <RouterWrapper>
                      <MyPage />
                    </RouterWrapper>
                  </KeepAlive>
                ),
              },
              {
                path: "video",
                element: <HomeBackgroundLayout />,
                children: [
                  {
                    path: "",
                    element: (
                      <KeepAlive name="video">
                        <RouterWrapper>
                          <Video />
                        </RouterWrapper>
                      </KeepAlive>
                    ),
                  },
                ],
              },
            ],
          },
          {
            path: "violation-list",
            element: (
              <RouterWrapper>
                <ViolationList />
              </RouterWrapper>
            ),
          },
          {
            path: "fans",
            element: (
              <RouterWrapper>
                <FansPage />
              </RouterWrapper>
            ),
          },
          {
            path: "follow",
            element: (
              <RouterWrapper>
                <FollowPage />
              </RouterWrapper>
            ),
          },
          {
            path: "wallet",
            loader: walletLoader,
            element: (
              <RouterWrapper>
                <WalletPage />
              </RouterWrapper>
            ),
          },
          {
            path: "cdk",
            element: (
              <RouterWrapper>
                <CDKPage />
              </RouterWrapper>
            ),
          },
          {
            path: "self-unban",
            loader: selfUnbanLoader,
            element: (
              <RouterWrapper>
                <SelfUnbanPage />
              </RouterWrapper>
            ),
          },
          {
            path: "vip",
            loader: vipLoader,
            element: (
              <RouterWrapper>
                <VipPage />
              </RouterWrapper>
            ),
          },
          {
            path: "user-agent",
            loader: userAgentLoader,
            element: (
              <RouterWrapper>
                <UserAgent />
              </RouterWrapper>
            ),
          },
          {
            path: "user-invite-poster",
            loader: userInvitePosterLoader,
            element: (
              <RouterWrapper>
                <UserInvitePoster />
              </RouterWrapper>
            ),
          },
          {
            path: "user-copartner",
            element: (
              <RouterWrapper>
                <UserCopartner />
              </RouterWrapper>
            ),
          },
          {
            path: "sign-in",
            element: (
              <RouterWrapper>
                <SignIn />
              </RouterWrapper>
            ),
          },
          {
            path: "visitor",
            element: (
              <RouterWrapper>
                <Visitor />
              </RouterWrapper>
            ),
          },
          {
            path: "task",
            element: (
              <RouterWrapper>
                <Task />
              </RouterWrapper>
            ),
          },
          {
            path: "user-order",
            loader: userOrderLoader,
            element: (
              <RouterWrapper>
                <UserOrder />
              </RouterWrapper>
            ),
          },
          {
            path: "merchant-order",
            loader: merchantOrderLoader,
            element: (
              <RouterWrapper>
                <MerchantOrder />
              </RouterWrapper>
            ),
          },
          {
            path: "user-order-refund",
            loader: userOrderRefundLoader,
            element: (
              <RouterWrapper>
                <UserOrderRefund />
              </RouterWrapper>
            ),
          },
          {
            path: "medal",
            element: (
              <RouterWrapper>
                <Medal />
              </RouterWrapper>
            ),
          },
          {
            path: "face-certified",
            element: (
              <RouterWrapper>
                <FaceCertified />
              </RouterWrapper>
            ),
          },
          {
            path: "idcard-auth",
            loader: idcardAuthLoader,
            element: (
              <RouterWrapper>
                <IdcardAuth />
              </RouterWrapper>
            ),
          },
          {
            path: "",
            element: <HomeBackgroundLayout />,
            children: [
              {
                path: "user-publish",
                loader: userPublishLoader,
                element: (
                  <RouterWrapper>
                    <UserPublish />
                  </RouterWrapper>
                ),
              },
            ],
          },
          {
            path: "user-level",
            loader: userLevelLoader,
            element: (
              <RouterWrapper>
                <UserLevel />
              </RouterWrapper>
            ),
          },
          {
            path: "user-level-summary",
            element: (
              <RouterWrapper>
                <UserLevelSummary />
              </RouterWrapper>
            ),
          },
          {
            path: "user-level-detailed",
            element: (
              <RouterWrapper>
                <UserLevelDetailed />
              </RouterWrapper>
            ),
          },
          {
            path: "user-purchased",
            loader: userPurchasedLoader,
            element: (
              <RouterWrapper>
                <UserPurchased />
              </RouterWrapper>
            ),
          },
          {
            path: "collection",
            element: (
              <RouterWrapper>
                <Collection />
              </RouterWrapper>
            ),
          },
          {
            path: "feedback",
            element: (
              <RouterWrapper>
                <Feedback />
              </RouterWrapper>
            ),
          },
          {
            path: "camouflage-setting",
            element: (
              <RouterWrapper>
                <CamouflageSetting />
              </RouterWrapper>
            ),
          },
          {
            path: "mounts-list",
            element: (
              <RouterWrapper>
                <MountsList />
              </RouterWrapper>
            ),
          },
          {
            path: "join-special-list",
            element: (
              <RouterWrapper>
                <JoinSpecialList />
              </RouterWrapper>
            ),
          },
          {
            path: "identify-card",
            loader: identifyCardLoader,
            element: (
              <RouterWrapper>
                <IdentifyCard />
              </RouterWrapper>
            ),
          },
          {
            path: "setting",
            loader: settingLoader,
            element: (
              <RouterWrapper>
                <Setting />
              </RouterWrapper>
            ),
          },
          {
            path: "user-home",
            loader: userHomeLoader,
            element: (
              <RouterWrapper>
                <UserHome />
              </RouterWrapper>
            ),
          },
          {
            path: "common-setting",
            element: (
              <RouterWrapper>
                <CommonSetting />
              </RouterWrapper>
            ),
          },
          {
            path: "beauty-setting",
            element: (
              <RouterWrapper>
                <BeautySetting />
              </RouterWrapper>
            ),
          },
          {
            path: "security-setting",
            loader: securitySettingLoader,
            element: (
              <RouterWrapper>
                <SecuritySetting />
              </RouterWrapper>
            ),
          },
          {
            path: "update-password",
            element: (
              <RouterWrapper>
                <UpdatePassword />
              </RouterWrapper>
            ),
          },
          {
            path: "privacy-setting",
            loader: privacySettingLoader,
            element: (
              <RouterWrapper>
                <PrivacySetting />
              </RouterWrapper>
            ),
          },
          {
            path: "message-setting",
            loader: messageSettingLoader,
            element: (
              <RouterWrapper>
                <MessageSetting />
              </RouterWrapper>
            ),
          },
          {
            path: "blacklist",
            element: (
              <RouterWrapper>
                <Blacklist />
              </RouterWrapper>
            ),
          },
          {
            path: "about",
            element: (
              <RouterWrapper>
                <About />
              </RouterWrapper>
            ),
          },
          {
            path: "signature",
            loader: signatureLoader,
            element: (
              <RouterWrapper>
                <Signature />
              </RouterWrapper>
            ),
          },
          {
            path: "withdrawal",
            element: (
              <RouterWrapper>
                <Withdrawal />
              </RouterWrapper>
            ),
          },
          {
            path: "bank-list",
            element: (
              <RouterWrapper>
                <BankList />
              </RouterWrapper>
            ),
          },
          {
            path: "bind-bank-card",
            element: (
              <RouterWrapper>
                <BindBankCard />
              </RouterWrapper>
            ),
          },
          {
            path: "exchange",
            loader: exchangeLoader,
            element: (
              <RouterWrapper>
                <Exchange />
              </RouterWrapper>
            ),
          },
          {
            path: "recharge",
            loader: rechargeLoader,
            element: (
              <RouterWrapper>
                <Recharge />
              </RouterWrapper>
            ),
          },
          {
            path: "dynamic-like-list",
            element: (
              <RouterWrapper>
                <DynamicLikeList />
              </RouterWrapper>
            ),
          },
          {
            path: "dynamic-gift-giver",
            element: (
              <RouterWrapper>
                <DynamicGiftGiver />
              </RouterWrapper>
            ),
          },
          {
            path: "dynamic-comment-detailed",
            element: (
              <RouterWrapper>
                <DynamicCommentDetailed />
              </RouterWrapper>
            ),
          },
          {
            path: "video-detail",
            loader: videoDetailLoader,
            element: (
              <RouterWrapper>
                <VideoDetail />
              </RouterWrapper>
            ),
          },
          {
            path: "video-gift-giver",
            element: (
              <RouterWrapper>
                <VideoGiftGiver />
              </RouterWrapper>
            ),
          },
          {
            path: "video-comment-detailed",
            element: (
              <RouterWrapper>
                <VideoCommentDetailed />
              </RouterWrapper>
            ),
          },
          {
            path: "user-search-home",
            element: (
              <RouterWrapper>
                <UserSearchHome />
              </RouterWrapper>
            ),
          },
          {
            path: "user-search-detailed",
            loader: userSearchDetailedLoader,
            element: (
              <RouterWrapper>
                <UserSearchDetailed />
              </RouterWrapper>
            ),
          },
          {
            path: "user-level-ranking",
            loader: userLevelRankingLoader,
            element: (
              <RouterWrapper>
                <UserLevelRanking />
              </RouterWrapper>
            ),
          },
          {
            path: "short-video-tag",
            element: (
              <RouterWrapper>
                <ShortVideoTag />
              </RouterWrapper>
            ),
          },
          {
            path: "short-video-list",
            element: (
              <RouterWrapper>
                <ShortVideoList />
              </RouterWrapper>
            ),
          },
          {
            path: "short-video-comment-detailed",
            element: (
              <RouterWrapper>
                <ShortVideoCommentDetailed />
              </RouterWrapper>
            ),
          },
          {
            path: "chat",
            loader: chatLoader,
            element: (
              <RouterWrapper>
                <Chat />
              </RouterWrapper>
            ),
          },
          {
            path: "red-envelope-detail",
            loader: redEnvelopeDetailLoader,
            element: (
              <RouterWrapper>
                <RedEnvelopeDetail />
              </RouterWrapper>
            ),
          },
          {
            path: "red-envelope-publish",
            loader: redEnvelopePublishLoader,
            element: (
              <RouterWrapper>
                <RedEnvelopePublish />
              </RouterWrapper>
            ),
          },
          {
            path: "user-chat-permission-create",
            element: (
              <RouterWrapper>
                <UserChatPermissionCreate />
              </RouterWrapper>
            ),
          },
          {
            path: "user-chat-permission-detail",
            loader: userChatPermissionDetailLoader,
            element: (
              <RouterWrapper>
                <UserChatPermissionDetail />
              </RouterWrapper>
            ),
          },
          {
            path: "resource-preview",
            element: (
              <RouterWrapper>
                <ResourcePreview />
              </RouterWrapper>
            ),
          },
          {
            path: "discount-activity-detailed",
            loader: discountActivityDetailedLoader,
            element: (
              <RouterWrapper>
                <DiscountActivityDetailed />
              </RouterWrapper>
            ),
          },
          {
            path: "playback-video-detail",
            loader: playbackVideoDetailLoader,
            element: (
              <RouterWrapper>
                <PlaybackVideoDetail />
              </RouterWrapper>
            ),
          },
          {
            path: "playback-video-comment-detailed",
            element: (
              <RouterWrapper>
                <PlaybackVideoCommentDetailed />
              </RouterWrapper>
            ),
          },
          {
            path: "mind-wall-detailed",
            element: (
              <RouterWrapper>
                <MindWallDetailed />
              </RouterWrapper>
            ),
          },
          {
            path: "mind-wall-comment-detailed",
            element: (
              <RouterWrapper>
                <MindWallCommentDetailed />
              </RouterWrapper>
            ),
          },
          {
            path: "user-margin",
            element: (
              <RouterWrapper>
                <UserMargin />
              </RouterWrapper>
            ),
          },
          {
            path: "user-transaction-detailed",
            element: (
              <RouterWrapper>
                <UserTransactionDetailed />
              </RouterWrapper>
            ),
          },
          {
            path: "create-order",
            element: (
              <RouterWrapper>
                <CreateOrder />
              </RouterWrapper>
            ),
          },
          {
            path: "user-address",
            element: (
              <RouterWrapper>
                <UserAddress />
              </RouterWrapper>
            ),
          },
          {
            path: "user-address-edit",
            element: (
              <RouterWrapper>
                <UserAddressEdit />
              </RouterWrapper>
            ),
          },
          {
            path: "user-order-detail",
            element: (
              <RouterWrapper>
                <UserOrderDetailed />
              </RouterWrapper>
            ),
          },
          {
            path: "user-transaction-participate",
            element: (
              <RouterWrapper>
                <UserTransactionParticipate />
              </RouterWrapper>
            ),
          },
          {
            path: "trade-create",
            element: (
              <RouterWrapper>
                <TradeCreate />
              </RouterWrapper>
            ),
          },
          {
            path: "short-video-publish",
            element: (
              <RouterWrapper>
                <ShortVideoPublish />
              </RouterWrapper>
            ),
          },
          {
            path: "video-publish",
            element: (
              <RouterWrapper>
                <VideoPublish />
              </RouterWrapper>
            ),
          },
          {
            path: "video-uploader-apply",
            element: (
              <RouterWrapper>
                <VideoUploaderApply />
              </RouterWrapper>
            ),
          },
          {
            path: "short-video-detail",
            element: (
              <RouterWrapper>
                <ShortVideoDetail />
              </RouterWrapper>
            ),
          },
          {
            path: "user-profile",
            element: (
              <RouterWrapper>
                <UserProfile />
              </RouterWrapper>
            ),
          },
          {
            path: "user-mind-wall",
            element: (
              <RouterWrapper>
                <UserMindWall />
              </RouterWrapper>
            ),
          },
          {
            path: "user-album",
            element: (
              <RouterWrapper>
                <UserAlbum />
              </RouterWrapper>
            ),
          },
          {
            path: "report",
            element: (
              <RouterWrapper>
                <Report />
              </RouterWrapper>
            ),
          },
          {
            path: "dynamic-publish",
            element: (
              <RouterWrapper>
                <DynamicPublish />
              </RouterWrapper>
            ),
          },
          {
            path: "user-order-refund-detail",
            element: (
              <RouterWrapper>
                <UserOrderRefundDetail />
              </RouterWrapper>
            ),
          },
          {
            path: "short-video-publish-list",
            element: (
              <RouterWrapper>
                <ShortVideoPublishList />
              </RouterWrapper>
            ),
          },
          {
            path: "live-player",
            element: (
              <RouterWrapper>
                <LivePlayerPage />
              </RouterWrapper>
            ),
          },
          {
            path: "live-list",
            element: (
              <RouterWrapper>
                <LiveListPage />
              </RouterWrapper>
            ),
          },
          {
            path: "charge-setting",
            element: (
              <RouterWrapper>
                <ChargeSetting />
              </RouterWrapper>
            ),
          },
          {
            path: "playback-modify",
            element: (
              <RouterWrapper>
                <PlaybackModify />
              </RouterWrapper>
            ),
          },
          {
            path: "re-punch-in-record",
            element: (
              <RouterWrapper>
                <RePunchInRecord />
              </RouterWrapper>
            ),
          },
          {
            path: "conversation-select",
            element: (
              <RouterWrapper>
                <ConversationSelect />
              </RouterWrapper>
            ),
          },
        ],
      },
      {
        path: "",
        element: <LoginLayout />,
        children: [
          {
            path: "login",
            element: (
              <RouterWrapper>
                <Login />
              </RouterWrapper>
            ),
          },
          {
            path: "login/code",
            element: (
              <RouterWrapper>
                <LoginCode />
              </RouterWrapper>
            ),
          },
          {
            path: "login/pwd",
            element: (
              <RouterWrapper>
                <LoginPwd />
              </RouterWrapper>
            ),
          },
          {
            path: "login/gender-info",
            element: (
              <RouterWrapper>
                <LoginGenderInfo />
              </RouterWrapper>
            ),
          },
          {
            path: "login/tag-info",
            element: (
              <RouterWrapper>
                <LoginTagInfo />
              </RouterWrapper>
            ),
          },
          {
            path: "login/basic-info",
            element: (
              <RouterWrapper>
                <LoginBasicInfo />
              </RouterWrapper>
            ),
          },
          {
            path: "login/bind-phone",
            element: (
              <RouterWrapper>
                <LoginBindPhone />
              </RouterWrapper>
            ),
          },
        ],
      },
      {
        path: "faq",
        loader: faqLoader,
        element: (
          <RouterWrapper>
            <FaqPage />
          </RouterWrapper>
        ),
      },
      {
        path: "pay",
        element: (
          <RouterWrapper>
            <PayPage />
          </RouterWrapper>
        ),
      },
      {
        path: "pay-success",
        element: (
          <RouterWrapper>
            <PaySuccess />
          </RouterWrapper>
        ),
      },
      {
        path: "web-pay",
        loader: webPayLoader,
        element: (
          <RouterWrapper>
            <WebPayPage />
          </RouterWrapper>
        ),
      },
    ],
  },
];

export const router: ReturnType<typeof createBrowserRouter> =
  createBrowserRouter(routes);

export const navigate: ReturnType<typeof createBrowserRouter>["navigate"] =
  router.navigate;
