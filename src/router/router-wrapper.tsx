import { PropsWithChildren, Suspense } from "react";
import { Fallback } from "./fallback";
import { Await, useLoaderData } from "react-router-dom";

export const RouterWrapper = ({ children }: PropsWithChildren) => {
  const data = useLoaderData() as { data: Promise<any> };

  return (
    <Suspense fallback={<Fallback />}>
      <Await resolve={data?.data}>{children}</Await>
    </Suspense>
  );
};
