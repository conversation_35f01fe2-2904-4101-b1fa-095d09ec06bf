import { ScrollLoadData } from "@/components/scroll-load-data";
import { getLayoutPageList } from "@/lib/api/layout";
import { PageParams } from "@/type";
import { BaseUrlConfig } from "@/type/layout-config";
import { useEffect, useState } from "react";
import { PullToRefresh } from "@/components/pull-to-refresh";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { ScrollArea } from "@/ui/scroll-area";
import { UserSearchUserInfo } from "./user-info";
import { UserSearchDynamic } from "./dynamic";
import { UserSearchVideo } from "./video";
import { UserSearchTransaction } from "./user-transaction";
import { UserSearchPlaybackVideo } from "./playback-video";

interface Props {
  isActive: boolean;
  index: number;
  id?: string;
  keyword: string;
  data: BaseUrlConfig & { custom: { widget: string } };
}

export const TabContent = (props: Props) => {
  const { isActive, index, data, keyword } = props;

  const [isInit, setIsInit] = useState(false);

  const newParams = { ...data.params, keyword };

  const queryKey = [data.url, index, newParams];
  const queryFn = (params: PageParams) => {
    return getLayoutPageList<any[]>({
      url: data.url,
      params,
      data: newParams,
    });
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  useEffect(() => {
    if (isActive) {
      setIsInit(true);
    }
  }, [isActive]);

  if (!isInit) {
    return null;
  }

  return (
    <ScrollArea className={isActive ? "h-full" : "hidden"}>
      <PullToRefresh onRefresh={handleRefresh}>
        <ScrollLoadData
          size={10}
          enabled={isActive}
          queryKey={queryKey}
          queryFn={queryFn}
        >
          {(list) => {
            if (data.custom.widget === "User") {
              return <UserSearchUserInfo data={list} />;
            }
            if (data.custom.widget === "Dynamic") {
              return <UserSearchDynamic data={list} />;
            }
            if (data.custom.widget === "Video") {
              return <UserSearchVideo data={list} />;
            }
            if (data.custom.widget === "UserTransaction") {
              return <UserSearchTransaction data={list} />;
            }
            if (data.custom.widget === "PlaybackVideo") {
              return <UserSearchPlaybackVideo data={list} />;
            }
          }}
        </ScrollLoadData>
      </PullToRefresh>
    </ScrollArea>
  );
};
