import { DynamicItem } from "@/components/work/dynamic-item";
import { DynamicResult } from "@/type/dynamic-result";
import { Divider } from "antd-mobile";
import { Fragment } from "react/jsx-runtime";

interface Props {
  data: DynamicResult[];
}

export const UserSearchDynamic = (props: Props) => {
  const { data } = props;

  return (
    <div className="flex flex-col">
      {data.map((item, index) => (
        <Fragment key={item.id}>
          <DynamicItem data={item} />
          {index !== data.length - 1 && (
            <Divider className="border-divider my-[10px]" />
          )}
        </Fragment>
      ))}
    </div>
  );
};
