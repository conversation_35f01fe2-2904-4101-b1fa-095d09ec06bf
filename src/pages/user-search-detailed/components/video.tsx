import { VideoItem } from "@/pages/home/<USER>/components/video-item";
import { VideoResult } from "@/type/video-result";
import { AspectRatio } from "@/ui/aspect-ratio";

interface Props {
  data: VideoResult[];
}

export const UserSearchVideo = (props: Props) => {
  const { data } = props;

  return (
    <div className="grid grid-cols-2 gap-[15px]">
      {data.map((item) => (
        <AspectRatio key={item.id} ratio={1}>
          <VideoItem data={item} />
        </AspectRatio>
      ))}
    </div>
  );
};
