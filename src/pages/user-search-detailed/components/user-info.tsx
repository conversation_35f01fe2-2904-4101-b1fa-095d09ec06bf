import { UserInfo } from "@/components/work/user-info";
import { User } from "@/type/user";
import { Divider } from "antd-mobile";
import { Fragment } from "react/jsx-runtime";

interface Props {
  data: User[];
}

export const UserSearchUserInfo = (props: Props) => {
  const { data } = props;

  return (
    <div className="flex flex-col">
      {data.map((item, index) => (
        <Fragment key={item.id}>
          <UserInfo user={item} size={50} />
          {index !== data.length - 1 && (
            <Divider className="border-divider my-[10px]" />
          )}
        </Fragment>
      ))}
    </div>
  );
};
