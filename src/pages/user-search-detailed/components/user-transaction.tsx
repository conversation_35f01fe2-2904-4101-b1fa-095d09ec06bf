import { TradeItem } from "@/pages/home/<USER>/trade/components/trade-item";
import { UserTransactionResult } from "@/type/user-transaction-result";
import { Divider } from "antd-mobile";
import { Fragment } from "react/jsx-runtime";

interface Props {
  data: UserTransactionResult[];
}

export const UserSearchTransaction = (props: Props) => {
  const { data } = props;

  return (
    <div className="flex flex-col">
      {data.map((item, index) => (
        <Fragment key={item.id}>
          <div className="h-[250px]">
            <TradeItem data={item} />
          </div>
          {index !== data.length - 1 && (
            <Divider className="border-divider my-[10px]" />
          )}
        </Fragment>
      ))}
    </div>
  );
};
