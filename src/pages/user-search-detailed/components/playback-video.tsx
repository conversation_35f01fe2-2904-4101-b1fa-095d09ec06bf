import { PlaybackTabItem } from "@/pages/home/<USER>/components/playback-tab-item";
import { PlaybackVideoResult } from "@/type/playback-video-result";

interface Props {
  data: PlaybackVideoResult[];
}

export const UserSearchPlaybackVideo = (props: Props) => {
  const { data } = props;

  return (
    <div className="grid grid-cols-2 gap-[15px]">
      {data.map((item) => (
        <PlaybackTabItem key={item.id} data={item} />
      ))}
    </div>
  );
};
