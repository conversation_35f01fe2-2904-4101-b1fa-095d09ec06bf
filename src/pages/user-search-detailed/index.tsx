import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { NavigationSearch } from "@/components/work/navigation-search";
import { Tabs } from "@/components/work/tabs";
import { <PERSON><PERSON> } from "@/ui/button";
import { ScrollArea, ScrollBar } from "@/ui/scroll-area";
import { Form } from "antd-mobile";
import { useAsyncValue, useSearchParams } from "react-router-dom";
import { TabContent } from "./components/tab-content";
import { LayoutConfigUserSearchDetailed } from "@/type/layout-config";
import { useState } from "react";

export default function UserSearchDetailed() {
  const { data } = useAsyncValue() as { data: LayoutConfigUserSearchDetailed };

  const tabs = data.tabs.items;

  const [form] = Form.useForm();

  const value = Form.useWatch("name", form);

  const [searchParams, setSearchParams] = useSearchParams();
  const defaultIndex = Number(searchParams.get("defaultIndex")) || 0;
  const keyword = searchParams.get("keyword") || "";

  const [activeKey, setActiveKey] = useState(defaultIndex);

  const handleSearch = () => {
    if (!value.trim()) return;

    setSearchParams(
      {
        defaultIndex: String(defaultIndex),
        keyword: value,
      },
      {
        replace: true,
      }
    );
  };

  return (
    <PageWrapper>
      <NavigationBar
        canBack
        content={
          <div className="flex-1 flex items-center gap-2">
            <NavigationSearch
              form={form}
              placeholder="请输入要搜索的内容"
              name="name"
              initialValue={keyword}
              onFinish={handleSearch}
            />
            <Button
              variant="primary"
              className="text-[13px] py-0 px-[10px] h-[32px]"
              disabled={!value?.trim()}
              onClick={handleSearch}
            >
              搜索
            </Button>
          </div>
        }
      />
      <PageMain needScroll={false}>
        <div className="pt-0 p-[15px] h-full flex flex-col">
          <ScrollArea>
            <div>
              <Tabs tabs={tabs} value={activeKey} onChange={setActiveKey} />
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
          <div className="flex-1 h-0">
            {tabs.map((tab, index) => (
              <TabContent
                key={index}
                index={index}
                isActive={index === activeKey}
                data={tab}
                keyword={keyword}
              />
            ))}
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
