import { proxyBackground } from "@/components/image-list";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { UserAvatar } from "@/components/user-avatar";
import { confirmDialog } from "@/components/work/confirm-dialog";
import {
  UserInfoModal,
  UserInfoModalRef,
} from "@/components/work/user-info-modal";
import { useConfigList } from "@/hooks/use-config-list";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { useScrollOpacity } from "@/hooks/user-scroll-opacity";
import { agentApply, getInviteList } from "@/lib/api/user";
import { PageParams } from "@/type";
import { UserInviteInfoResult } from "@/type/user-invite-info-result";
import { But<PERSON> } from "@/ui/button";
import { Textarea } from "@/ui/textarea";
import { formatTimeLine } from "@/utils/format-time-line";
import { useMutation } from "@tanstack/react-query";
import { Image, Toast } from "antd-mobile";
import dayjs from "dayjs";
import { Fragment, useRef } from "react";
import { useAsyncValue } from "react-router-dom";

export default function UserAgentPage() {
  const { data } = useAsyncValue() as { data: UserInviteInfoResult };

  const { getSystemConfig } = useConfigList();

  const queryKey = ["user-agent-invite-list"];
  const queryFn = (params: PageParams) => getInviteList(params);

  const contentRef = useRef<HTMLTextAreaElement>(null);

  const { opacity, scrollRef } = useScrollOpacity(200);

  const { navigateRoute } = useNavigateRoute();

  const agentApplyMutation = useMutation({
    mutationFn: agentApply,
  });
  const userInfoModalRef = useRef<UserInfoModalRef>(null);

  const openUserInfoModal = (id: string) => {
    userInfoModalRef.current?.open(id);
  };

  const handleInvite = () => {
    navigateRoute("/user-invite-poster");
  };

  const handleAgentApply = async () => {
    const result = await confirmDialog.show(
      <div>
        <Textarea
          ref={contentRef}
          placeholder="请输入申请理由"
          rows={3}
          className="text-scaffold-background border-[#eee] rounded-[5px] px-[15px] py-[10px]"
        />
      </div>,
      "申请成为代理人"
    );

    if (result) {
      Toast.show({
        icon: "loading",
        content: "请稍后...",
        duration: 0,
      });
      const { ok } = await agentApplyMutation.mutateAsync(
        contentRef.current?.value
      );

      if (ok) {
        Toast.show({
          content: "代理人申请成功，请等待管理员审核!",
        });
      }
    }
  };

  return (
    <PageWrapper className="relative z-10">
      <NavigationBar
        canBack
        title="代理中心"
        className="bg-transparent text-transparent"
        style={
          opacity > 0.5
            ? {
                background: `hsla(var(--app-bar-background)/${opacity})`,
                color: `hsla(var(--foreground)/${opacity})`,
              }
            : {}
        }
      />
      <PageMain scrollRef={scrollRef}>
        <div className="mt-[200px] px-[15px] pt-[15px]">
          <div className="h-[150px] bg-white rounded-[10px] flex flex-col gap-[10px] justify-center items-center">
            <span className="text-base text-[#9F370D] font-bold">
              我的邀请码
            </span>
            <span className="text-[25px] text-[#FF4900] font-bold">
              {data.inviteCode}
            </span>
            <span className="text-sm text-[#9F370D] font-bold">
              总邀请人数:{data.totalInviteNumber}，今日收益:
              {data.todayIncome ?? 0}，总收益:{data.totalIncome ?? 0}
            </span>
          </div>
        </div>
        <div className="px-[15px] pt-[15px]">
          <div className="bg-white rounded-[10px] p-[15px] flex flex-col justify-center items-center">
            <span className="text-[#9F370D] text-sm font-bold whitespace-pre-wrap text-center">
              {data.note}
            </span>
            <div className="mt-[25px] w-full">
              <Button
                variant="primary"
                size="lg"
                className="w-full"
                onClick={handleInvite}
              >
                立即邀请
              </Button>
              {data.canApplyAgent ? (
                <div className="mt-[5px] w-full">
                  <Button
                    variant="primary"
                    size="lg"
                    className="w-full"
                    onClick={handleAgentApply}
                  >
                    申请成为代理人
                  </Button>
                </div>
              ) : null}
            </div>
          </div>
        </div>
        <div className="px-[15px] pt-[15px]">
          <div className="bg-white rounded-[10px] p-[15px] flex flex-col gap-[15px]">
            <span className="text-[17px] text-[#9F370D] font-bold text-center">
              邀请说明
            </span>
            <span className="text-sm text-black whitespace-pre-wrap">
              {getSystemConfig("USER_AGENT_INVITE_NOTE")}
            </span>
          </div>
        </div>
        <div className="p-[15px]">
          <div className="bg-white rounded-[10px] p-[15px] flex flex-col gap-[15px]">
            <span className="text-[17px] text-[#9F370D] font-bold text-center">
              我邀请的好友
            </span>
            <div>
              <ScrollLoadData
                queryKey={queryKey}
                queryFn={queryFn}
                noMoreStyle={{ color: "#e1dcff" }}
              >
                {(data) => (
                  <div className="w-full">
                    {data.map((item) => (
                      <Fragment key={item.user?.id}>
                        <div
                          className="flex gap-[10px] w-full"
                          onClick={() => openUserInfoModal(item.user?.id!)}
                        >
                          <UserAvatar
                            src={item.user?.avatarUrl!}
                            size={25}
                            isVip={item.user?.vip}
                          />
                          <span className="text-black text-sm line-clamp-1 flex-1">
                            {item.user?.nickname}
                          </span>
                          <span className="text-sm text-[#666]">
                            {formatTimeLine(dayjs(item.cdate!).valueOf())}
                            (今日收益:{item.todayIncome ?? 0} 总收益:
                            {item.totalIncome ?? 0})
                          </span>
                        </div>
                      </Fragment>
                    ))}
                  </div>
                )}
              </ScrollLoadData>
              <UserInfoModal ref={userInfoModalRef} />
            </div>
          </div>
        </div>
      </PageMain>
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-[-1]">
        <Image
          src={proxyBackground}
          width="100%"
          height="auto"
          placeholder={null}
          fallback={null}
          className="object-cover relative top-[-50px]"
        />
      </div>
    </PageWrapper>
  );
}
