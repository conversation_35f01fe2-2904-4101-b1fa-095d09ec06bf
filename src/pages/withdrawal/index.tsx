import { CommonIcon } from "@/components/common-icon";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ArrowIcon } from "@/components/svg-icon";
import { tipsDialog } from "@/components/work/tips-dialog";
import { useConfigList } from "@/hooks/use-config-list";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { userWalletQueryKey, useUserWallet } from "@/hooks/use-user-wallet";
import { withdrawApply } from "@/lib/api/wallet";
import { queryClient } from "@/provider/query-client";
import { useBankState } from "@/store/bank-state";
import { Button } from "@/ui/button";
import { Input } from "@/ui/input";
import { userWalletType } from "@/utils/enums";
import { useMutation } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import { useState } from "react";

export default function Withdrawal() {
  const { getBalanceOrEmptyByType } = useUserWallet();
  const { getSystemConfig } = useConfigList();
  const { navigateRoute } = useNavigateRoute();

  const [amount, setAmount] = useState<string>("");

  const { bankState } = useBankState();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === "") {
      setAmount("");
    } else {
      const parsedValue = parseInt(value.replace(/[^\d]/g, ""), 10);
      setAmount(isNaN(parsedValue) ? "" : parsedValue.toString());
    }
  };

  const handleSelectBank = () => {
    navigateRoute("/bank-list", {});
  };

  const withdrawApplyMutation = useMutation({
    mutationFn: withdrawApply,
  });

  const handleWithdraw = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await withdrawApplyMutation.mutateAsync({
      bankCardId: bankState?.id!,
      amount,
    });

    if (ok) {
      tipsDialog.success({
        title: "提现申请成功",
        content: "提现申请成功，等待管理员审核!",
        confirmText: "知道了",
      });

      queryClient.refetchQueries({
        queryKey: userWalletQueryKey,
      });
    }
  };

  return (
    <PageWrapper className="relative z-10 bg-scaffold-background">
      <NavigationBar
        canBack
        title={`${userWalletType.bean.label}提现`}
        className="bg-transparent"
      />
      <PageMain>
        <div className="h-[125px] flex flex-col gap-[10px] items-center justify-center">
          <span className="text-[35px] text-white">
            {getBalanceOrEmptyByType(userWalletType.bean.value)}
          </span>
          <div className="flex gap-[5px] items-center">
            <CommonIcon name="bean" h={23} w={23} />
            <span className="text-base text-white">
              当前{userWalletType.bean.label}
            </span>
          </div>
        </div>
        <div className="flex flex-col rounded-t-[10px] bg-scaffold-background px-[15px] py-[10px]">
          <div className="pt-[25px] flex flex-col" onClick={handleSelectBank}>
            <span className="text-[15px]">银行卡</span>
            <div className="flex items-center justify-between pt-[10px]">
              <span className="text-sm">
                {bankState
                  ? `${bankState.bankName}  ${bankState.userName}  ${bankState.bankCardNumber}`
                  : "暂未选择银行卡，点击选择"}
              </span>
              <ArrowIcon />
            </div>
          </div>
          <div className="pt-[25px] flex flex-col">
            <span className="text-[15px]">提现金额</span>
            <Input
              placeholder="请输入提现金额，仅能输入整数"
              className="text-[15px]"
              inputMode="numeric"
              maxLength={7}
              value={amount}
              onInput={handleInputChange}
            />
          </div>
          <div className="pt-[25px]">
            <Button
              variant="primary"
              size="lg"
              className="w-full"
              disabled={!amount || !Number(amount) || !bankState}
              onClick={handleWithdraw}
            >
              提现
            </Button>
          </div>
          <div className="pt-[15px]">
            <span className="text-sm text-hint-color">说明</span>
            <div className="pt-[10px] text-xs text-hint-color whitespace-pre-wrap">
              {getSystemConfig("WITHDRAWAL_TIPS")}
            </div>
          </div>
        </div>
      </PageMain>
      <div className="absolute left-0 top-0 w-full h-[200px] withdrawal-bg z-[-1]" />
    </PageWrapper>
  );
}
