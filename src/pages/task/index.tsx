import { DefaultLoadImage } from "@/components/default-load-image";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { getTaskList, receiveTaskReward } from "@/lib/api/user";
import { getRoute } from "@/router/route-map";
import { PageParams } from "@/type";
import { Button } from "@/ui/button";
import { UserTaskState, userTaskStateConfig } from "@/utils/enums";
import { useMutation } from "@tanstack/react-query";
import { Image, Toast } from "antd-mobile";

export default function TaskPage() {
  const { navigateRoute } = useNavigateRoute();

  const queryKey = ["task-list"];
  const queryFn = (params: PageParams) => {
    return getTaskList(params);
  };

  const navigateUrl = (uri: string) => {
    navigateRoute(getRoute(uri));
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  const receiveTaskRewardMutation = useMutation({
    mutationFn: receiveTaskReward,
  });

  const handleReceiveTaskReward = async (id: string) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await receiveTaskRewardMutation.mutateAsync(id);
    if (ok) {
      handleRefresh();
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="任务中心" />
      <PageMain onRefresh={handleRefresh}>
        <div className="p-[15px]">
          <ScrollLoadData queryFn={queryFn} queryKey={queryKey}>
            {(data) => {
              return (
                <div className="flex flex-col gap-[30px]">
                  {data.map((item) => {
                    return (
                      <div key={item.id} className="flex items-center">
                        <div className="flex-1 flex items-center">
                          <div className="w-[40px] h-[40px]">
                            <Image
                              src={item.icon}
                              width="100%"
                              height="100%"
                              fit="cover"
                              placeholder={<DefaultLoadImage />}
                              fallback={<DefaultLoadImage />}
                              onLoad={() => {}}
                            />
                          </div>
                          <div className="pl-3 flex flex-col">
                            <span>
                              <span className="text-base">
                                {item.title ?? "特殊任务"}
                              </span>
                              {item.vip ? (
                                <span className="ml-[5px] text-[13px] text-white px-2 bg-[#ff5252] rounded-[10px]">
                                  VIP
                                </span>
                              ) : null}
                            </span>
                            <span className="text-[13px] text-hint-color">
                              {item.content ?? "暂无奖励"}
                            </span>
                          </div>
                        </div>
                        <div>
                          {item.state === UserTaskState.WaitReward ? (
                            <Button
                              variant="primary"
                              className="text-[13px] w-[75px] h-[30px]"
                              onClick={() => {
                                handleReceiveTaskReward(item.id!);
                              }}
                            >
                              领取奖励
                            </Button>
                          ) : null}
                          {!item.state ||
                          ![
                            UserTaskState.WaitReward,
                            UserTaskState.Finish,
                          ].includes(item.state) ? (
                            <Button
                              variant="secondary"
                              className="text-[13px] w-[75px] h-[30px]"
                              onClick={() => {
                                navigateUrl(item.targetUri!);
                              }}
                            >
                              {item.targetLabel ?? "去完成"}
                            </Button>
                          ) : null}
                          {item.state === UserTaskState.Finish ? (
                            <Button
                              variant="secondary"
                              className="text-[13px] w-[75px] h-[30px] text-hint-color bg-transparent border-hint-color"
                            >
                              {userTaskStateConfig[item.state]}
                            </Button>
                          ) : null}
                        </div>
                      </div>
                    );
                  })}
                </div>
              );
            }}
          </ScrollLoadData>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
