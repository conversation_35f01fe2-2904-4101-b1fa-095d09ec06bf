import { useUploadInfo } from "@/hooks/use-upload-info";
import { uploadFile } from "@/utils/upload-file";
import { Image, ImageUploader, ImageUploaderRef } from "antd-mobile";
import { useRef } from "react";

interface Props {
  value?: {
    id?: string;
    url?: string;
  };
  onChange?: (value: { id: string; url: string }) => void;
}

export const UploadSingleImage = (props: Props) => {
  const { value, onChange } = props;

  const { imageUploadInfo } = useUploadInfo();
  const inputRef = useRef<ImageUploaderRef>(null);

  return (
    <div>
      <div className="w-[108px] h-[108px] relative">
        <Image
          src={value?.url ?? ""}
          className="w-full h-full rounded-[10px]"
          fit="cover"
          onClick={() => inputRef.current?.nativeElement?.click()}
        />
        <div className="absolute bottom-0 left-0 w-full h-[18px] bg-black/40 flex justify-center items-center">
          <span className="text-white text-[12px]">更换封面</span>
        </div>
      </div>
      <ImageUploader
        className="hidden"
        ref={inputRef}
        upload={async (file) => {
          const res = await uploadFile(file, imageUploadInfo!, {
            showDialog: true,
          });

          onChange?.({
            id: res?.id ?? "",
            url: res?.images?.Original?.url ?? "",
          });

          return {
            url: res?.images?.Original?.url ?? "",
          };
        }}
      />
    </div>
  );
};
