import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { playbackVideoDetail, playbackVideoUpdate } from "@/lib/api/video";
import { <PERSON><PERSON> } from "@/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/ui/form";
import { Input } from "@/ui/input";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Switch, Toast } from "antd-mobile";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { useSearchParams } from "react-router-dom";
import { UploadSingleImage } from "./components/UploadSingleImage";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { queryClient } from "@/provider/query-client";

export default function PlaybackModifyPage() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id");

  const form = useForm();

  const { data } = useQuery({
    queryKey: ["playback-video-detail", id],
    queryFn: () => playbackVideoDetail(id!),
  });
  const { navigateBack } = useNavigateRoute();

  useEffect(() => {
    if (data?.data) {
      form.setValue("cover", { url: data.data.cover });
      form.setValue("title", data.data.title);
      form.setValue("content", data.data.content);
      form.setValue("playPrice", data.data.playPrice);
      form.setValue("downloadPrice", data.data.downloadPrice);
      form.setValue("top", data.data.top);
      form.setValue("hidden", data.data.hidden);
    }
  }, [data?.data, form]);

  const { mutateAsync: updatePlaybackVideo } = useMutation({
    mutationFn: playbackVideoUpdate,
  });

  const handleSubmit = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const values = form.getValues();

    const { ok } = await updatePlaybackVideo({
      id: id!,
      ...{
        ...values,
        cover: values.cover.id,
      },
    });

    if (ok) {
      navigateBack();
      queryClient.invalidateQueries({
        queryKey: ["playback-video-detail", id],
      });
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="修改回放信息" />
      <PageMain>
        <div className="p-[15px] flex flex-col gap-[25px]">
          <Form {...form}>
            <div className="flex flex-col gap-[25px]">
              <FormField
                control={form.control}
                name="cover"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>封面</FormLabel>
                      <FormControl>
                        <UploadSingleImage
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>标题</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="请输入标题" />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>描述</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="请输入内容" />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="playPrice"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>播放价格</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          placeholder="请输入播放价格"
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="downloadPrice"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>下载价格</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          placeholder="请输入下载价格"
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="hidden"
                render={({ field }) => {
                  return (
                    <FormItem className="flex items-center justify-between">
                      <FormLabel>是否隐藏回放</FormLabel>
                      <FormControl>
                        <Switch
                          className="!m-0"
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="top"
                render={({ field }) => {
                  return (
                    <FormItem className="flex items-center justify-between">
                      <FormLabel>是否置顶</FormLabel>
                      <FormControl>
                        <Switch
                          className="!m-0"
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
            </div>
          </Form>
          <div>
            <Button
              disabled={!data?.data?.id}
              variant="primary"
              className="w-full"
              onClick={handleSubmit}
            >
              保存
            </Button>
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
