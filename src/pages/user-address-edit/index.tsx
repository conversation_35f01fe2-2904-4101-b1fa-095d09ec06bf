import { CityPicker } from "@/components/city-picker";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { getUserAddress, saveUserAddress } from "@/lib/api/user";
import { Button } from "@/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/ui/form";
import { Input } from "@/ui/input";
import { Textarea } from "@/ui/textarea";
import { extractLeadingNumbers } from "@/utils";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { useSearchParams } from "react-router-dom";

export default function UserAddressEdit() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id");

  const form = useForm<any>();

  const { data } = useQuery({
    queryKey: ["user-address", id],
    queryFn: () => getUserAddress(id!),
    enabled: !!id,
  });

  useEffect(() => {
    if (data?.data) {
      const { name, phone, province, city, region, address } = data.data;
      form.setValue("name", name);
      form.setValue("phone", phone);
      form.setValue("region", [province, city, region]);
      form.setValue("address", address);
    }
  }, [data?.data, form]);

  const { mutateAsync } = useMutation({
    mutationFn: saveUserAddress,
  });

  const { navigateBack } = useNavigateRoute();

  const handleSubmit = async () => {
    const { name, phone, region, address } = form.getValues();

    if (!name) {
      Toast.show({ content: "请输入收件人姓名" });
      return;
    }

    if (!phone) {
      Toast.show({ content: "请输入收件人电话" });
      return;
    }

    if (!region) {
      Toast.show({ content: "请选择收件地址" });
      return;
    }

    if (!address) {
      Toast.show({ content: "请输入详细地址" });
      return;
    }

    Toast.show({
      content: "请稍后...",
      icon: "loading",
      duration: 0,
    });

    const { ok } = await mutateAsync({
      id,
      ...data?.data,
      name,
      phone,
      address,
      province: region[0],
      city: region[1],
      region: region[2],
    });

    if (ok) {
      navigateBack();
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title={`${id ? "编辑" : "新增"}地址`} />
      <PageMain>
        <Form {...form}>
          <div className="flex flex-col gap-8 p-[15px]">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>收件人</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="请输入收件人姓名"
                        {...field}
                        autoComplete="off"
                      />
                    </FormControl>
                  </FormItem>
                );
              }}
            />
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>收件电话</FormLabel>
                  <FormControl>
                    <div className="flex items-center relative z-0">
                      <span className="absolute ml-[5px] z-[-1]">+86</span>
                      <Input
                        placeholder="请输入收件人姓名"
                        className="pl-[45px]"
                        maxLength={11}
                        {...field}
                        autoComplete="off"
                        value={extractLeadingNumbers(field.value)}
                      />
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="region"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>收件地址</FormLabel>
                    <FormControl>
                      <CityPicker {...field} placeholder="点击选择收件地址" />
                    </FormControl>
                  </FormItem>
                );
              }}
            />
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>详细地址</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="请输入详细地址"
                        rows={3}
                        className="px-[15px] py-[10px] rounded-[10px] resize-none"
                      />
                    </FormControl>
                  </FormItem>
                );
              }}
            />
          </div>
        </Form>
        <div className="h-[20px]" />
        <div className="p-[15px]">
          <Button variant="primary" className="w-full" onClick={handleSubmit}>
            提交
          </Button>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
