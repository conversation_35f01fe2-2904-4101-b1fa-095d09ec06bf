import { CommonIcon } from "@/components/common-icon";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { UserInfo } from "@/components/work/user-info";
import { UserOrderOpt } from "@/components/work/user-order-opt";
import { useCountDownTimer } from "@/hooks/use-count-down-timer";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { getUserOrderCreate } from "@/lib/api/user";
import { getRoute } from "@/router/route-map";
import { UserOrderDetailedResult } from "@/type/user-order-detailed-result";
import { UserOrderState, userOrderStateConfig } from "@/utils/enums";
import { formatDuration } from "@/utils/format-duration";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { layoutConfigOptions } from "@/utils/query-options";
import { useQuery } from "@tanstack/react-query";
import { Image, Toast } from "antd-mobile";
import dayjs from "dayjs";
import { useMemo } from "react";
import { useSearchParams } from "react-router-dom";

export default function UserOrderDetail() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id") || "";

  const { data: userInfo } = useFullUserInfo();

  const { data: config } = useQuery(
    layoutConfigOptions<{
      items: Array<{
        key: string;
        label: string;
        copy: boolean;
      }>;
    }>(LayoutConfigKey.UserOrderDetailed)
  );

  const { data: orderData, refetch } = useQuery({
    queryKey: ["user-order-create", id],
    queryFn: () => getUserOrderCreate(id),
    enabled: !!id,
  });

  const countDownTime = useMemo(() => {
    if (
      orderData?.data?.state === UserOrderState.WaitingPay &&
      orderData?.data?.expireDate
    ) {
      return dayjs(orderData.data.expireDate).diff(dayjs(), "second");
    }

    if (
      !orderData?.data?.existRefund &&
      orderData?.data?.state === UserOrderState.Shipped &&
      orderData?.data?.autoReceiveDate
    ) {
      return dayjs(orderData.data.autoReceiveDate).diff(dayjs(), "second");
    }

    return 0;
  }, [orderData?.data]);

  const { seconds, isRunning } = useCountDownTimer(countDownTime, true);

  const hasMerchant = orderData?.data?.merchant?.id === userInfo?.id;

  const { navigateRoute } = useNavigateRoute();

  const handleTargetUriClick = () => {
    if (orderData?.data?.targetUri) {
      navigateRoute(getRoute(orderData.data.targetUri));
    }
  };

  const handleCopy = (value: string) => {
    navigator.clipboard.writeText(value);
    Toast.show("内容已复制到粘贴板!");
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="订单详情" />
      <PageMain
        extra={
          <div className="h-[55px] px-[15px] border-t border-divider border-solid flex items-center">
            {orderData?.data ? (
              <UserOrderOpt
                data={orderData.data}
                merchant={hasMerchant}
                onRefresh={() => refetch()}
              />
            ) : null}
          </div>
        }
      >
        <div className="p-[15px] bg-scaffold-background">
          <span className="text-[#FF9212]">
            订单状态:{" "}
            {userOrderStateConfig[
              orderData?.data?.state as keyof typeof userOrderStateConfig
            ] ?? "未知"}
          </span>
          {orderData?.data?.state === UserOrderState.WaitingPay &&
          orderData.data.expireDate ? (
            <span className="text-[#FF9212]">
              {isRunning
                ? `请在${formatDuration(seconds)}内完成支付!`
                : "，订单已失效"}
            </span>
          ) : null}
          {orderData?.data?.state === UserOrderState.Shipped &&
          orderData?.data?.autoReceiveDate ? (
            <span className="text-[#FF9212]">
              {isRunning
                ? `系统将于${formatDuration(seconds)}后自动确认收货`
                : "，订单已收货"}
            </span>
          ) : null}
        </div>
        <div
          className="flex gap-[15px] items-center p-[15px]"
          onClick={() =>
            handleCopy(
              `${orderData?.data?.receiveName} ${orderData?.data?.receivePhone}\n${orderData?.data?.fullAddress}`
            )
          }
        >
          <CommonIcon name="location" w={22} h={22} />
          <div className="flex flex-col flex-1 w-0">
            {!orderData?.data?.fullAddress ? (
              "点击选择收货地址~"
            ) : (
              <>
                <span className="text-[13px] text-hint-color">
                  {orderData?.data?.receiveProvince}
                  {orderData?.data?.receiveCity}
                  {orderData?.data?.receiveRegion}
                </span>
                <span className="text-[17px]">
                  {orderData?.data?.receiveAddress}
                </span>
                <span className="text-[13px] text-hint-color">
                  {orderData?.data?.receiveName}
                  {orderData?.data?.receivePhone}
                </span>
              </>
            )}
          </div>
          <span className="text-[#448AFF]">复制</span>
        </div>
        <div className="bg-scaffold-background h-[15px]" />
        <div className="p-[15px] flex flex-col">
          {!hasMerchant && orderData?.data?.merchant ? (
            <div className="pb-[15px]">
              <UserInfo user={orderData.data.merchant} size={47} />
            </div>
          ) : null}
          {hasMerchant && orderData?.data?.user ? (
            <div className="pb-[15px]">
              <UserInfo user={orderData.data.user} size={47} />
            </div>
          ) : null}
          <div
            className="flex gap-[15px] items-center"
            onClick={handleTargetUriClick}
          >
            <div className="w-[69px] h-[69px]">
              <Image
                width="100%"
                height="100%"
                src={orderData?.data?.cover}
                fit="cover"
                className="rounded-[5px]"
              />
            </div>
            <div className="flex-1 w-0 flex flex-col">
              <span className="line-clamp-1">
                <span className="bg-[#FF005C] px-[2px] rounded-[5px] mr-[5px]">
                  {orderData?.data?.targetLabel}
                </span>
                {orderData?.data?.title}
              </span>
              {orderData?.data?.content ? (
                <span className="text-[13px] line-clamp-2">
                  {orderData?.data?.content}
                </span>
              ) : null}
              <span className="text-[#FF005C] text-sm font-bold">
                ¥ {orderData?.data?.totalPrice}
              </span>
            </div>
          </div>
        </div>
        <div className="bg-scaffold-background h-[15px]" />
        <div className="flex flex-col bg-divider gap-[1px]">
          {config?.data?.items.map((item) => (
            <div
              key={item.key}
              className="bg-app-bar-background flex items-center justify-between h-[75px] px-[15px]"
              onClick={() => {
                if (item.copy) {
                  handleCopy(
                    orderData?.data?.[
                      item.key as keyof UserOrderDetailedResult
                    ] as string
                  );
                }
              }}
            >
              <span>{item.label}</span>
              <div>
                <span className="text-[13px] text-hint-color">
                  {(orderData?.data?.[
                    item.key as keyof UserOrderDetailedResult
                  ] as string) ?? "-"}
                </span>
                {item.copy ? (
                  <span className="text-[#448AFF]">复制</span>
                ) : null}
              </div>
            </div>
          ))}
        </div>
      </PageMain>
    </PageWrapper>
  );
}
