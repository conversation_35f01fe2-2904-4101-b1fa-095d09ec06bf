import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { UserInfo } from "@/components/work/user-info";
import { dynamicLikeList } from "@/lib/api/dynamic";
import { PageParams } from "@/type";
import { useSearchParams } from "react-router-dom";

export default function DynamicLikeListPage() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("dynamicId");

  const queryKey = ["dynamic-like-list", id];
  const queryFn = (params: PageParams) =>
    dynamicLikeList({ id: id || "" }, params);

  return (
    <PageWrapper>
      <NavigationBar canBack title="点赞列表" />
      <PageMain>
        <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="flex flex-col p-[15px]">
              {data.map((item) => (
                <UserInfo key={item.id} user={item} size={50} />
              ))}
            </div>
          )}
        </ScrollLoadData>
      </PageMain>
    </PageWrapper>
  );
}
