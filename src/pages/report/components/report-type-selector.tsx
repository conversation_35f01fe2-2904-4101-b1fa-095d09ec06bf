import { reportTypeList } from "@/lib/api/system";
import { useQuery } from "@tanstack/react-query";
import classNames from "classnames";

interface Props {
  value?: string;
  onChange?: (value: string) => void;
}

export const ReportTypeSelector = ({ value, onChange }: Props) => {
  const { data: reportType } = useQuery({
    queryKey: ["report-type-list"],
    queryFn: () => reportTypeList(),
    staleTime: Infinity,
  });

  return (
    <div className="flex flex-wrap gap-[10px]">
      {reportType?.data?.map((item) => (
        <div
          key={item.id}
          className={classNames(
            "px-[15px] py-[5px] rounded-[20px] text-[#999] dark:text-[#7F848D] bg-[#999]/10 dark:bg-[#7F848D]/10",
            value === item.id && " !text-[#FF3460] !bg-[#FF3460]/10"
          )}
          onClick={() => onChange?.(item.id)}
        >
          {item.note}
        </div>
      ))}
    </div>
  );
};
