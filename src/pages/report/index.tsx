import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { MediaUpload } from "@/components/work/media-upload";
import { Button } from "@/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/ui/form";
import { Textarea } from "@/ui/textarea";
import { useForm } from "react-hook-form";
import { ReportTypeSelector } from "./components/report-type-selector";
import { useSearchParams } from "react-router-dom";
import { useMutation } from "@tanstack/react-query";
import { reportRecordCheck, reportRecordUrl } from "@/lib/api/system";
import { useUploadQueueManager } from "@/hooks/use-upload-queue-manager";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { Toast } from "antd-mobile";
import { getRandomParams } from "@/utils/common";

export default function ReportPage() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id");
  const type = searchParams.get("type");

  const form = useForm();
  const categoryId = form.watch("categoryId");

  const { mutateAsync } = useMutation({
    mutationFn: reportRecordCheck,
  });

  const { addToQueue } = useUploadQueueManager();
  const { navigateBack } = useNavigateRoute();

  const handleSubmit = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const data = form.getValues();

    const { resources, ...rest } = data;

    const { ok } = await mutateAsync({
      targetId: id,
      targetType: type,
      resourceIds: resources?.map(() => getRandomParams()),
      ...rest,
    });

    if (ok) {
      addToQueue(
        {
          title: "举报",
          url: reportRecordUrl,
          params: {
            ...rest,
            targetId: id,
            targetType: type,
            resourceIds: resources,
          },
        },
        () => {
          navigateBack();
        }
      );
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="举报" />
      <PageMain>
        <div className="p-[15px] flex flex-col gap-[25px]">
          <Form {...form}>
            <div className="flex flex-col gap-[25px]">
              <FormField
                control={form.control}
                name="categoryId"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>举报类型</FormLabel>
                      <FormControl>
                        <ReportTypeSelector {...field} />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>举报内容</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="请填写举报内容"
                          {...field}
                          autoComplete="off"
                          rows={5}
                          className="resize-none px-[15px] py-[10px] rounded-[10px]"
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="resources"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>图片说明</FormLabel>
                      <FormControl>
                        <MediaUpload
                          maxCount={9}
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
            </div>
          </Form>
          <div>
            <Button
              disabled={!categoryId}
              variant="primary"
              className="w-full"
              onClick={handleSubmit}
            >
              提交
            </Button>
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
