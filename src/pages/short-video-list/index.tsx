import { NavigationBar } from "@/components/navigation-bar";
import { PageWrapper } from "@/components/page-wrapper";
import { PageParams } from "@/type";
import { useSearchParams } from "react-router-dom";
import { shortVideoTag } from "@/lib/api/video";
import { ShortVideoSwiper } from "../home/<USER>/components/short-video-swiper";

export default function ShortVideoList() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id");
  const defaultIndex = Number(searchParams.get("defaultIndex"));

  const queryKey = ["short-video-tag", id!];
  const queryFn = (params: PageParams) => shortVideoTag({ id: id! }, params);

  return (
    <PageWrapper className="relative">
      <NavigationBar canBack className="fixed z-10 bg-transparent" />
      <ShortVideoSwiper
        queryKey={queryKey}
        queryFn={queryFn}
        defaultIndex={defaultIndex}
      />
    </PageWrapper>
  );
}
