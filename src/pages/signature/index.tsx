import { CommonIcon } from "@/components/common-icon";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { updateUserProfile } from "@/lib/api/user";
import { UserProfile } from "@/type/user-profile";
import { Button } from "@/ui/button";
import { Textarea } from "@/ui/textarea";
import { useMutation } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import { useMemo, useState } from "react";
import { useAsyncValue, useRevalidator } from "react-router-dom";

export default function Signature() {
  const { data } = useAsyncValue() as { data: UserProfile };
  const { revalidate } = useRevalidator();

  const [signature, setSignature] = useState(data.signature);

  const isUnsaved = useMemo(() => {
    return signature?.trim() !== data.signature?.trim();
  }, [signature, data.signature]);

  const updateUserProfileMutation = useMutation({
    mutationFn: updateUserProfile,
  });

  const handleSaveSignature = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await updateUserProfileMutation.mutateAsync({
      signature,
    });

    if (ok) {
      Toast.show({
        content: "个性签名修改成功!",
      });

      revalidate();
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="个性签名" />
      <PageMain className="bg-scaffold-background relative">
        <div className="bg-app-bar-background">
          <Textarea
            className="border-none rounded-none p-[15px]"
            rows={5}
            value={signature}
            readOnly={data.hasAuditSignature}
            onChange={(e) => setSignature(e.target.value)}
          />
        </div>
        {!data.hasAuditSignature ? (
          <div className="p-[15px]">
            <Button
              variant="primary"
              size="lg"
              className="w-full"
              disabled={!isUnsaved}
              onClick={handleSaveSignature}
            >
              保存
            </Button>
          </div>
        ) : null}
        {data.hasAuditAvatar ? (
          <div className="absolute top-0 right-5">
            <CommonIcon name="state_0" w={35} h={35} />
          </div>
        ) : null}
      </PageMain>
    </PageWrapper>
  );
}
