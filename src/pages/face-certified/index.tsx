import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { useConfigList } from "@/hooks/use-config-list";
import { Button } from "@/ui/button";
import { useMemo } from "react";

export default function FaceCertifiedPage() {
  const { getSystemConfig } = useConfigList();

  // todo: 人脸认证接入

  const contents = useMemo(() => {
    return getSystemConfig("USER_FACE_CERTIFIED_NOTE")
      ?.split("###\n")
      .map((item) => {
        const [title, ...rest] = item.split("\n") ?? [];
        return {
          title,
          content: rest.join("\n"),
        };
      });
  }, [getSystemConfig]);

  return (
    <PageWrapper>
      <NavigationBar canBack title="真人认证" />
      <PageMain className="face-certified-bg">
        <div className="p-[15px] pt-[25px] flex flex-col gap-5">
          <div className="flex flex-col gap-5">
            {contents?.map((item) => (
              <div
                key={item.title}
                className="p-[15px] bg-app-bar-background flex flex-col gap-[10px] rounded-[10px]"
              >
                <span className="text-[14px]">{item.title}</span>
                <span className="text-[13px] whitespace-pre-wrap">
                  {item.content}
                </span>
              </div>
            ))}
          </div>
          <Button variant="primary" size="lg" className="w-full">
            开始认证
          </Button>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
