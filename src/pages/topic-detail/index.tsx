import { DefaultLoadImage } from "@/components/default-load-image";
import { topicDetailBg } from "@/components/image-list";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { StateView } from "@/components/state-view";
import { topicDetail } from "@/lib/api/dynamic";
import { LayoutConfigTopicDetail } from "@/type/layout-config";
import { AspectRatio } from "@/ui/aspect-ratio";
import { useQuery } from "@tanstack/react-query";
import { Image } from "antd-mobile";
import { useAsyncValue, useSearchParams } from "react-router-dom";
import { TabContent } from "./components/tab-content";
import { useState } from "react";
import { ScrollArea, ScrollBar } from "@/ui/scroll-area";
import { Tabs } from "@/components/work/tabs";

export default function TopicDetail() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id") || undefined;
  const name = searchParams.get("name") || undefined;

  const { data: layoutConfig } = useAsyncValue() as {
    data: LayoutConfigTopicDetail;
  };

  const defaultIndex = layoutConfig.tabs.defaultIndex ?? -1;
  const tabs = layoutConfig.tabs.items;

  const [activeKey, setActiveKey] = useState(defaultIndex);

  const { data, isLoading } = useQuery({
    queryKey: ["topic", "detail", id, name],
    queryFn: () => topicDetail({ id: id!, name }),
  });

  return (
    <StateView isLoading={isLoading} isEmpty={!data?.data}>
      <PageWrapper>
        <div
          style={{ backgroundImage: `url("${topicDetailBg}")` }}
          className="h-[180px] bg-no-repeat bg-cover flex flex-col justify-between pb-[25px]"
        >
          <NavigationBar canBack title="话题详情" className="!bg-transparent" />
          <div className="flex gap-[15px] px-5">
            {data?.data?.cover ? (
              <div className="w-[72px]">
                <AspectRatio ratio={1}>
                  <Image
                    src={data?.data?.cover}
                    width="100%"
                    height="100%"
                    fit="cover"
                    className="rounded-[10px] border-white border-[2px] border-solid"
                    placeholder={<DefaultLoadImage />}
                    fallback={<DefaultLoadImage />}
                    onLoad={() => {}}
                  />
                </AspectRatio>
              </div>
            ) : null}
            <div>
              <span className="text-base line-clamp-1 text-white">
                #{data?.data?.name}
              </span>
              <span className="text-[13px] line-clamp-2 mb-[5px] text-white">
                {data?.data?.note ?? "暂无话题描述~"}
              </span>
              <span className="text-xs text-white">
                {data?.data?.dynamicCount}动态
              </span>
            </div>
          </div>
        </div>
        <div className="flex flex-col flex-1 h-0 px-[15px] py-2 bg-app-bar-background">
          <ScrollArea>
            <Tabs tabs={tabs} value={activeKey} onChange={setActiveKey} />
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
          <PageMain needScroll={false}>
            {tabs.map((tab, index) => (
              <TabContent
                key={index}
                isActive={index === activeKey}
                searchParams={searchParams}
                data={tab}
              />
            ))}
          </PageMain>
        </div>
      </PageWrapper>
    </StateView>
  );
}
