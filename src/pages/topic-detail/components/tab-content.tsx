import { ScrollLoadData } from "@/components/scroll-load-data";
import { getLayoutPageList } from "@/lib/api/layout";
import { PageParams } from "@/type";
import { BaseUrlConfig } from "@/type/layout-config";
import { Fragment, useEffect, useState } from "react";
import { PullToRefresh } from "@/components/pull-to-refresh";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { ScrollArea } from "@/ui/scroll-area";
import { DynamicItem } from "@/components/work/dynamic-item";
import { Divider } from "antd-mobile";
import { DynamicResult } from "@/type/dynamic-result";

interface Props {
  isActive: boolean;
  data: BaseUrlConfig;
  searchParams?: Record<string, any>;
}

export const TabContent = (props: Props) => {
  const { isActive, data, searchParams } = props;

  const [isInit, setIsInit] = useState(false);

  const queryKey = [data.url, data.params, searchParams];
  const queryFn = (params: PageParams) => {
    return getLayoutPageList<DynamicResult[]>({
      url: data.url,
      params,
      data: { ...data.params, ...searchParams },
    });
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  useEffect(() => {
    if (isActive) {
      setIsInit(true);
    }
  }, [isActive]);

  if (!isInit) {
    return null;
  }

  return (
    <ScrollArea className={isActive ? "h-full" : "hidden"}>
      <PullToRefresh onRefresh={handleRefresh}>
        <ScrollLoadData size={10} queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="flex flex-col pt-2">
              {data.map((item, index) => (
                <Fragment key={item.id}>
                  <DynamicItem data={item} />
                  {index !== data.length - 1 ? (
                    <Divider className="border-divider" />
                  ) : null}
                </Fragment>
              ))}
            </div>
          )}
        </ScrollLoadData>
      </PullToRefresh>
    </ScrollArea>
  );
};
