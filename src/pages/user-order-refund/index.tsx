import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { LayoutConfigUserOrderRefundHome } from "@/type/layout-config";
import { useState } from "react";
import { useAsyncValue } from "react-router-dom";
import { TabContent } from "./components/tab-content";
import { Tabs } from "@/components/work/tabs";
import { ScrollArea, ScrollBar } from "@/ui/scroll-area";

export default function UserOrderPage() {
  const { data: layoutConfig } = useAsyncValue() as {
    data: LayoutConfigUserOrderRefundHome;
  };

  const tabs =
    layoutConfig?.tabs?.items.map((item, index) => ({
      ...item,
      key: index,
    })) || [];

  const [activeTab, setActiveTab] = useState(
    layoutConfig.tabs.defaultIndex ?? 0
  );

  return (
    <PageWrapper>
      <NavigationBar canBack title="订单售后" />
      <PageMain needScroll={false}>
        <div className="p-[15px] flex flex-col h-full bg-scaffold-background">
          <ScrollArea>
            <div>
              <Tabs tabs={tabs} value={activeTab} onChange={setActiveTab} />
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
          <div className="flex-1 h-0">
            {tabs.map((tab, index) => (
              <TabContent
                key={index}
                index={index}
                isActive={index === activeTab}
                data={tab}
              />
            ))}
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
