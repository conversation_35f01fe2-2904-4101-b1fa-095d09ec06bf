import { NavigationBar } from "@/components/navigation-bar";
import { PageWrapper } from "@/components/page-wrapper";
import { useState } from "react";
import { PageMain } from "@/components/page-main";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { PageParams } from "@/type";
import { userBanList } from "@/lib/api/system";
import { ItemContent } from "./components/item-content";
import { NavigationSearch } from "@/components/work/navigation-search";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { PullToRefresh } from "@/components/pull-to-refresh";

export default function ViolationList() {
  const [searchParams, setSearchParams] = useState({ keyword: "" });

  const queryKey = ["violation-list", searchParams];
  const queryFn = (params: PageParams) => {
    return userBanList(searchParams, params);
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  return (
    <PageWrapper>
      <NavigationBar
        canBack
        content={
          <NavigationSearch
            placeholder="请输入昵称/ID搜索"
            name="keyword"
            onFinish={setSearchParams}
          />
        }
      />
      <PageMain className="bg-scaffold-background p-[15px]">
        <PullToRefresh onRefresh={handleRefresh}>
          <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
            {(data) => (
              <div className="flex flex-col w-full gap-5">
                {data.map((item) => (
                  <ItemContent key={item.id} data={item} />
                ))}
              </div>
            )}
          </ScrollLoadData>
        </PullToRefresh>
      </PageMain>
    </PageWrapper>
  );
}
