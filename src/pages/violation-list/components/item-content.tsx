import { UserAvatar } from "@/components/user-avatar";
import { UserBanRecord } from "@/type/user-ban-record";
import { Divider } from "antd-mobile";
import dayjs from "dayjs";

interface Props {
  data: UserBanRecord;
}

export const ItemContent = ({ data }: Props) => {
  const { user, content, cdate, note, recoveryDate } = data;

  return (
    <div className="flex flex-col p-[15px] bg-app-bar-background rounded-[10px]">
      <div className="relative flex gap-3">
        <UserAvatar src={user?.avatarUrl ?? ""} size={47} />
        <div className="flex flex-col justify-between">
          <span className="line-clamp-1 text-base">{user?.nickname}</span>
          <span className="line-clamp-1 text-[13px] text-hint-color">
            ID: {user?.userid}
          </span>
        </div>
        <div
          className="absolute top-0 right-[-15px] text-[13px] px-[10px] rounded-l-[10px]"
          style={{
            background: recoveryDate ? "#EBD5FF" : "rgb(255 89 93 / 30%)",
            color: recoveryDate ? "#8D3577" : "#FF595D",
          }}
        >
          {recoveryDate
            ? `${dayjs(recoveryDate).format("YYYY-MM-DD")}解封`
            : "永久封禁"}
        </div>
      </div>
      <Divider className="border-divider" />
      <div className="flex flex-col text-[13px] text-hint-color">
        <span>
          封禁时间:&nbsp;&nbsp;&nbsp;
          {cdate ? dayjs(cdate).format("YYYY-MM-DD") : "未知"}
        </span>
        <span>封禁原因:&nbsp;&nbsp;&nbsp;{note}</span>
        <span>封禁内容:&nbsp;&nbsp;&nbsp;{content}</span>
      </div>
    </div>
  );
};
