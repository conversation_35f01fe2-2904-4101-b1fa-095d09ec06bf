import { ConversationList } from "@tencentcloud/chat-uikit-react";
import { ImItem } from "./components/im-item";
import { Image, Toast } from "antd-mobile";
import noRecord from "@/assets/images/no-record.png";
import { PageWrapper } from "@/components/page-wrapper";
import { NavigationBar } from "@/components/navigation-bar";
import { useSearchParams } from "react-router-dom";
import { ShareTargetType } from "@/utils/enums";
import { useMutation } from "@tanstack/react-query";
import { confirmDialog } from "@/components/work/confirm-dialog";
import { IConversationModel } from "@tencentcloud/chat-uikit-engine";
import { shareFriend } from "@/lib/api/user";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

export default function ConversationSelect() {
  const [searchParams] = useSearchParams();
  const title = searchParams.get("title");
  const type = searchParams.get("type");
  const id = searchParams.get("id") || "";

  const { mutateAsync } = useMutation({
    mutationFn: shareFriend,
  });

  const { navigateBack } = useNavigateRoute();

  const handleClick = async (conversation: IConversationModel) => {
    const result = await confirmDialog.show(
      `将该内容分享给:${conversation.userProfile?.nick}`,
      "是否确认分享"
    );

    if (result) {
      Toast.show({
        icon: "loading",
        content: "请稍后...",
        duration: 0,
      });

      const { ok } = await mutateAsync({
        targetType: Number(type) as ShareTargetType,
        id,
        cid: conversation.conversationID,
        uid: conversation.userProfile?.userID,
      });

      if (ok) {
        Toast.show({
          content: "分享成功!",
        });

        navigateBack();
      }
    }
  };

  const noRecordRender = (
    <div className="flex flex-col gap-[15px] items-center justify-center mt-[50px]">
      <Image
        src={noRecord}
        fit="cover"
        width={`${150 / 16}rem`}
        height="auto"
        placeholder={null}
      />
      <span>没有会话消息~</span>
    </div>
  );

  return (
    <PageWrapper>
      <NavigationBar canBack title={title || ""} />
      <ConversationList
        className="flex-1 h-0 px-[15px] overflow-y-auto"
        enableSearch={false}
        enableCreate={false}
        Preview={(conversation) => (
          <ImItem {...conversation} onSelect={handleClick} />
        )}
        PlaceholderEmptyList={noRecordRender}
        PlaceholderLoading={noRecordRender}
        PlaceholderLoadError={noRecordRender}
      />
    </PageWrapper>
  );
}
