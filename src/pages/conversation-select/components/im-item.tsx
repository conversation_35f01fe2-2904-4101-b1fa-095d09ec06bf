import { Avatar } from "@/components/avatar";
import { IConversationPreviewUIProps } from "@tencentcloud/chat-uikit-react";
import { getTimeStringForChat } from "@/utils/format-time";
import { lastMessageBuild } from "@/utils/last-message-build";
import { IConversationModel } from "@tencentcloud/chat-uikit-engine";

interface Props extends IConversationPreviewUIProps {
  onSelect: (conversation: IConversationModel) => void;
}

export const ImItem = (props: Props) => {
  const { conversation, onSelect } = props;

  const userProfile = conversation?.userProfile;

  const lastMessage = conversation?.lastMessage;

  const handleClick = () => {
    onSelect(conversation);
  };

  return (
    <div
      className="large-badge flex gap-2 items-center py-3"
      onClick={handleClick}
    >
      <Avatar src={userProfile.avatar} size={50} />
      <div className="w-full">
        <div className="flex justify-between items-center">
          <span className="text-base flex-1 line-clamp-1">
            {userProfile.nick}
          </span>
          <span className="text-xs text-hint-color">
            {getTimeStringForChat(Number(lastMessage?.lastTime))}
          </span>
        </div>
        <div className="text-sm text-hint-color line-clamp-1">
          {lastMessageBuild(conversation.lastMessage as any)}
        </div>
      </div>
    </div>
  );
};
