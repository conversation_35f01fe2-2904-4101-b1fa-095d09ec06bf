import { messageSettingList } from "@/lib/api/user";
import { queryClient } from "@/provider/query-client";
import { defer, LoaderFunction } from "react-router-dom";

export const loader: LoaderFunction = async () => {
  return defer({
    data: queryClient
      .fetchQuery({
        queryKey: ["message-setting-list"],
        queryFn: messageSettingList,
      })
      .catch((err) => err),
  });
};
