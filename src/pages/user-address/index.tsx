import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import {
  DeleteIcon,
  EditIcon,
  RadioOffIcon,
  RadioOnIcon,
} from "@/components/svg-icon";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import {
  deleteUserAddress,
  getUserOrderAddress,
  setUserAddressDefault,
  updateUserOrderAddress,
} from "@/lib/api/user";
import { queryClient } from "@/provider/query-client";
import { PageParams } from "@/type";
import { Button } from "@/ui/button";
import { useMutation } from "@tanstack/react-query";
import { Divider, Toast } from "antd-mobile";
import classNames from "classnames";
import { useSearchParams } from "react-router-dom";

export default function UserAddress() {
  const [searchParams] = useSearchParams();
  const orderId = searchParams.get("orderId");

  const queryKey = ["user-address-list"];
  const queryFn = (params: PageParams) => getUserOrderAddress(params);

  const { navigateRoute, navigateBack } = useNavigateRoute();

  const { mutateAsync: setDefaultAddress } = useMutation({
    mutationFn: setUserAddressDefault,
  });

  const { mutateAsync: deleteAddress } = useMutation({
    mutationFn: deleteUserAddress,
  });

  const handleAdd = () => {
    navigateRoute("/user-address-edit");
  };

  const handleEdit = (id: string) => {
    navigateRoute("/user-address-edit", { id });
  };

  const handleSetDefault = async (id: string) => {
    Toast.show({
      content: "请稍后...",
      icon: "loading",
      duration: 0,
    });

    const { ok } = await setDefaultAddress(id);

    if (ok) {
      queryClient.refetchQueries({ queryKey });
    }
  };

  const handleDelete = async (id: string) => {
    Toast.show({
      content: "请稍后...",
      icon: "loading",
      duration: 0,
    });

    const { ok } = await deleteAddress(id);

    if (ok) {
      queryClient.refetchQueries({ queryKey });
    }
  };

  const { mutateAsync: updateAddress } = useMutation({
    mutationFn: updateUserOrderAddress,
  });

  const handleUpdateAddress = async (addressId: string) => {
    Toast.show({
      content: "请稍后...",
      icon: "loading",
      duration: 0,
    });

    const { ok } = await updateAddress({ id: orderId!, addressId });

    if (ok) {
      queryClient.invalidateQueries({
        queryKey: ["user-order-create", orderId],
      });
      navigateBack();
    }
  };

  return (
    <PageWrapper className="bg-scaffold-background relative">
      <NavigationBar canBack title="地址管理" />
      <PageMain>
        <div className="p-[15px] mb-[90px]">
          <ScrollLoadData queryKey={queryKey} queryFn={queryFn} size={999}>
            {(data) => (
              <div className="flex flex-col gap-[15px]">
                {data.map((item) => (
                  <div
                    key={item.id}
                    className="bg-app-bar-background rounded-[10px] p-[15px] flex flex-col"
                    onClick={() => handleUpdateAddress(item.id!)}
                  >
                    <div className="flex flex-col">
                      <div className="w-full flex">
                        <span className="text-base flex-1 w-0 line-clamp-1">
                          收货人：{item.name}
                        </span>
                        <span>{item.phone}</span>
                      </div>
                      <div className="h-[5px]" />
                      <div>
                        {item.province}
                        {item.city}
                        {item.region}
                        {item.address}
                      </div>
                    </div>
                    <Divider className="my-[10px] border-divider" />
                    <div className="flex justify-between">
                      <div
                        className="flex gap-[5px] items-center"
                        onClick={() => handleSetDefault(item.id!)}
                      >
                        {item.isDefault ? (
                          <RadioOnIcon size={15} className="text-[#FF005c]" />
                        ) : (
                          <RadioOffIcon size={15} className="text-hint-color" />
                        )}
                        <span
                          className={classNames(
                            item.isDefault
                              ? "text-[#FF005c]"
                              : "text-hint-color"
                          )}
                        >
                          默认地址
                        </span>
                      </div>
                      <div className="flex gap-[10px]">
                        <div
                          className="flex items-center gap-[2px]"
                          onClick={() => handleEdit(item.id!)}
                        >
                          <EditIcon size={15} className="text-hint-color" />
                          <span className="text-hint-color">修改</span>
                        </div>
                        <div
                          className="flex items-center gap-[2px]"
                          onClick={() => handleDelete(item.id!)}
                        >
                          <DeleteIcon size={15} className="text-hint-color" />
                          <span className="text-hint-color">删除</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollLoadData>
        </div>
      </PageMain>
      <div className="absolute left-0 right-0 bottom-[50px] px-[15px]">
        <Button variant="primary" className="w-full" onClick={handleAdd}>
          新增收货地址
        </Button>
      </div>
    </PageWrapper>
  );
}
