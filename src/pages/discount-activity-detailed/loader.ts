import { getDiscountActivity } from "@/lib/api/system";
import { queryClient } from "@/provider/query-client";
import { parseUrl } from "@/utils/parse-url";
import { defer, LoaderFunction, LoaderFunctionArgs } from "react-router-dom";

export const loader: LoaderFunction = ({ request }: LoaderFunctionArgs) => {
  const { searchParams } = parseUrl(request.url);

  const sign = searchParams.get("sign");

  return defer({
    data: queryClient
      .fetchQuery({
        queryKey: ["discount-activity-detailed", sign],
        queryFn: () => getDiscountActivity(sign || ""),
      })
      .catch((err) => err),
  });
};
