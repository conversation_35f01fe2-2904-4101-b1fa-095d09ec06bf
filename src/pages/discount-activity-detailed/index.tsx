import { discountActivityBackground } from "@/components/image-list";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { usePay } from "@/hooks/use-pay";
import { useScrollOpacity } from "@/hooks/user-scroll-opacity";
import { DiscountActivityResult } from "@/type/discount-activity-result";
import { Button } from "@/ui/button";
import { PayBusinessType } from "@/utils/enums";
import { Image } from "antd-mobile";
import { useAsyncValue } from "react-router-dom";

export default function DiscountActivityDetailed() {
  const { data } = useAsyncValue() as { data: DiscountActivityResult };

  const { opacity, scrollRef } = useScrollOpacity(200);

  const { showPay } = usePay({
    type: PayBusinessType.DiscountActivity,
    params: {
      id: data.id,
    },
  });

  return (
    <PageWrapper className="relative z-10">
      <NavigationBar
        canBack
        className="bg-transparent text-transparent"
        style={
          opacity > 0.5
            ? {
                background: `hsla(var(--app-bar-background)/${opacity})`,
                color: `hsla(var(--foreground)/${opacity})`,
              }
            : {}
        }
      />
      <PageMain scrollRef={scrollRef}>
        <div className="mt-[360px] pb-[70px] flex flex-col gap-[10px]">
          <div className="px-[15px] flex flex-col gap-[10px]">
            {data.items?.map((item) => (
              <div className="bg-white px-[15px] py-[10px] rounded-[5px] text-[#794F33] text-sm">
                {item}
              </div>
            ))}
          </div>
          <div className="text-[#794F33] text-sm text-center">{data.note}</div>
        </div>
      </PageMain>

      <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-[-1]">
        <Image
          src={discountActivityBackground}
          width="100%"
          height="auto"
          placeholder={null}
          fallback={null}
          className="object-cover relative top-[-30px]"
        />
      </div>
      <div className="fixed bottom-[15px] left-0 right-0 px-[15px]">
        <Button
          disabled={data.disabled}
          variant="primary"
          className="w-full"
          onClick={showPay}
        >
          {data.confirmText}
        </Button>
      </div>
    </PageWrapper>
  );
}
