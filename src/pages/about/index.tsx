import { CommonIcon } from "@/components/common-icon";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { useConfigList } from "@/hooks/use-config-list";

export default function AboutPage() {
  const { getSystemConfig } = useConfigList();

  return (
    <PageWrapper>
      <NavigationBar canBack title="关于我们" />
      <PageMain>
        <div className="flex flex-col items-center justify-center h-full">
          <div className="flex items-center gap-2">
            <CommonIcon name="logo" w={50} h={50} />
            <span className="font-bold text-2xl">
              {getSystemConfig("APP_SHOW_NAME")}
            </span>
          </div>
          <div className="pt-5">
            Copyright(C) {getSystemConfig("APP_SHOW_NAME")}版权所有
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
