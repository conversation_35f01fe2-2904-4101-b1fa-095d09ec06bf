import { CommonIcon } from "@/components/common-icon";
import { userLevelHomeBackground } from "@/components/image-list";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { useUserLevelConfig } from "@/hooks/use-user-level-config";
import { UserLevelResult } from "@/type/user-level-result";
import { ScrollArea, ScrollBar } from "@/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/ui/table";
import { UserLevelType, userLevelTypeConfig } from "@/utils/enums";
import { Image, ProgressBar } from "antd-mobile";
import { useMemo, useState } from "react";
import { useAsyncValue } from "react-router-dom";

const levelType = [
  UserLevelType.integral,
  UserLevelType.charm,
  UserLevelType.heroism,
];

export default function UserLevel() {
  const [selected, setSelected] = useState(levelType[0]);

  const { getType } = useUserLevelConfig();

  const { data } = useAsyncValue() as { data: UserLevelResult[] };

  const { navigateRoute } = useNavigateRoute();

  const currentValue = useMemo(() => {
    return data.find((item) => item.type === selected);
  }, [data, selected]);

  const curentPercent = useMemo(() => {
    if (!currentValue) {
      return 0;
    }

    return (
      (currentValue.currentLevelValue! / currentValue.nextLevelValue!) * 100
    );
  }, [currentValue]);

  const handleClick = () => {
    navigateRoute("/user-level-summary", {
      type: selected,
    });
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="我的等级" />
      <div className="p-[15px]">
        <ScrollArea>
          <div className="h-[89px] flex gap-[5px]">
            {levelType.map((type) => (
              <div
                key={type}
                className="relative"
                onClick={() => setSelected(type)}
              >
                <CommonIcon
                  name={
                    selected === type
                      ? `${UserLevelType[type]}Selected`
                      : UserLevelType[type]
                  }
                  fit="contain"
                  w={selected === type ? 165 : 105}
                  h={selected === type ? 89 : 80}
                />
                <div className="absolute left-[18px] top-[15px]">
                  <span className="text-white text-base">
                    {userLevelTypeConfig[type].label}
                  </span>
                </div>
                <div className="absolute left-[18px] bottom-[15px]">
                  <span className="text-white text-base">
                    {data.find((item) => item.type === type)?.currentLevel ?? 0}
                    级
                  </span>
                </div>
              </div>
            ))}
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>
      <div className="px-[15px]">
        <div className="w-full h-[115px] relative">
          <Image
            src={userLevelHomeBackground}
            width="100%"
            height="100%"
            fit="cover"
            placeholder={null}
            fallback={null}
          />
          <div className="absolute top-0 left-0 w-full">
            <div className="pt-3 flex flex-col items-center">
              <CommonIcon name="level" w={93} h={45} />
              <div className="pt-[5px] w-[275px] flex flex-col gap-[10px]">
                <ProgressBar
                  percent={curentPercent}
                  style={{
                    "--track-width": "11px",
                    "--fill-color": "#1FB2F5",
                    "--track-color": "#EEEEEE",
                  }}
                />
                <div className="flex justify-between text-xs text-white">
                  <span>下一等级</span>
                  <span>
                    {currentValue?.currentLevelValue ?? 0}/
                    {currentValue?.nextLevelValue ?? 0}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="absolute right-0 top-[15px]">
            <span
              className="text-[13px] text-[#6712FF] dark:text-white bg-[#6712FF]/10 px-[10px] py-[2px] rounded-l-full"
              onClick={handleClick}
            >
              查看明细
            </span>
          </div>
        </div>
      </div>
      <PageMain className="pt-[15px]">
        <div className="px-[15px] border-collapse border border-scaffold-background">
          <Table className="w-full text-[15px]">
            <TableHeader className="bg-scaffold-background/50">
              <TableRow>
                <TableHead className="text-center border border-scaffold-background">
                  <span className="text-[#6712FF] dark:text-white py-[15px]">
                    等级
                  </span>
                </TableHead>
                <TableHead className="text-center border border-scaffold-background">
                  <span className="text-[#6712FF] dark:text-white py-[15px]">
                    名称
                  </span>
                </TableHead>
                <TableHead className="text-center border border-scaffold-background">
                  <span className="text-[#6712FF] dark:text-white py-[15px]">
                    所需经验
                  </span>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {getType(selected)?.map((item) => (
                <TableRow
                  key={item.level}
                  className={
                    item.level === currentValue?.currentLevel
                      ? "bg-[#6712FF]/10"
                      : ""
                  }
                >
                  <TableCell className="text-center border border-scaffold-background">
                    <span className="py-3">{item.level}</span>
                  </TableCell>
                  <TableCell className="text-center border border-scaffold-background">
                    <span className="py-3">{item.name}</span>
                  </TableCell>
                  <TableCell className="text-center border border-scaffold-background">
                    <span className="py-3">{item.levelValue}</span>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
