import { ScrollLoadData } from "@/components/scroll-load-data";
import { UserInfo } from "@/components/work/user-info";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { LiveRoomMembers } from "@/lib/api/live";
import { PageParams } from "@/type";
import { ScrollArea } from "@/ui/scroll-area";
import { Popup } from "antd-mobile";
import { PropsWithChildren, useState } from "react";

const colors = ["#FFBD26", "#D5D5D5", "FF6A10"];

interface Props {
  id: string;
}

export const LiveLayoutMembersModal = (props: PropsWithChildren<Props>) => {
  const { id, children } = props;

  const [open, setOpen] = useState(false);

  const queryKey = ["live-room-members", id];
  const queryFn = (params: PageParams) => LiveRoomMembers({ id }, params);

  const { handleRefresh } = usePageListRefresh(queryKey);

  const onOpen = () => {
    handleRefresh();
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  return (
    <>
      <div onClick={onOpen}>{children}</div>
      <Popup
        destroyOnClose
        visible={open}
        bodyClassName="rounded-t-[10px]"
        onMaskClick={onClose}
        onClose={onClose}
      >
        <div className="bg-app-bar-background p-[15px] flex flex-col gap-[15px] h-[350px]">
          <div className="text-center text-base">在线观众</div>
          <ScrollArea className="flex-1">
            <ScrollLoadData queryFn={queryFn} queryKey={queryKey}>
              {(data) => (
                <div className="flex flex-col gap-[12px]">
                  {data.map((item, index) => (
                    <div
                      key={item.user?.id}
                      className="flex items-center gap-[5px]"
                    >
                      <div
                        className="w-[40px] text-[15px] flex justify-center"
                        style={{
                          color: colors[index] || undefined,
                        }}
                      >
                        {index + 1}
                      </div>
                      <UserInfo user={item.user!} size={47} />
                      {item.contribute ? <span>{item.contribute}</span> : null}
                    </div>
                  ))}
                </div>
              )}
            </ScrollLoadData>
          </ScrollArea>
        </div>
      </Popup>
    </>
  );
};
