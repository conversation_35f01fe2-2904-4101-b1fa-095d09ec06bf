import { NavigationBar } from "@/components/navigation-bar";
import { PageWrapper } from "@/components/page-wrapper";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { Button } from "@/ui/button";
import { Image, SafeArea } from "antd-mobile";

interface Props {
  background: string;
  note: string;
}

export const LiveClosed = (props: Props) => {
  const { background, note } = props;

  const { navigateBack } = useNavigateRoute();

  return (
    <PageWrapper className="relative">
      <NavigationBar canBack className="bg-transparent" />
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="w-full h-full">
          <Image width="100%" height="100%" src={background} fit="cover" />
        </div>
        <div className="absolute top-0 left-0 w-full h-full bg-black/40 blur-[10px]" />
        <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
          <span className="text-[30px]">{note}</span>
        </div>
        <div className="absolute bottom-0 left-0 w-full py-[25px] px-[15px]">
          <Button
            variant="primary"
            className="h-[50px] w-full"
            onClick={() => navigateBack()}
          >
            返回主页
          </Button>
          <SafeArea position="bottom" />
        </div>
      </div>
    </PageWrapper>
  );
};
