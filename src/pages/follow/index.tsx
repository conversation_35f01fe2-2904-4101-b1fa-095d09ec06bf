import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { UserInfo } from "@/components/work/user-info";
import { fullUserInfoKey, useFullUserInfo } from "@/hooks/use-full-user-info";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { followList, unfollow } from "@/lib/api/user";
import { queryClient } from "@/provider/query-client";
import { PageParams } from "@/type";
import { Button } from "@/ui/button";
import { useMutation } from "@tanstack/react-query";
import { Divider, Toast } from "antd-mobile";
import { useSearchParams } from "react-router-dom";
import { Fragment } from "react/jsx-runtime";

export default function FollowPage() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id") ?? undefined;

  const queryKey = ["follow-page", id];

  const { data: selfInfo } = useFullUserInfo();

  const queryFn = (params: PageParams) => {
    return followList({ userId: id }, params);
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  const unfollowMutation = useMutation({
    mutationFn: unfollow,
  });

  const handleUnfollow = async (id: string) => {
    Toast.show({
      icon: "loading",
      content: "加载中…",
      duration: 0,
    });
    const { ok } = await unfollowMutation.mutateAsync(id);

    if (ok) {
      // 刷新列表
      handleRefresh();
      queryClient.refetchQueries({ queryKey: fullUserInfoKey });
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="关注列表" />
      <PageMain
        onRefresh={handleRefresh}
        className="bg-app-bar-background py-[15px]"
      >
        <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="flex flex-col">
              {data.map((userInfo, index) => (
                <Fragment key={userInfo.id}>
                  <div className="px-[15px] flex gap-2">
                    <div className="flex-1">
                      <UserInfo user={userInfo} size={50} />
                    </div>
                    {id === undefined || id === selfInfo?.id ? (
                      <Button
                        variant="secondary"
                        className="h-[40px] w-[90px]"
                        onClick={() => handleUnfollow(userInfo.id!)}
                      >
                        取消关注
                      </Button>
                    ) : null}
                  </div>
                  {index !== data.length - 1 ? (
                    <Divider className="border-divider" />
                  ) : null}
                </Fragment>
              ))}
            </div>
          )}
        </ScrollLoadData>
      </PageMain>
    </PageWrapper>
  );
}
