import { ScrollLoadData } from "@/components/scroll-load-data";
import { getLayoutPageList } from "@/lib/api/layout";
import { PageParams } from "@/type";
import { BaseUrlConfig } from "@/type/layout-config";
import { Fragment, useEffect, useState } from "react";
import { PullToRefresh } from "@/components/pull-to-refresh";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { ScrollArea } from "@/ui/scroll-area";
import { UserOrderDetailedResult } from "@/type/user-order-detailed-result";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { UserOrderItem } from "@/components/work/user-order-item";
import { Divider } from "antd-mobile";

interface Props {
  isActive: boolean;
  index: number;
  data: BaseUrlConfig;
}

export const TabContent = (props: Props) => {
  const { isActive, index, data } = props;

  const [isInit, setIsInit] = useState(false);

  const { data: userInfo } = useFullUserInfo();

  const queryKey = [data.url, index, data.params];
  const queryFn = (params: PageParams) => {
    return getLayoutPageList<UserOrderDetailedResult[]>({
      url: data.url,
      params,
      data: { ...data.params, merchantId: userInfo?.id },
    });
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  useEffect(() => {
    if (isActive) {
      setIsInit(true);
    }
  }, [isActive]);

  if (!isInit) {
    return null;
  }

  return (
    <ScrollArea className={isActive ? "h-full" : "hidden"}>
      <PullToRefresh onRefresh={handleRefresh}>
        <ScrollLoadData size={10} queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="flex flex-col w-full pt-[15px]">
              {data.map((item, index) => (
                <Fragment key={item.id}>
                  <UserOrderItem
                    key={item.id}
                    data={item}
                    merchant
                    onRefresh={handleRefresh}
                  />
                  {index !== data.length - 1 ? (
                    <Divider className="border-divider my-[10px]" />
                  ) : null}
                </Fragment>
              ))}
            </div>
          )}
        </ScrollLoadData>
      </PullToRefresh>
    </ScrollArea>
  );
};
