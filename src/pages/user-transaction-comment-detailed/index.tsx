import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { CommentInput, FinishType } from "@/components/work/comment-input";
import { CommentItem } from "@/components/work/comment-item";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { useUploadQueueManager } from "@/hooks/use-upload-queue-manager";
import {
  userTransactionCommentPageChildren,
  userTransactionCommentPublishCheck,
  userTransactionCommentPublishUrl,
} from "@/lib/api/transaction";
import { PageParams } from "@/type";
import { CommonCommentResult } from "@/type/common-comment-result";
import { getRandomParams } from "@/utils/common";
import { useMutation } from "@tanstack/react-query";
import { Divider, Toast } from "antd-mobile";
import { ReactNode, useState } from "react";
import { useLocation, useSearchParams } from "react-router-dom";
import { Fragment } from "react/jsx-runtime";

export default function MindWallCommentDetailed() {
  const [searchParams] = useSearchParams();
  const targetId = searchParams.get("targetId") || "";
  const location = useLocation();

  const [quote, setQuote] = useState<
    { id: string; content: ReactNode } | undefined
  >();

  const parentComment = location.state as CommonCommentResult;

  const queryKey = ["user-transaction-comment-detailed", parentComment.id];
  const queryFn = (params: PageParams) =>
    userTransactionCommentPageChildren(
      { targetId: targetId, topId: parentComment.id! },
      params
    );

  const { handleRefresh } = usePageListRefresh(queryKey);

  const { addToQueue } = useUploadQueueManager();

  const mindWallCommentPublishCheckMutations = useMutation({
    mutationFn: userTransactionCommentPublishCheck,
  });

  const handleComment = async (data: FinishType) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await mindWallCommentPublishCheckMutations.mutateAsync({
      targetId,
      content: data.text,
      ats: data.ats,
      voice: data.voice ? getRandomParams() : undefined,
      resources: data.resource
        ? data.resource.map(() => getRandomParams())
        : undefined,
      topId: parentComment?.id,
      parentId: quote?.id,
    });

    if (ok) {
      addToQueue(
        {
          title: "交易评论",
          url: userTransactionCommentPublishUrl,
          params: {
            targetId,
            content: data.text,
            ats: data.ats,
            voice: data.voice,
            resources: data.resource,
            topId: parentComment?.id,
            parentId: quote?.id,
          },
        },
        () => {
          handleRefresh();
        }
      );
    }
  };

  const handleSetQuote = (quote: CommonCommentResult) => {
    setQuote({
      id: quote.id!,
      content: `回复：${quote?.user?.nickname} “${quote.voice ? "[语音]" : ""}${
        (quote.resources ?? []).some((r) => r.duration) ? "[视频]" : ""
      }${(quote.resources ?? []).some((r) => !r.duration) ? "[图片]" : ""}${
        quote.content
      }”`,
    });
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="评论列表" />
      <PageMain
        scrollClassName="p-[15px]"
        isEmpty={!parentComment}
        extra={
          <CommentInput
            showGift={false}
            quote={quote}
            onClearQuote={() => setQuote(undefined)}
            onFinish={handleComment}
          />
        }
        onRefresh={handleRefresh}
      >
        <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="flex flex-col">
              <div className="mb-[15px]">
                <CommentItem
                  type="user-transaction"
                  id={targetId}
                  data={parentComment}
                  showChildren={false}
                />
              </div>
              <div className="p-[10px] bg-[#F7F5FB] dark:bg-[#151B24] rounded-[5p]">
                {data.map((item, index) => (
                  <Fragment key={item.id}>
                    <CommentItem
                      key={item.id}
                      type="user-transaction"
                      id={targetId}
                      data={item}
                      onSetQuote={handleSetQuote}
                    />
                    {index !== data.length - 1 ? (
                      <Divider className="border-divider" />
                    ) : null}
                  </Fragment>
                ))}
              </div>
            </div>
          )}
        </ScrollLoadData>
      </PageMain>
    </PageWrapper>
  );
}
