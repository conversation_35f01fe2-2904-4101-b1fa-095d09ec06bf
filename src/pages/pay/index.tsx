import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { submitWebPay } from "@/lib/api/wallet";
import { SubmitWebPay } from "@/type/web-pay";
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useSearchParams } from "react-router-dom";

const submitForm = (data: SubmitWebPay) => {
  const { action, method, data: formData } = data;

  const form = document.createElement("form");

  form.setAttribute("target", "_self");
  form.setAttribute("action", action);
  form.setAttribute("method", method);

  Object.entries(formData).forEach(([key, value]) => {
    const input = document.createElement("input");

    input.setAttribute("name", key);
    input.setAttribute("value", value);

    form.appendChild(input);
  });

  document.body.appendChild(form);
  form.style.display = "none";
  form.submit();
};

export default function PayPage() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id") || undefined;
  const code = searchParams.get("code") || undefined;

  const { data } = useQuery({
    queryKey: ["web-pay", id, code],
    queryFn: () => submitWebPay({ orderNumber: id!, code: code! }),
  });

  // 如果填写了code信息，则直接提交支付
  useEffect(() => {
    if (id && code && data?.data?.data) {
      submitForm(data!.data);
    }
  }, [id, code, data]);

  return (
    <PageWrapper>
      <NavigationBar title="订单支付" />
      <PageMain className="bg-[#F6F6F6] dark:bg-[#000510]" isEmpty={false}>
        <div className="w-full h-full flex flex-col gap-4 pt-4">
          <div style={{ padding: 20, textAlign: "center" }}>
            正在提交订单，请稍后...
          </div>
          {/*<div className="bg-app-bar-background px-4 py-3 flex flex-col gap-2">*/}
          {/*    {data?.infos.map((info) => (*/}
          {/*        <div key={info.label} className="flex">*/}
          {/*            <span className="text-right">{info.label}：</span>*/}
          {/*            <span>{info.value}</span>*/}
          {/*        </div>*/}
          {/*    ))}*/}
          {/*</div>*/}
          {/*<div className="bg-app-bar-background px-4 py-3 flex flex-col">*/}
          {/*    <span className="text-[#666] mb-3">请选择支付方式</span>*/}
          {/*    <Radio.Group value={payMethod} onChange={handleChange}>*/}
          {/*        <Space direction="vertical">*/}
          {/*            {data?.methods.map((method) => (*/}
          {/*                <Radio*/}
          {/*                    key={method.code}*/}
          {/*                    style={{*/}
          {/*                        "--icon-size": "18px",*/}
          {/*                        "--font-size": "14px",*/}
          {/*                        "--gap": "6px",*/}
          {/*                    }}*/}
          {/*                    value={method.code}*/}
          {/*                >*/}
          {/*                    {method.name}*/}
          {/*                </Radio>*/}
          {/*            ))}*/}
          {/*        </Space>*/}
          {/*    </Radio.Group>*/}
          {/*</div>*/}
          {/*<div className="flex mx-4 mt-[20%]">*/}
          {/*    <Button variant="primary" className="w-full" onClick={() => handlePay(id, payMethod)}>*/}
          {/*        确认支付*/}
          {/*    </Button>*/}
          {/*</div>*/}
        </div>
      </PageMain>
    </PageWrapper>
  );
}
