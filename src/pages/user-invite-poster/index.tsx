import { DefaultLoadImage } from "@/components/default-load-image";
import { userInvitePosterBackgroundOld } from "@/components/image-list";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { useConfigList } from "@/hooks/use-config-list";
import { UserInvitePosterResult } from "@/type/user-invite-poster-result";
import { Button } from "@/ui/button";
import { downloadFile } from "@/utils/download-file";
import { Image, Toast } from "antd-mobile";
import { useAsyncValue } from "react-router-dom";

export default function UserInvitePoster() {
  const { data } = useAsyncValue() as { data: UserInvitePosterResult };

  const { getSystemConfig } = useConfigList();

  const clipboardUrl = () => {
    navigator.clipboard.writeText(data.url ?? "").then(() => {
      Toast.show({
        content: "链接复制成功",
      });
    });
  };

  const handleSavePoster = async () => {
    Toast.show({
      icon: "loading",
      content: "保存中...",
      duration: 0,
    });
    await downloadFile(data.qrcode!, "邀请海报");
    Toast.clear();
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="分享海报" />
      <PageMain needScroll={false} className="relative z-10">
        <div className="w-full h-full flex justify-center items-center">
          <div className="mt-[50px] flex flex-col items-center">
            <span className="text-[20px] text-[#764BF8]">
              {data.inviteCode}
            </span>
            <span className="text-[13px] text-black">我的邀请码</span>
            <div className="w-[150px] h-[150px] pt-[10px]">
              <Image
                src={data.qrcode}
                width="100%"
                height="100%"
                placeholder={<DefaultLoadImage />}
                fallback={<DefaultLoadImage />}
                className="object-cover"
              />
            </div>
            <span className="text-[13px] text-black">
              扫描二维码加入{getSystemConfig("APP_SHOW_NAME")}
            </span>
          </div>
        </div>
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-[-1]">
          <Image
            src={userInvitePosterBackgroundOld}
            width="100%"
            height="100%"
            placeholder={null}
            fallback={null}
            className="object-cover"
          />
        </div>
        <div className="absolute bottom-0 left-0 w-full">
          <div className="p-[15px] pb-[50px] flex gap-[15px]">
            <Button
              variant="primary"
              size="lg"
              className="w-full"
              onClick={handleSavePoster}
            >
              保存专属海报
            </Button>
            <Button
              variant="primary"
              size="lg"
              className="w-full user-invite-poster-copy-btn"
              onClick={clipboardUrl}
            >
              复制分享链接
            </Button>
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
