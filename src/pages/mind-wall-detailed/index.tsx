import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import {
  mindWallDetailed,
  mindWallCommentPage,
  mindWallCommentPublishCheck,
  mindWallCommentPublishUrl,
} from "@/lib/api/mind-wall";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useSearchParams } from "react-router-dom";
import { MindWallItem } from "../home/<USER>/mind-wall/components/mind-wall-item";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { PageParams } from "@/type";
import { Fragment } from "react/jsx-runtime";
import { CommentItem } from "@/components/work/comment-item";
import { Divider, Toast } from "antd-mobile";
import { CommonCommentResult } from "@/type/common-comment-result";
import { useState } from "react";
import { ReactNode } from "react";
import { useUploadQueueManager } from "@/hooks/use-upload-queue-manager";
import { CommentInput, FinishType } from "@/components/work/comment-input";
import { getRandomParams } from "@/utils/common";
import { queryClient } from "@/provider/query-client";

export default function MindWallDetailedPage() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id");

  const { data: detail, isLoading } = useQuery({
    queryKey: ["mind-wall-detailed", id],
    queryFn: () => mindWallDetailed({ id: id! }),
  });

  const commentQueryKey = ["mind-wall-comment", id];
  const commentQueryFn = (params: PageParams) => {
    return mindWallCommentPage({ targetId: id! }, params);
  };

  const [quote, setQuote] = useState<
    { id: string; content: ReactNode } | undefined
  >();

  const dynamicCommentPublishCheckMutations = useMutation({
    mutationFn: mindWallCommentPublishCheck,
  });

  const { addToQueue } = useUploadQueueManager();

  const handleComment = async (data: FinishType) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await dynamicCommentPublishCheckMutations.mutateAsync({
      targetId: id!,
      content: data.text,
      ats: data.ats,
      voice: data.voice ? getRandomParams() : undefined,
      resources: data.resource
        ? data.resource.map(() => getRandomParams())
        : undefined,
      topId: quote?.id,
      parentId: quote?.id,
    });

    if (ok) {
      addToQueue(
        {
          title: "心意墙评论",
          url: mindWallCommentPublishUrl,
          params: {
            targetId: id!,
            content: data.text,
            ats: data.ats,
            voice: data.voice,
            resources: data.resource,
            topId: quote?.id,
            parentId: quote?.id,
          },
        },
        () => {
          queryClient.resetQueries({ queryKey: commentQueryKey });
        }
      );
    }
  };

  const handleSetQuote = (quote: CommonCommentResult) => {
    setQuote({
      id: quote.id!,
      content: `回复：${quote?.user?.nickname} “${quote.voice ? "[语音]" : ""}${
        (quote.resources ?? []).some((r) => r.duration) ? "[视频]" : ""
      }${(quote.resources ?? []).some((r) => !r.duration) ? "[图片]" : ""}${
        quote.content
      }”`,
    });
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="心意详情" />
      <PageMain
        isLoading={isLoading}
        isEmpty={!detail?.data}
        extra={
          <CommentInput
            quote={quote}
            onClearQuote={() => setQuote(undefined)}
            showGift={false}
            onFinish={handleComment}
          />
        }
      >
        <MindWallItem clickJump={false} data={detail?.data!} isRadius={false} />
        <div className="px-[15px]">
          <div className="font-medium text-base p-[15px]">
            全部评论({detail?.data?.commentNumber})
          </div>
          <ScrollLoadData queryKey={commentQueryKey} queryFn={commentQueryFn}>
            {(comments) => (
              <div className="flex flex-col pt-5">
                {comments.map((item, index) => (
                  <Fragment key={item.id}>
                    <CommentItem
                      type="mind-wall"
                      id={detail?.data?.id!}
                      data={item}
                      onSetQuote={handleSetQuote}
                    />
                    {index !== comments.length - 1 ? (
                      <Divider className="border-divider" />
                    ) : null}
                  </Fragment>
                ))}
              </div>
            )}
          </ScrollLoadData>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
