import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { MediaUpload } from "@/components/work/media-upload";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { useUploadQueueManager } from "@/hooks/use-upload-queue-manager";
import {
  userTransactionParticipateCheck,
  userTransactionParticipateUrl,
} from "@/lib/api/transaction";
import { Button } from "@/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/ui/form";
import { Input } from "@/ui/input";
import { Textarea } from "@/ui/textarea";
import { getRandomParams } from "@/utils/common";
import { useMutation } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import { useForm } from "react-hook-form";
import { useSearchParams } from "react-router-dom";

export default function UserTransactionParticipate() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id") || "";

  const form = useForm();

  const { mutateAsync } = useMutation({
    mutationFn: userTransactionParticipateCheck,
  });

  const { addToQueue } = useUploadQueueManager();
  const { navigateBack } = useNavigateRoute();

  const handleSubmit = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const data = form.getValues();

    const { cover, resources, ...rest } = data;

    const { ok } = await mutateAsync({
      id,
      cover: cover ? getRandomParams() : undefined,
      resources: resources ? resources.map(() => getRandomParams()) : undefined,
      ...rest,
    });

    if (ok) {
      addToQueue(
        {
          title: "交易发布",
          url: userTransactionParticipateUrl,
          params: {
            id,
            ...rest,
            cover: cover?.[0],
            resources,
          },
        },
        () => {
          navigateBack();
        }
      );
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="交易投标" />
      <PageMain>
        <div className="p-[15px]">
          <Form {...form}>
            <div className="flex flex-col gap-[20px]">
              <FormField
                control={form.control}
                name="cover"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>封面</FormLabel>
                      <FormControl>
                        <MediaUpload maxCount={1} {...field} />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="resources"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>资源</FormLabel>
                      <FormControl>
                        <MediaUpload maxCount={9} {...field} />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>描述内容</FormLabel>
                      <FormControl>
                        <Textarea
                          className="resize-none px-[15px] py-[10px] rounded-[10px]"
                          placeholder="请输入描述内容"
                          rows={5}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>价格</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="请输入价格"
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="freightPrice"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>运费</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="请输入运费"
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
            </div>
          </Form>
          <div className="my-[20px] px-[15px]">
            <Button variant="primary" className="w-full" onClick={handleSubmit}>
              立即发布
            </Button>
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
