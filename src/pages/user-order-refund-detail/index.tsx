import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { confirmDialog } from "@/components/work/confirm-dialog";
import { UserOrderMerchandiseItem } from "@/components/work/user-order-merchandise-item";
import { useCountDownTimer } from "@/hooks/use-count-down-timer";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { auditUserOrderRefund, getUserOrderRefundDetail } from "@/lib/api/user";
import { Button } from "@/ui/button";
import { Textarea } from "@/ui/textarea";
import {
  UserOrderRefundState,
  userOrderRefundStateConfig,
} from "@/utils/enums";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import dayjs from "dayjs";
import { useMemo, useState } from "react";
import { useSearchParams } from "react-router-dom";

export default function UserOrderRefundDetailPage() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id") ?? "";

  const [note, setNote] = useState("");

  const { data } = useQuery({
    queryKey: ["user-order-refund-detail", id],
    queryFn: () => getUserOrderRefundDetail(id),
  });

  const { data: userInfo } = useFullUserInfo();

  const isMerchant = userInfo?.id !== data?.data?.merchant?.id;

  const duration = useMemo(() => {
    if (data?.data?.state === UserOrderRefundState.Wait) {
      return dayjs(data?.data?.autoReasonDate).diff(dayjs(), "second");
    }
    return 0;
  }, [data?.data?.autoReasonDate, data?.data?.state]);

  const { seconds, isRunning } = useCountDownTimer(duration, true);

  const { navigateBack } = useNavigateRoute();

  const { mutateAsync } = useMutation({
    mutationFn: auditUserOrderRefund,
  });

  const handleAudit = async (state: boolean) => {
    const result = await confirmDialog.show(
      state
        ? "确认退款后，钱款将打回买家钱包，不可撤销"
        : "拒绝退款后，订单将继续流程",
      `是否${state ? "确认" : "拒绝"}退款?`
    );

    if (result) {
      Toast.show({
        icon: "loading",
        content: "请稍后...",
        duration: 0,
      });

      const { ok } = await mutateAsync({
        id,
        state,
        note,
      });

      if (ok) {
        Toast.show("操作成功");
        navigateBack();
      }
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="售后详情" />
      <PageMain>
        <div className="flex p-[15px] bg-scaffold-background">
          <span>售后状态: </span>
          <span className="text-[#FF9212]">
            {userOrderRefundStateConfig[data?.data?.state!] ?? "未知"}
          </span>
          {data?.data?.state === UserOrderRefundState.Wait &&
          !data.data.autoReasonDate ? (
            isRunning ? (
              <span className="text-[#FF9212]">
                ，系统将于
                {dayjs.duration(seconds, "seconds").format("HH:mm:ss")}
                后自动通过审核
              </span>
            ) : (
              <span className="text-[#FF9212] text-center">
                ，请刷新页面查看最新状态
              </span>
            )
          ) : null}
        </div>
        <div className="p-[15px]">
          <UserOrderMerchandiseItem data={data?.data?.order!} showUserInfo />
        </div>
        <div className="w-full h-[15px] bg-scaffold-background" />
        <div className="p-[15px] flex flex-col gap-[15px]">
          <div className="flex justify-between">
            <span>订单号</span>
            <span>{data?.data?.order?.orderNumber}</span>
          </div>
          <div className="flex justify-between">
            <span>申请原因</span>
            <span>{data?.data?.applyReason}</span>
          </div>
          <div className="flex justify-between">
            <span>商家金额</span>
            <span>{data?.data?.order?.totalPrice}</span>
          </div>
          <div className="flex justify-between">
            <span>支付金额</span>
            <span>{data?.data?.order?.actualPayAmount}</span>
          </div>
          <div className="flex justify-between">
            <span>申请金额</span>
            <span>{data?.data?.applyAmount}</span>
          </div>
          {data?.data?.auditReason ? (
            <div className="flex justify-between">
              <span>审核理由</span>
              <span>{data?.data?.auditReason}</span>
            </div>
          ) : null}
        </div>
        {isMerchant ? (
          <div className="w-full h-[15px] bg-scaffold-background" />
        ) : null}
        {isMerchant && data?.data?.state === UserOrderRefundState.Wait ? (
          <div className="p-[15px] flex flex-col gap-[15px]">
            <Textarea
              rows={3}
              className="resize-none px-[10px] py-[15px] rounded-[5px]"
              placeholder="您可以在此处输入您要对买家的售后的理由"
              value={note}
              onChange={(e) => setNote(e.target.value)}
            />
            <div className="flex justify-between">
              <Button
                variant="secondary"
                className="w-[73px] h-[29px] text-[13px]"
                onClick={() => handleAudit(false)}
              >
                拒绝退款
              </Button>
              <Button
                variant="primary"
                className="w-[73px] h-[29px] text-[13px]"
                onClick={() => handleAudit(true)}
              >
                同意退款
              </Button>
            </div>
          </div>
        ) : null}
      </PageMain>
    </PageWrapper>
  );
}
