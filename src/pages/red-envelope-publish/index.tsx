import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { useConfigList } from "@/hooks/use-config-list";
import { redEnvelopeCreate } from "@/lib/api/wallet";
import { LayoutConfigRedEnvelopePublish } from "@/type/layout-config";
import { Button } from "@/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/ui/form";
import { Input } from "@/ui/input";
import { userWalletType } from "@/utils/enums";
import { useMutation } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import { useForm } from "react-hook-form";
import { useAsyncValue, useNavigate, useSearchParams } from "react-router-dom";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

export default function RedEnvelopePublish() {
  const { data: config } = useAsyncValue() as {
    data: LayoutConfigRedEnvelopePublish;
  };

  const [searchParams] = useSearchParams();

  const targetId = searchParams.get("targetId");
  const c2c = Boolean(Number(searchParams.get("c2c")));

  const { getSystemConfig } = useConfigList();

  const minAmount = getSystemConfig("RED_ENVELOPE_MIN_AMOUNT");
  const maxAmount = getSystemConfig("RED_ENVELOPE_MAX_AMOUNT");

  const formSchema = z.object({
    number: z.coerce.number().optional(),
    amount: z.coerce.number().gte(Number(minAmount)).lte(Number(maxAmount)),
    content: z.string().optional(),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });

  const navigate = useNavigate();

  const mutation = useMutation({
    mutationFn: redEnvelopeCreate,
  });

  const onSubmit = async (data: any) => {
    Toast.show({
      content: "请稍后...",
      icon: "loading",
      duration: 0,
    });

    const { amount, content = config.defaultContent } = data;

    const { ok } = await mutation.mutateAsync({
      userId: c2c ? targetId! : undefined,
      groupId: c2c ? undefined : targetId!,
      data: {
        amount: Number(amount),
        content,
        ...(c2c ? config.c2c.defaultParams : config.group.defaultParams),
      },
    });

    if (ok) {
      navigate(-1);
    }
  };

  return (
    <PageWrapper className="bg-[#FFFAF5] dark:bg-[#000510]">
      <NavigationBar
        canBack
        title="发红包"
        className="!bg-[#FF595D] !text-white"
      />
      <PageMain>
        <div className="p-[15px]">
          <Form {...form}>
            <div className="flex flex-col gap-5 py-[15px]">
              {!c2c ? (
                <FormField
                  control={form.control}
                  name="number"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <div className="flex items-center p-[15px] bg-app-bar-background rounded-[5px]">
                          <FormLabel>数量</FormLabel>
                          <FormControl className="flex-1 ml-[10px] mr-[5px]">
                            <Input
                              placeholder="填写数量"
                              {...field}
                              className="h-auto border-none px-0"
                              type="number"
                            />
                          </FormControl>
                          <span>个</span>
                        </div>
                      </FormItem>
                    );
                  }}
                />
              ) : null}
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <div className="flex items-center p-[15px] bg-app-bar-background rounded-[5px]">
                        <FormLabel>金额</FormLabel>
                        <FormControl className="flex-1 ml-[10px] mr-[5px]">
                          <Input
                            placeholder={`单个金额范围: ${minAmount} - ${maxAmount}`}
                            {...field}
                            className="h-auto border-none px-0"
                            type="number"
                          />
                        </FormControl>
                        <span>{userWalletType.currency.label}</span>
                      </div>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <div className="flex items-center p-[15px] bg-app-bar-background rounded-[5px]">
                        <FormControl className="flex-1 ml-[10px] mr-[5px]">
                          <Input
                            placeholder={config.defaultContent}
                            {...field}
                            className="h-auto border-none px-0 mx-0"
                          />
                        </FormControl>
                      </div>
                    </FormItem>
                  );
                }}
              />
            </div>
          </Form>
        </div>
        <div className="pt-[50px] px-[15px]">
          <Button
            variant="primary"
            size="lg"
            className="w-full"
            onClick={form.handleSubmit(onSubmit)}
          >
            发红包
          </Button>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
