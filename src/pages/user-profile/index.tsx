import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { getUserProfile, updateUserProfile } from "@/lib/api/user";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/ui/form";
import { Input } from "@/ui/input";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { UploadAvatar } from "../login/pages/basic-info/components/upload-avatar";
import { layoutConfigOptions } from "@/utils/query-options";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { UserProfileLayout } from "@/type/user-profile-layout";
import { Select } from "@/components/select";
import {
  extentConfig,
  genderConfig,
  roleConfig,
  sexualConfig,
} from "@/utils/enums";
import { useEffect } from "react";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { Button } from "@/ui/button";
import { Toast } from "antd-mobile";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

export default function UserProfilePage() {
  const form = useForm();

  const { data: userInfo } = useFullUserInfo();

  const { data: config } = useQuery(
    layoutConfigOptions<UserProfileLayout>(LayoutConfigKey.UserProfile)
  );

  const { data: userProfile } = useQuery({
    queryKey: ["user-profile"],
    queryFn: () => getUserProfile(),
  });

  useEffect(() => {
    if (userProfile?.data) {
      form.reset({
        avatar: { url: userProfile.data.avatar },
        nickname: userProfile.data.nickname,
        role: userProfile.data.role,
        extent: userProfile.data.extent,
        sexualOrientation: userProfile.data.sexualOrientation,
        gender: userInfo?.gender,
        birthday: userProfile.data.birthday,
      });
    }
  }, [userProfile?.data, userInfo?.gender, form]);

  const { mutateAsync } = useMutation({
    mutationFn: updateUserProfile,
  });

  const { navigateBack } = useNavigateRoute();

  const handleSubmit = async () => {
    const values = form.getValues();

    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await mutateAsync({
      ...values,
      avatar: values.avatar?.id,
    });

    if (ok) {
      Toast.show({
        content: "个人资料修改成功!",
      });

      navigateBack();
    }
  };

  return (
    <PageWrapper className="bg-scaffold-background">
      <NavigationBar canBack title="个人资料" />
      <PageMain>
        <div className="p-[15px]">
          <Form {...form}>
            <div className="flex flex-col gap-2">
              {config?.data?.avatar ? (
                <FormField
                  control={form.control}
                  name="avatar"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormControl>
                          <div className="flex flex-col items-center gap-2">
                            <UploadAvatar {...field} />
                            <span className="text-[#999]">点击更换头像</span>
                          </div>
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
              ) : null}
              <div className="flex gap-2">
                {config?.data?.nickname ? (
                  <FormField
                    control={form.control}
                    name="nickname"
                    render={({ field }) => {
                      return (
                        <FormItem className="flex-1">
                          <FormLabel>昵称</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="请输入昵称"
                              {...field}
                              autoComplete="off"
                            />
                          </FormControl>
                        </FormItem>
                      );
                    }}
                  />
                ) : null}
                {config?.data?.gender ? (
                  <FormField
                    control={form.control}
                    name="gender"
                    render={({ field }) => {
                      return (
                        <FormItem className="flex-1">
                          <FormLabel>性别(不可修改)</FormLabel>
                          <FormControl>
                            <Select
                              placeholder="请输入性别"
                              disabled
                              options={genderConfig}
                              {...field}
                            />
                          </FormControl>
                        </FormItem>
                      );
                    }}
                  />
                ) : null}
              </div>
              <div className="flex gap-2">
                {config?.data?.sexualOrientation ? (
                  <FormField
                    control={form.control}
                    name="sexualOrientation"
                    render={({ field }) => {
                      return (
                        <FormItem className="flex-1">
                          <FormLabel>性取向</FormLabel>
                          <FormControl>
                            <Select
                              placeholder="请选择性取向"
                              options={sexualConfig}
                              {...field}
                            />
                          </FormControl>
                        </FormItem>
                      );
                    }}
                  />
                ) : null}
                {config?.data?.role ? (
                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => {
                      return (
                        <FormItem className="flex-1">
                          <FormLabel>角色</FormLabel>
                          <FormControl>
                            <Select
                              placeholder="请选择角色"
                              options={roleConfig}
                              {...field}
                            />
                          </FormControl>
                        </FormItem>
                      );
                    }}
                  />
                ) : null}
              </div>
              <div className="flex gap-2">
                {config?.data?.extent ? (
                  <FormField
                    control={form.control}
                    name="extent"
                    render={({ field }) => {
                      return (
                        <FormItem className="flex-1">
                          <FormLabel>程度</FormLabel>
                          <FormControl>
                            <Select
                              placeholder="请选择程度"
                              options={extentConfig}
                              {...field}
                            />
                          </FormControl>
                        </FormItem>
                      );
                    }}
                  />
                ) : null}
                {config?.data?.birthday ? (
                  <FormField
                    control={form.control}
                    name="birthday"
                    render={({ field }) => {
                      return (
                        <FormItem className="flex-1">
                          <FormLabel>生日(不可修改)</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="请选择生日时间"
                              disabled
                              {...field}
                            />
                          </FormControl>
                        </FormItem>
                      );
                    }}
                  />
                ) : null}
              </div>
            </div>
          </Form>
          <div className="pt-[35px] pb-[10px]">
            <Button variant="primary" className="w-full" onClick={handleSubmit}>
              保存
            </Button>
          </div>
          {userProfile?.data?.tips ? (
            <div className="text-center text-[13px]">
              {userProfile.data.tips}
            </div>
          ) : null}
        </div>
      </PageMain>
    </PageWrapper>
  );
}
