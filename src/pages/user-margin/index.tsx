import { marginBackground } from "@/components/image-list";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { useConfigList } from "@/hooks/use-config-list";
import { useScrollOpacity } from "@/hooks/user-scroll-opacity";
import { Button } from "@/ui/button";
import { Image } from "antd-mobile";
import { useMemo } from "react";

export default function UserMarginPage() {
  const { getSystemConfig } = useConfigList();

  const noteList = useMemo(() => {
    const list = getSystemConfig("USER_MARGIN_NOTE")?.split("###\n") || [];

    return list.map((item) => {
      const title = item.split("\n")[0];
      const content = item.replace(`${title}\n`, "");
      return {
        title,
        content,
      };
    });
  }, [getSystemConfig]);

  const { opacity, scrollRef } = useScrollOpacity(200);

  return (
    <PageWrapper className="relative z-10 user-margin-bg">
      <NavigationBar
        canBack
        title="信用分介绍"
        className="bg-transparent text-transparent"
        style={
          opacity > 0.5
            ? {
                background: `hsla(var(--app-bar-background)/${opacity})`,
                color: `hsla(var(--foreground)/${opacity})`,
              }
            : {}
        }
      />
      <PageMain scrollRef={scrollRef}>
        <div className="flex flex-col gap-[15px] px-[15px] mt-[250px] mb-[70px]">
          {noteList.map((note, index) => (
            <div
              key={index}
              className="flex flex-col gap-[15px] bg-[#F8E8FC] p-[15px] rounded-[10px]"
            >
              <span className="text-center text-[#4F2959] text-[25px] font-bold">
                {note.title}
              </span>
              <div className="text-[#4F2959] text-[15px] whitespace-pre-wrap">
                {note.content}
              </div>
            </div>
          ))}
        </div>
      </PageMain>
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-[-1]">
        <Image
          src={marginBackground}
          width="100%"
          height="auto"
          placeholder={null}
          fallback={null}
          className="object-cover relative top-[-50px]"
        />
      </div>
      <div className="fixed bottom-[15px] left-0 right-0 px-[15px]">
        <Button
          variant="primary"
          className="w-full"
          // onClick={showPay}
        >
          点击缴纳保证金
        </Button>
      </div>
    </PageWrapper>
  );
}
