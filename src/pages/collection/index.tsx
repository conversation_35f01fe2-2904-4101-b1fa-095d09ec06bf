import { DefaultLoadImage } from "@/components/default-load-image";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { collectionList } from "@/lib/api/user";
import { PageParams } from "@/type";
import { formatTimeLine } from "@/utils/format-time-line";
import { Image } from "antd-mobile";
import dayjs from "dayjs";

export default function Collection() {
  const queryKey = ["collection-list"];
  const queryFn = (params: PageParams) => collectionList(params);

  const { handleRefresh } = usePageListRefresh(queryKey);

  return (
    <PageWrapper>
      <NavigationBar canBack title="收藏列表" />
      <PageMain
        onRefresh={handleRefresh}
        className="p-[15px] bg-scaffold-background"
      >
        <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="flex flex-col gap-[15px]">
              {data.map((item) => (
                <div
                  key={item.id}
                  className="p-[15px] bg-app-bar-background rounded-[10px] flex gap-[25px]"
                >
                  <div className="flex-1 flex flex-col gap-[25px] text-sm">
                    <span className="line-clamp-2">{item.note}</span>
                    <span className="text-hint-color">
                      {item.targetTypeName} |{" "}
                      {formatTimeLine(dayjs(item.cdate).valueOf())}
                    </span>
                  </div>
                  <div className="w-[105px] h-[75px]">
                    <Image
                      src={item.cover}
                      width="100%"
                      height="100%"
                      fit="cover"
                      className="rounded-[5px]"
                      placeholder={<DefaultLoadImage />}
                      fallback={<DefaultLoadImage />}
                      onLoad={() => {}}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollLoadData>
      </PageMain>
    </PageWrapper>
  );
}
