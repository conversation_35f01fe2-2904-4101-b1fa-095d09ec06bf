import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { useCountDownTimer } from "@/hooks/use-count-down-timer";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { smsSendCode } from "@/lib/api/message";
import { bindBankCard } from "@/lib/api/wallet";
import { Button } from "@/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/ui/form";
import { Input } from "@/ui/input";
import { extractLeadingNumbers } from "@/utils";
import { useMutation } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import { useForm } from "react-hook-form";

export default function BindBankCard() {
  const sendCodeMutation = useMutation({ mutationFn: smsSendCode });

  const form = useForm();
  const { isRunning, seconds, handleStart } = useCountDownTimer(60);
  const { navigateBack } = useNavigateRoute();

  const bindBankCardMutation = useMutation({ mutationFn: bindBankCard });
  const { handleRefresh } = usePageListRefresh(["bank-list"]);

  const handleSubmit = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const values = form.getValues();
    const { ok } = await bindBankCardMutation.mutateAsync(values);

    if (ok) {
      handleRefresh();
      navigateBack();
    }
  };

  const checkPhone = async () => {
    const { userPhone } = form.getValues();

    if (!userPhone) {
      Toast.show({ content: "请输入手机号" });
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(userPhone)) {
      Toast.show({ content: "请输入正确的手机号" });
      return;
    }
    // 发送验证码
    Toast.show({
      icon: "loading",
      content: "加载中…",
      duration: 0,
    });

    const { ok, msg } = await sendCodeMutation.mutateAsync(userPhone);

    if (ok) {
      handleStart();
    } else {
      Toast.show({ content: msg });
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="绑定银行卡" />
      <PageMain className="bg-scaffold-background">
        <Form {...form}>
          <div className="flex flex-col gap-[15px] bg-app-bar-background p-[15px]">
            <FormField
              control={form.control}
              name="bankName"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>银行名称</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="请输入银行名称，如：支付宝、微信"
                        {...field}
                        autoComplete="off"
                      />
                    </FormControl>
                  </FormItem>
                );
              }}
            />
            <FormField
              control={form.control}
              name="subBankName"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>[可选] 支行名称</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="请输入支行名称，如：支付宝、微信"
                        {...field}
                        autoComplete="off"
                      />
                    </FormControl>
                  </FormItem>
                );
              }}
            />
            <FormField
              control={form.control}
              name="userName"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>开户名称</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="真实姓名，如：张三"
                        {...field}
                        autoComplete="off"
                      />
                    </FormControl>
                  </FormItem>
                );
              }}
            />
            <FormField
              control={form.control}
              name="userPhone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>开户手机号</FormLabel>
                  <FormControl>
                    <div className="flex items-center relative z-0">
                      <span className="absolute ml-[5px] z-[-1]">+86</span>
                      <Input
                        placeholder="请输入手机号"
                        className="pl-[45px] pr-[70px]"
                        maxLength={11}
                        {...field}
                        autoComplete="off"
                        value={extractLeadingNumbers(field.value)}
                      />
                      {!isRunning ? (
                        <Button
                          variant="primary"
                          size="sm"
                          className="absolute right-0"
                          onClick={checkPhone}
                        >
                          发送
                        </Button>
                      ) : (
                        <Button
                          size="sm"
                          variant="secondary"
                          className="absolute right-0"
                        >
                          {seconds}
                        </Button>
                      )}
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>短信验证码</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="请输入验证码"
                        maxLength={6}
                        {...field}
                        autoComplete="off"
                        value={extractLeadingNumbers(field.value)}
                      />
                    </FormControl>
                  </FormItem>
                );
              }}
            />
            <FormField
              control={form.control}
              name="bankCardNumber"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>银行卡号</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="如果是支付宝则对应支付宝账号"
                        {...field}
                        autoComplete="off"
                      />
                    </FormControl>
                  </FormItem>
                );
              }}
            />
          </div>
        </Form>
        <div className="px-[15px] py-[20px]">
          <Button
            variant="primary"
            size="lg"
            className="w-full"
            onClick={handleSubmit}
          >
            提交
          </Button>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
