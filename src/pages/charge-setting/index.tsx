import { CommonIcon } from "@/components/common-icon";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { SelectPopup } from "@/components/select-popup";
import { ArrowIcon } from "@/components/svg-icon";
import { updateChargeConfig, userChargeConfigItems } from "@/lib/api/wallet";
import { userWalletType } from "@/utils/enums";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Divider, Switch, Toast } from "antd-mobile";

const iconList: Record<string, string> = {
  "assets/images/common/icon/charge-config/chat.png": "chargeConfigChat",
  "assets/images/common/icon/charge-config/voice.png": "chargeConfigVoice",
  "assets/images/common/icon/charge-config/video.png": "chargeConfigVideo",
};

export default function ChargeSettingPage() {
  const { data, refetch } = useQuery({
    queryKey: ["charge-setting"],
    queryFn: () => userChargeConfigItems(),
  });

  const { mutateAsync } = useMutation({
    mutationFn: updateChargeConfig,
  });

  const handleItemClick = async (key: string, value: number) => {
    Toast.show({
      icon: "loading",
      content: "更新中...",
      duration: 0,
    });
    const { ok } = await mutateAsync({ key, value });

    if (ok) {
      Toast.show("更改成功");
      refetch();
    }
  };

  return (
    <PageWrapper className="bg-scaffold-background">
      <NavigationBar canBack title="收费设置" />
      <PageMain>
        <div className="flex flex-col p-[15px] gap-[15px]">
          {data?.data?.map((item) => {
            return (
              <div
                key={item.key}
                className=" bg-app-bar-background rounded-[10px] p-[15px]"
              >
                <div className="flex flex-col">
                  <div className="flex items-center">
                    <CommonIcon name={iconList[item.icon!]} w={36} h={36} />
                    <div className="ml-[12px] flex flex-col flex-1">
                      <span className="text-[15px] font-bold">
                        {item.title}
                      </span>
                      <span className="text-[13px] text-hint-color">
                        {item.note}
                      </span>
                    </div>
                    {(item.value ?? 0) > 0 ? (
                      <Switch
                        checked={item.value! > 0}
                        onChange={() => handleItemClick(item.key!, 0)}
                      />
                    ) : null}
                  </div>
                  <Divider className="my-[15px] border-hint-color" />
                  <div className="flex justify-center">
                    <SelectPopup
                      options={
                        item.values?.map((v) => ({
                          value: v,
                          title: `${v}${userWalletType.currency.label}${item.unit}`,
                          onClick: () => handleItemClick(item.key!, v),
                        })) ?? []
                      }
                    >
                      <div className="text-hint-color flex items-center">
                        <span className="text-[13px]">
                          {(item.value ?? 0) <= 0
                            ? "已关闭"
                            : `${item.value}${userWalletType.currency.label}${item.unit}`}
                        </span>
                        <ArrowIcon className="rotate-90 ml-[5px]" />
                      </div>
                    </SelectPopup>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </PageMain>
    </PageWrapper>
  );
}
