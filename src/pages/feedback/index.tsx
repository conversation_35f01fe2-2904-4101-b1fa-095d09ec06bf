import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { MediaUpload } from "@/components/work/media-upload";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { useUploadQueueManager } from "@/hooks/use-upload-queue-manager";
import { feedbackSubmitCheck, feedbackUrl } from "@/lib/api/system";
import { Button } from "@/ui/button";
import { Textarea } from "@/ui/textarea";
import { getRandomParams } from "@/utils/common";
import { useMutation } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import { useState } from "react";

export default function Feedback() {
  // todo: 上传图片和提交

  const [content, setContent] = useState("");
  const [mediaList, setMediaList] = useState<File[]>([]);

  const { mutateAsync } = useMutation({
    mutationFn: feedbackSubmitCheck,
  });

  const { addToQueue } = useUploadQueueManager();
  const { navigateBack } = useNavigateRoute();

  const handleSubmit = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await mutateAsync({
      content,
      resourceIds: mediaList.map(() => getRandomParams()),
    });

    if (ok) {
      addToQueue(
        {
          title: "意见反馈",
          url: feedbackUrl,
          params: {
            content,
            resourceIds: mediaList,
          },
        },
        () => {
          navigateBack();
        }
      );
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="意见反馈" />
      <PageMain className="bg-scaffold-background">
        <div className="bg-app-bar-background p-[15px] flex flex-col gap-[25px]">
          <div className="flex flex-col gap-[10px]">
            <span>反馈内容</span>
            <Textarea
              rows={5}
              className="border-none p-0"
              placeholder="请填写反馈内容"
              value={content}
              onChange={(e) => setContent(e.target.value)}
            />
          </div>
          <div className="flex flex-col gap-[10px]">
            <span>图片说明</span>
            <MediaUpload
              value={mediaList}
              onChange={(files) => setMediaList(files)}
            />
          </div>
        </div>
        <div className="mt-[25px] px-[15px]">
          <Button
            className="w-full"
            variant="primary"
            size="lg"
            onClick={handleSubmit}
          >
            提交反馈
          </Button>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
