import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { EnabledLocation } from "@/components/work/enabled-location";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/ui/form";
import { Image, Switch, Toast } from "antd-mobile";
import { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import {
  PriceSettingModal,
  PriceSettingModalRef,
} from "./components/price-setting-modal";
import { userWalletType } from "@/utils/enums";
import { useConfigList } from "@/hooks/use-config-list";
import { useMutation, useQuery } from "@tanstack/react-query";
import { layoutConfigOptions } from "@/utils/query-options";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { LayoutConfigDynamicPublish } from "@/type/layout-config";
import { Select } from "@/components/select";
import { Button } from "@/ui/button";
import { MediaUpload } from "./components/media-upload";
import { useSearchParams } from "react-router-dom";
import {
  dynamicParseMention,
  dynamicPublishCheck,
  dynamicPublishUrl,
} from "@/lib/api/dynamic";
import { DefaultLoadImage } from "@/components/default-load-image";
import { useTheme } from "@/provider/useTheme";
import { ThemeIcon } from "@/components/theme-icon";
import { VoiceRecordModal } from "@/components/work/voice-record-modal";
import { useModal } from "@/hooks/use-modal";
import { VoicePlayer } from "@/components/work/voice-player";
import { DeleteIcon } from "@/components/svg-icon";
import { Editor, EditorRef } from "@/components/editor";
import { getRandomParams, getVideoDuration, isVideo } from "@/utils/common";
import { useUploadQueueManager } from "@/hooks/use-upload-queue-manager";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

const formatIcon = (icon: { dark: string; light: string }, theme: string) => {
  const url = theme === "light" ? icon.light : icon.dark;

  return url.replace(/.*\/([^/]+)\.png$/, "$1");
};

export default function DynamicPublishPage() {
  const [searchParams] = useSearchParams();
  const targetType = Number(searchParams.get("targetType") || 0);
  const targetId = searchParams.get("targetId") || "";

  const form = useForm();
  const theme = useTheme();
  const {
    open: openVoice,
    openModal: openVoiceModal,
    closeModal: closeVoiceModal,
  } = useModal();
  const { getSystemConfig } = useConfigList();

  const minDuration = getSystemConfig("DYNAMIC_PUBLISH_VOICE_MIN_DURATION");
  const maxDuration = getSystemConfig("DYNAMIC_PUBLISH_VOICE_MAX_DURATION");
  const [media, setMedia] = useState<{ file: File; charge: boolean }[]>([]);
  const [voiceFile, setVoiceFile] = useState<File | null>(null);
  const [voiceDuration, setVoiceDuration] = useState<number>(0);
  const [voiceCharge, setVoiceCharge] = useState<boolean>(false);

  const [publicContent, setPublicContent] = useState("");
  const [paidContent, setPaidContent] = useState("");
  const [publicAtList, setPublicAtList] = useState<Record<string, string>>({});
  const [paidAtList, setPaidAtList] = useState<Record<string, string>>({});

  const publicEditorRef = useRef<EditorRef>(null);
  const paidEditorRef = useRef<EditorRef>(null);
  const focusEditorRef = useRef<EditorRef | undefined | null>();

  const handlePublicContentChange = (value: string) => {
    setPublicContent(value);
  };

  const handlePaidContentChange = (value: string) => {
    setPaidContent(value);
  };

  const handlePublicAtUserChange = (ats: Record<string, string>) => {
    setPublicAtList(ats);
  };

  const handlePaidAtUserChange = (ats: Record<string, string>) => {
    setPaidAtList(ats);
  };

  const { data: config } = useQuery(
    layoutConfigOptions<LayoutConfigDynamicPublish>(
      LayoutConfigKey.DynamicPublish
    )
  );

  const { data: mention } = useQuery({
    queryKey: ["dynamic-parse-mention", { targetType, targetId }],
    queryFn: () => dynamicParseMention({ targetType, targetId }),
    enabled: !!targetType && !!targetId,
  });

  const charge = form.watch("charge");
  const price = form.watch("price");

  const min = getSystemConfig("DYNAMIC_PUBLISH_MIN_PRICE");
  const max = getSystemConfig("DYNAMIC_PUBLISH_MAX_PRICE");
  const defaultPrice = getSystemConfig("DYNAMIC_PUBLISH_DEFAULT_PRICE");

  const maxLength = getSystemConfig("DYNAMIC_PUBLISH_TEXT_MAX_LENGTH");

  const priceSettingModalRef = useRef<PriceSettingModalRef>(null);

  useEffect(() => {
    form.setValue("price", defaultPrice);
  }, [defaultPrice, form]);

  useEffect(() => {
    form.setValue("payWay", config?.data?.payWay[0].value);
  }, [config?.data?.payWay, form]);

  const handleOpsClick = (type: string) => {
    if (type === "voice") {
      openVoiceModal();
    } else if (type === "at") {
      focusEditorRef.current?.showAt();
    } else if (type === "topic") {
      focusEditorRef.current?.showTopic();
    }
  };

  const handlePublicEditorFocus = () => {
    focusEditorRef.current = publicEditorRef.current;
  };

  const handlePaidEditorFocus = () => {
    focusEditorRef.current = paidEditorRef.current;
  };

  const voiceUrl = useMemo(() => {
    if (voiceFile) {
      return URL.createObjectURL(voiceFile);
    }

    return "";
  }, [voiceFile]);

  const { mutateAsync } = useMutation({
    mutationFn: dynamicPublishCheck,
  });

  const { addToQueue } = useUploadQueueManager();
  const { navigateBack } = useNavigateRoute();

  const handleSubmit = async () => {
    const data = form.getValues();

    const { payWay, charge, price, ...rest } = data;

    if (
      publicContent.length > Number(maxLength) ||
      (charge && paidContent.length > Number(maxLength))
    ) {
      Toast.show({
        content: `不能超过${maxLength}字`,
      });

      return;
    }

    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const durations: number[] = [];

    for (let i = 0; i < media.length; i++) {
      const { file } = media[i];
      if (isVideo(file)) {
        const duration = await getVideoDuration(file);
        durations.push(duration);
      } else {
        durations.push(0);
      }
    }

    const atList = {
      ...publicAtList,
      ...paidAtList,
    };

    const { ok } = await mutateAsync({
      ...rest,
      publicContent,
      paidContent,
      voice: voiceFile ? getRandomParams() : undefined,
      resources: media.map(() => getRandomParams()),
      ats: atList,
      chargeConfig: charge
        ? {
            payWay,
            price,
            resources: media
              .filter((item) => item.charge)
              .map(() => getRandomParams()),
            voice: voiceCharge,
          }
        : undefined,
      mention: mention?.data ? { targetId, targetType } : undefined,
    });

    if (ok) {
      addToQueue(
        {
          title: "动态发布",
          url: dynamicPublishUrl,
          params: {
            ...rest,
            publicContent,
            paidContent,
            voice: voiceFile ? voiceFile : undefined,
            resources: media.map((item) => item.file),
            ats: atList,
            chargeConfig: charge
              ? {
                  payWay,
                  price,
                  resources: media
                    .filter((item) => item.charge)
                    .map((item) => item.file),
                  voice: voiceCharge,
                }
              : undefined,
            mention: mention?.data ? { targetId, targetType } : undefined,
          },
        },
        () => {
          navigateBack();
        }
      );
    }
  };

  return (
    <PageWrapper className="bg-scaffold-background">
      <NavigationBar canBack title="动态发布" />
      <PageMain>
        <div className="p-[15px] flex flex-col gap-[15px] bg-app-bar-background mb-[15px]">
          {mention?.data ? (
            <div className="flex flex-col gap-[5px]">
              <span className="text-[15px]">引用内容</span>
              <div className="flex gap-[10px]">
                {mention?.data?.cover ? (
                  <div className="flex-none w-[60px] h-[60px]">
                    <Image
                      src={mention.data.cover}
                      width="100%"
                      height="100%"
                      fit="cover"
                      placeholder={<DefaultLoadImage />}
                      fallback={<DefaultLoadImage />}
                      className="rounded-[10px]"
                    />
                  </div>
                ) : null}
                <div className="flex flex-col justify-around">
                  <div className="line-clamp-1">
                    <span className="text-white text-xs bg-[#FF595D] px-[2px] rounded-[3px] mr-[5px]">
                      {mention?.data?.label}
                    </span>
                    <span>
                      {mention?.data?.title}
                      {mention?.data?.title}
                      {mention?.data?.title}
                    </span>
                  </div>
                  <div className="line-clamp-2 text-hint-color text-[13px]">
                    {mention?.data?.subtitle}
                  </div>
                </div>
              </div>
            </div>
          ) : null}
          {voiceFile ? (
            <div className="flex items-center gap-[15px]">
              <div className="flex items-center gap-[5px]">
                <div className="w-[200px]">
                  <VoicePlayer url={voiceUrl} duration={voiceDuration} />
                </div>
                <span onClick={() => setVoiceFile(null)}>
                  <DeleteIcon size={24} />
                </span>
              </div>
              {charge ? (
                <Switch
                  checked={voiceCharge}
                  onChange={(value) => setVoiceCharge(value)}
                />
              ) : null}
            </div>
          ) : null}
          <div className="flex flex-col gap-[5px]">
            <span className="text-[15px]">发布内容</span>
            <Editor
              ref={publicEditorRef}
              canTopic
              showCount
              maxLength={Number(maxLength)}
              className="h-[100px]"
              onFocus={handlePublicEditorFocus}
              onChangeValue={handlePublicContentChange}
              onChangeAtUsers={handlePublicAtUserChange}
            />
          </div>
          <div className="flex flex-col gap-[5px]">
            <span className="text-[15px]">收费内容</span>
            <Editor
              ref={paidEditorRef}
              canTopic
              showCount
              maxLength={Number(maxLength)}
              className="h-[100px]"
              onFocus={handlePaidEditorFocus}
              onChangeValue={handlePaidContentChange}
              onChangeAtUsers={handlePaidAtUserChange}
            />
          </div>
          <div className="flex gap-[10px]">
            {config?.data?.ops?.map((item) => (
              <div
                className="flex justify-center items-center min-w-[65px] min-h-[25px] bg-[#F2EFF9] dark:bg-[#312C5D] rounded-[3px]"
                onClick={() => handleOpsClick(item.sign)}
              >
                {item.title ? (
                  <span className="text-sm text-[#6712FF)] dark:text-[#CAB3FF]">
                    {item.title}
                  </span>
                ) : null}
                {item.icon ? (
                  <ThemeIcon
                    name={formatIcon(item.icon, theme)}
                    w={item.icon.width / 2}
                    h={item.icon.height / 2}
                  />
                ) : null}
                {}
              </div>
            ))}
          </div>
          <MediaUpload showCharge={charge} value={media} onChange={setMedia} />
        </div>
        <div className="flex flex-col gap-[15px] bg-app-bar-background p-[15px]">
          <Form {...form}>
            <div className="flex flex-col gap-[15px]">
              <FormField
                control={form.control}
                name="charge"
                render={({ field }) => {
                  return (
                    <FormItem className="flex items-center justify-between">
                      <FormLabel>开启收费</FormLabel>
                      <div className="flex items-center gap-[10px]">
                        {charge ? (
                          <div
                            className="text-[15px] text-[#ffb400]"
                            onClick={() => priceSettingModalRef.current?.open()}
                          >
                            {price}
                            {userWalletType.currency.label}
                            (点击修改)
                          </div>
                        ) : null}
                        <FormControl>
                          <Switch
                            className="!m-0"
                            checked={field.value}
                            onChange={field.onChange}
                          />
                        </FormControl>
                      </div>
                    </FormItem>
                  );
                }}
              />
              {charge ? (
                <FormField
                  control={form.control}
                  name="payWay"
                  render={({ field }) => {
                    return (
                      <FormItem className="flex items-center justify-between">
                        <FormLabel>支付方式</FormLabel>

                        <FormControl>
                          <Select
                            options={
                              config?.data?.payWay.map((item) => ({
                                title: item.label,
                                value: item.value,
                              })) ?? []
                            }
                            {...field}
                            titleRender={(label) => `${label}(点击修改)`}
                          />
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
              ) : null}
              <FormField
                control={form.control}
                name="enabledLocation"
                render={({ field }) => {
                  return (
                    <FormItem className="flex justify-between items-center">
                      <FormLabel>位置信息</FormLabel>
                      <FormControl>
                        <EnabledLocation {...field} />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="anonymous"
                render={({ field }) => {
                  return (
                    <FormItem className="flex items-center justify-between">
                      <FormLabel>匿名</FormLabel>
                      <FormControl>
                        <Switch
                          className="!m-0"
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="prohibitComment"
                render={({ field }) => {
                  return (
                    <FormItem className="flex items-center justify-between">
                      <FormLabel>禁止评论</FormLabel>
                      <FormControl>
                        <Switch
                          className="!m-0"
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
            </div>
          </Form>
        </div>
        <div className="px-[30px] py-[25px]">
          <Button variant="primary" className="w-full" onClick={handleSubmit}>
            发布
          </Button>
        </div>
      </PageMain>
      <PriceSettingModal
        min={Number(min)}
        max={Number(max)}
        value={Number(price)}
        onChange={(value) => form.setValue("price", value)}
        ref={priceSettingModalRef}
      />
      <VoiceRecordModal
        open={openVoice}
        minDuration={Number(minDuration)}
        maxDuration={Number(maxDuration)}
        onClose={closeVoiceModal}
        onFinish={(file, duration) => {
          setVoiceFile(file);
          setVoiceDuration(duration);
        }}
      />
    </PageWrapper>
  );
}
