import { BaseMediaUpload } from "@/components/base-media-upload";
import { UploadFile } from "@/components/work/upload-file";
import { AspectRatio } from "@/ui/aspect-ratio";
import { Switch } from "antd-mobile";

interface Props {
  showCharge?: boolean;
  maxCount?: number;
  accept?: "image/*" | "video/*" | "image/*,video/*";
  value: { file: File; charge: boolean }[];
  onChange: (files: { file: File; charge: boolean }[]) => void;
}

export const MediaUpload = (props: Props) => {
  const { maxCount = 9, accept, value, onChange, showCharge } = props;

  const canUploadCount = maxCount - (value?.length ?? 0);

  const handleUpload = (files: File[]) => {
    onChange?.([
      ...(value ?? []),
      ...files.map((file) => ({ file, charge: false })),
    ]);
  };

  const handleDelete = (index: number) => {
    onChange?.(value?.filter((_, i) => i !== index));
  };

  return (
    <div className="flex-1">
      <div className="grid grid-cols-3 gap-[15px]">
        {value?.length
          ? value.map((item, index) => (
              <div className="flex flex-col items-center gap-[5px]">
                <AspectRatio key={index} ratio={1}>
                  <UploadFile
                    file={item.file}
                    onDelete={() => handleDelete(index)}
                  />
                </AspectRatio>
                {showCharge ? (
                  <div className="flex items-center gap-[10px]">
                    <span>收费</span>
                    <Switch
                      style={{ "--height": "20px", "--width": "30px" }}
                      checked={item.charge}
                      onChange={(checked) => {
                        onChange?.(
                          value.map((item, i) =>
                            i === index ? { ...item, charge: checked } : item
                          )
                        );
                      }}
                    />
                  </div>
                ) : null}
              </div>
            ))
          : null}
        {canUploadCount > 0 ? (
          <div className="flex flex-col items-center gap-[5px]">
            <AspectRatio ratio={1}>
              <BaseMediaUpload
                accept={accept}
                className="w-full h-full"
                maxCount={canUploadCount}
                onSelected={handleUpload}
              />
              <div className="h-[21px]" />
            </AspectRatio>
          </div>
        ) : null}
      </div>
    </div>
  );
};
