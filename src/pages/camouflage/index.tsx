import { PageWrapper } from "@/components/page-wrapper";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { useCamouflageState } from "@/store/camouflage-state";
import { CAMOUFLAGE_PASSWORD } from "@/type/cache";
import { Input } from "@/ui/input";
import { Form } from "antd-mobile";
import "./index.less";
export default function Camouflage() {
  const password = localStorage.getItem(CAMOUFLAGE_PASSWORD);

  const { navigateRoute } = useNavigateRoute();
  const { setCamouflageState } = useCamouflageState();

  const handleSearch = (values: any) => {
    const value = values.search;
    if (value) {
      if (value === password) {
        setCamouflageState({ state: true });
        navigateRoute("/", {}, { replace: true });
      }
    }
  };

  return (
    <PageWrapper>
      <div className="camouflage p-[15px] w-full h-full flex flex-col justify-center gap-5">
        <span className="text-[25px] font-bold text-center">神秘搜索</span>
        <Form className="bg-transparent" onFinish={handleSearch}>
          <Form.Item name="search">
            <Input
              placeholder="请输入您要搜索的内容"
              autoComplete="off"
              className="border rounded-full px-[15px] focus:border-[hsl(var(--input))]"
            />
          </Form.Item>
        </Form>
      </div>
    </PageWrapper>
  );
}
