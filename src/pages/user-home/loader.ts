import { getUserDetailed } from "@/lib/api/user";
import { queryClient } from "@/provider/query-client";
import { parseUrl } from "@/utils/parse-url";
import { defer, LoaderFunction, LoaderFunctionArgs } from "react-router-dom";

export const loader: LoaderFunction = async ({
  request,
}: LoaderFunctionArgs) => {
  const { searchParams } = parseUrl(request.url);
  const id = searchParams.get("id");

  return defer({
    data: queryClient.fetchQuery({
      queryKey: ["user-detailed", id],
      queryFn: () => getUserDetailed(id ?? undefined),
    }),
  });
};
