import { Input } from "@/ui/input";
import { Divider, Popup } from "antd-mobile";
import { Fragment, useMemo, useState } from "react";

const typeList = [
  {
    label: "QQ",
    value: 0,
  },
  {
    label: "微信",
    value: 1,
  },
  {
    label: "电话",
    value: 2,
  },
];

interface Props {
  value?: number;
  onChange?: (value: number) => void;
}

export const FormSelect = (props: Props) => {
  const { value, onChange } = props;

  const label = useMemo(() => {
    return typeList.find((item) => item.value === value)?.label;
  }, [value]);

  const [visible, setVisible] = useState(false);

  const handleSelect = (value: number) => {
    setVisible(false);
    onChange?.(value);
  };

  return (
    <div>
      <Input
        placeholder="请选择类型"
        autoComplete="off"
        value={label}
        readOnly
        onClick={() => setVisible(true)}
      />
      <Popup
        visible={visible}
        bodyClassName="bg-transparent"
        onMaskClick={() => setVisible(false)}
        onClose={() => setVisible(false)}
      >
        <div className="flex flex-col gap-[10px] mx-[15px]">
          <div className="flex flex-col rounded-[10px] bg-app-bar-background">
            {typeList.map((item, index) => (
              <Fragment key={item.value}>
                <div
                  className="py-[15px] text-center text-base text-[#9d64ff]"
                  onClick={() => handleSelect(item.value)}
                >
                  {item.label}
                </div>
                {index !== typeList.length - 1 ? (
                  <Divider className="border-divider m-0" />
                ) : null}
              </Fragment>
            ))}
          </div>
          <div
            className="mb-[10px] py-[15px] text-center text-base text-[#9d64ff] bg-hint-color rounded-[10px]"
            onClick={() => setVisible(false)}
          >
            取消
          </div>
        </div>
      </Popup>
    </div>
  );
};
