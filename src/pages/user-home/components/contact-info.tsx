import { contactBackground } from "@/components/image-list";
import { ReportIcon } from "@/components/svg-icon";
import { useConfigList } from "@/hooks/use-config-list";
import { buyContact, closeSellContact, setContact } from "@/lib/api/user";
import { UserContactResult } from "@/type/user-contact-result";
import { Button } from "@/ui/button";
import { Form, FormControl, FormField, FormItem } from "@/ui/form";
import { Input } from "@/ui/input";
import {
  ReportTargetType,
  UserContactState,
  userWalletType,
} from "@/utils/enums";
import { useMutation } from "@tanstack/react-query";
import { Dialog, Image, Toast } from "antd-mobile";
import { ReactNode } from "react";
import { useForm } from "react-hook-form";
import { FormSelect } from "./form-select";
import "./contact-info.less";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

interface ContactCardProps {
  title: ReactNode;
  type?: string;
  price?: number | string;
  target?: string;
  action: ReactNode;
  showTips: boolean;
}

const ContactCard = (props: ContactCardProps) => {
  const {
    title,
    type = "-",
    price = "-",
    target = "-",
    action,
    showTips,
  } = props;

  const { getSystemConfig } = useConfigList();

  return (
    <div className="user-home-contact-bg rounded-[10px] px-[15px] py-5">
      <div className="flex flex-col gap-[10px]">
        <span className="text-black text-xl font-bold">{title}</span>
        {showTips ? (
          <div>
            <span className="inline-flex bg-[#FF595D] rounded-[3px] px-[5px] mr-[5px]">
              <span className="text-[22px] text-white" style={{ zoom: 0.5 }}>
                请注意
              </span>
            </span>
            <span className="whitespace-pre-wrap text-black text-xs">
              {getSystemConfig("USER_CONTACT_BUY_TIPS")}
            </span>
          </div>
        ) : null}
      </div>
      <div className="mt-3 relative w-[307px] h-[85px] z-10">
        <div className="flex h-full">
          <div className="w-[96px] h-full flex flex-col justify-center items-center gap-[10px] text-[15px]">
            <span className="text-[#C16B5A]">联系方式</span>
            <span className="text-[#830000]">{type}</span>
          </div>
          <div className="w-[92px] h-full flex flex-col justify-center items-center gap-[10px] text-[15px]">
            <span className="text-[#C16B5A]">价格</span>
            <span className="text-[#830000]">{price}</span>
          </div>
          <div className="flex-1 h-full flex flex-col justify-center items-center gap-[10px] text-[15px]">
            <span className="text-[#C16B5A]">号码(点击复制)</span>
            <span className="text-[#830000] line-clamp-1">{target}</span>
          </div>
        </div>
        <div className="absolute top-0 left-0 w-full h-full z-[-1]">
          <Image
            src={contactBackground}
            width="100%"
            height="100%"
            fit="cover"
            placeholder={null}
            fallback={null}
          />
        </div>
      </div>
      {action}
    </div>
  );
};

interface ContactBuyProps {
  data: UserContactResult;
  onUpdate: () => void;
}

export const ContactBuy = (props: ContactBuyProps) => {
  const { data, onUpdate } = props;

  const { navigateRoute } = useNavigateRoute();

  const handleReportClick = () => {
    navigateRoute("/report", {
      id: data.id,
      type: ReportTargetType.Contact,
    });
  };

  const buyContactMutation = useMutation({
    mutationFn: buyContact,
  });

  const handleBuyClick = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await buyContactMutation.mutateAsync(data.id!);

    if (ok) {
      Toast.show({
        content: "购买成功",
      });
      onUpdate();
    }
  };

  return (
    <ContactCard
      title={
        <div className="flex gap-1">
          <span>购买联系方式</span>
          {!data.hasBuy ? (
            <div
              className="flex gap-[2px] text-[14px] text-[#FF5252]"
              onClick={handleReportClick}
            >
              <ReportIcon size={16} />
              <span>举报</span>
            </div>
          ) : null}
        </div>
      }
      type={data.typeLabel}
      price={`${data.price ?? 0}${userWalletType.currency.label}`}
      target={data.targetValue}
      showTips
      action={
        <div className="pt-[15px] flex justify-center">
          {data.hasBuy ? (
            <span className="text-black">{data.tips}</span>
          ) : (
            <Button
              variant="primary"
              size="lg"
              className="w-full"
              onClick={handleBuyClick}
            >
              {data.price ?? 0}
              {userWalletType.currency.label}，立即购买
            </Button>
          )}
        </div>
      }
    />
  );
};

interface ContactSettingProps {
  data?: UserContactResult;
  onUpdate: () => void;
}

export const ContactSetting = (props: ContactSettingProps) => {
  const { data, onUpdate } = props;

  const form = useForm({
    defaultValues: data,
  });

  const { getSystemConfig } = useConfigList();

  const setContactMutation = useMutation({
    mutationFn: setContact,
  });

  const handleSetContact = async (close: () => void) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await setContactMutation.mutateAsync(form.getValues());

    if (ok) {
      onUpdate();
      close();
    }
  };

  const closeSellContactMutation = useMutation({
    mutationFn: closeSellContact,
  });

  const handleCloseSellContact = async (close: () => void) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await closeSellContactMutation.mutateAsync();

    if (ok) {
      onUpdate();
      close();
    }
  };

  const handleSettingClick = () => {
    const { close } = Dialog.show({
      bodyClassName: "bg-app-bar-background !p-[15px] setting-contact-dialog",
      title: <span className="text-xl">设置联系方式</span>,
      content: (
        <div className="flex flex-col">
          <div>
            <span className="px-[5px] bg-[#FF595D] rounded-[3px] mr-[5px] inline-flex">
              <span className="text-[22px] text-white" style={{ zoom: 0.5 }}>
                请注意
              </span>
            </span>
            <span className="text-xs text-hint-color">
              {getSystemConfig("USER_CONTACT_SETTING_TIPS")}
            </span>
          </div>
          <div className="pt-5 text-foreground">
            <Form {...form}>
              <div className="flex flex-col gap-[10px]">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormControl>
                          <FormSelect {...field} />
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
                <FormField
                  control={form.control}
                  name="targetValue"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormControl>
                          <Input
                            placeholder="请设置联系方式"
                            autoComplete="off"
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormControl>
                          <Input
                            placeholder="请输入价格(足币)"
                            autoComplete="off"
                            type="number"
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
                <FormField
                  control={form.control}
                  name="note"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormControl>
                          <Input
                            placeholder="请输入说明(可选)"
                            autoComplete="off"
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
              </div>
            </Form>
          </div>
          <div className="pt-5 flex gap-[10px]">
            <Button
              variant="primary"
              size="lg"
              className="w-full h-[40px]"
              onClick={async () => handleSetContact(close)}
            >
              确定
            </Button>
            {data?.state === UserContactState.Normal ? (
              <Button
                variant="secondary"
                size="lg"
                className="w-full h-[40px]"
                onClick={async () => handleCloseSellContact(close)}
              >
                停止出售
              </Button>
            ) : null}
          </div>
        </div>
      ),
      closeOnMaskClick: true,
    });
  };

  return (
    <ContactCard
      title="出售联系方式"
      type={data?.typeLabel ?? "-"}
      price={data?.price ?? "-"}
      target={data?.targetValue ?? "-"}
      showTips={false}
      action={
        <div className="pt-[15px] flex flex-col items-center">
          {data?.state === UserContactState.Violation ? (
            <span className="text-[#FF5252]">{data?.tips}</span>
          ) : null}
          {!data ? (
            <Button
              variant="primary"
              size="lg"
              className="w-full"
              onClick={handleSettingClick}
            >
              设置联系方式
            </Button>
          ) : (
            <Button
              variant="primary"
              size="lg"
              className="w-full"
              onClick={handleSettingClick}
            >
              {data.state === UserContactState.Closed
                ? "已下架，点击上架"
                : "已上架，点击修改"}
            </Button>
          )}
        </div>
      }
    />
  );
};
