import { CommonIcon } from "@/components/common-icon";
import { DefaultLoadImage } from "@/components/default-load-image";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ArrowIcon, EditIcon, MoreIcon, PlusIcon } from "@/components/svg-icon";
import { AlbumResourceBox } from "@/components/work/album-resource-box";
import { UserInfo } from "@/components/work/user-info";
import { useConfigList } from "@/hooks/use-config-list";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useScrollOpacity } from "@/hooks/user-scroll-opacity";
import { UserDetailed } from "@/type/user-detailed";
import { ScrollArea, ScrollBar } from "@/ui/scroll-area";
import { ReportTargetType, userWalletType } from "@/utils/enums";
import { Divider, Image, NoticeBar, Toast } from "antd-mobile";
import { useMemo } from "react";
import {
  useAsyncValue,
  useRevalidator,
  useSearchParams,
} from "react-router-dom";
import { ContactBuy, ContactSetting } from "./components/contact-info";
import classNames from "classnames";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  follow,
  getUserExpand,
  giveGift,
  joinBlacklist,
  removeBlacklist,
  unfollow,
} from "@/lib/api/user";
import { useModal } from "@/hooks/use-modal";
import { GiftResult } from "@/type/gift-result";
import { queryClient } from "@/provider/query-client";
import { userWalletQueryKey } from "@/hooks/use-user-wallet";
import { GiftModal } from "@/components/work/gift-modal";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { getRoute } from "@/router/route-map";
import { SelectPopup } from "@/components/select-popup";

export default function UserHomePage() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id") || undefined;

  const { revalidate } = useRevalidator();
  const { navigateRoute } = useNavigateRoute();

  const { data: userInfo } = useFullUserInfo();
  const { getSystemConfig } = useConfigList();

  const { data: userDetailed } = useAsyncValue() as {
    data: UserDetailed;
  };

  const userExpandQueryKey = ["user-expand", id];
  const { data } = useQuery({
    queryKey: userExpandQueryKey,
    queryFn: () => getUserExpand(id ?? undefined),
  });
  const userExpand = data?.data;

  const { open, openModal, closeModal } = useModal();

  const isSelf = !id || userInfo?.id === id;

  const resources = useMemo(
    () => [
      ...(userDetailed?.album?.publicResource ?? []),
      ...(userDetailed?.album?.paidResource ?? []),
    ],
    [userDetailed]
  );
  const needBuyIndex = userDetailed?.album?.publicResource?.length ?? 0;

  const { opacity, scrollRef } = useScrollOpacity(165);

  const refreshUserExpand = () => {
    queryClient.refetchQueries({
      queryKey: userExpandQueryKey,
    });
  };

  const handleAvatarClick = () => {
    navigateRoute(
      "/resource-preview",
      {},
      {
        state: {
          data: [
            {
              thumbnailImage: userDetailed.avatarUrl ?? "",
              url: userDetailed.avatarBigImageUrl,
              video: false,
              preview: false,
            },
          ],
          defaultIndex: 0,
        },
      }
    );
  };

  const handleCoverClick = () => {
    navigateRoute(
      "/resource-preview",
      {},
      {
        state: {
          data: [
            {
              thumbnailImage: userDetailed.cover?.images?.Thumbnail?.url ?? "",
              url: userDetailed.cover?.url,
              video: !!userDetailed.cover?.duration,
              preview: false,
            },
          ],
          defaultIndex: 0,
        },
      }
    );
  };

  const handleSelfClick = () => {
    navigateRoute("/user-profile");
  };

  const handleMindWallClick = () => {
    navigateRoute("/user-mind-wall", {
      id: userDetailed?.id,
    });
  };

  const handleFansClick = () => {
    navigateRoute("/fans", {
      id: userDetailed?.id,
    });
  };

  const handleFollowClick = () => {
    navigateRoute("/follow", {
      id: userDetailed?.id,
    });
  };

  const handleAlbumClick = () => {
    navigateRoute("/user-album", {
      id: userDetailed?.id,
    });
  };

  const handleSignatureClick = () => {
    navigateRoute("/signature");
  };

  const handleMedalsClick = () => {
    navigateRoute("/medal");
  };

  const handleRouteClick = (uri: string) => {
    navigateRoute(getRoute(uri));
  };

  const handleChatClick = () => {
    navigateRoute("/chat", {
      uid: userDetailed?.id,
    });
  };

  const handleVideoClick = () => {
    navigateRoute("/chat", {
      uid: userDetailed?.id,
    });
  };

  const handleReportClick = () => {
    navigateRoute("/report", {
      id,
      type: ReportTargetType.User,
    });
  };

  const followMutation = useMutation({
    mutationFn: follow,
  });
  const unfollowMutation = useMutation({
    mutationFn: unfollow,
  });

  const handleFollowChange = async () => {
    Toast.show({
      icon: "loading",
      content: "加载中...",
      duration: 0,
    });

    const mutation = userDetailed?.hasFollow
      ? unfollowMutation
      : followMutation;
    const { ok } = await mutation.mutateAsync(userDetailed?.id!);

    if (ok) {
      revalidate();
    }
  };

  const handleSendGiftClick = () => {
    openModal();
  };

  const giveGiftMutation = useMutation({
    mutationFn: giveGift,
  });

  const handleSendGift = async (gift: GiftResult, note?: string) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await giveGiftMutation.mutateAsync({
      userId: userDetailed.id!,
      giftId: gift.id!,
      note,
    });

    if (ok) {
      Toast.show({
        content: "礼物赠送成功",
      });

      closeModal();
      refreshUserExpand();
      queryClient.refetchQueries({
        queryKey: userWalletQueryKey,
      });
    }
  };

  const joinBlacklistMutation = useMutation({
    mutationFn: joinBlacklist,
  });
  const removeBlacklistMutation = useMutation({
    mutationFn: removeBlacklist,
  });

  const handleBlacklistChange = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const mutation = userDetailed?.block
      ? removeBlacklistMutation
      : joinBlacklistMutation;
    const { ok } = await mutation.mutateAsync(userDetailed?.id!);

    if (ok) {
      revalidate();
    }
  };

  return (
    <PageWrapper className="relative z-10">
      <NavigationBar
        canBack
        title={userDetailed.nickname}
        className="bg-transparent text-transparent"
        forceWhiteBackIcon={opacity > 0.5 ? false : true}
        style={
          opacity > 0.5
            ? {
                background: `hsla(var(--app-bar-background)/${opacity})`,
                color: `hsla(var(--foreground)/${opacity})`,
              }
            : {}
        }
        action={
          <span className={opacity > 0.5 ? "" : "text-white"}>
            {isSelf ? (
              <span onClick={handleSelfClick}>
                <EditIcon size={18} />
              </span>
            ) : (
              <SelectPopup
                options={[
                  {
                    title: "举报",
                    value: "report",
                    onClick: handleReportClick,
                  },
                  {
                    title: userDetailed.block ? "从黑名单移除" : "加入黑名单",
                    value: "block",
                    onClick: handleBlacklistChange,
                  },
                ]}
              >
                <MoreIcon size={30} />
              </SelectPopup>
            )}
          </span>
        }
      />
      <PageMain scrollRef={scrollRef}>
        <div className="flex flex-col p-[15px]">
          <div className="h-[130px] w-full" onClick={handleCoverClick} />
          <div
            className="flex items-center"
            onClick={isSelf ? handleSelfClick : undefined}
          >
            <UserInfo
              user={userDetailed}
              tail={
                <div className="flex items-center">
                  <span>ID: {userDetailed.userid}</span>
                  <span className="pl-[10px] pr-[5px]">
                    <CommonIcon name="location" w={12} h={12} />
                  </span>
                  <span className="text-xs text-hint-color">
                    {userDetailed.realAddress ?? "未知"}
                  </span>
                </div>
              }
              onClick={isSelf ? handleSelfClick : undefined}
              onAvatarClick={handleAvatarClick}
            />
            {isSelf ? <ArrowIcon /> : null}
          </div>
          <Divider className="border-divider" />
          <div className="flex gap-2 text-hint-color">
            <div className="flex-1 flex justify-center items-center gap-[5px]">
              <CommonIcon name="chargeConfigChat" w={12} h={12} />
              <span className="text-[13px]">
                {userDetailed.chargeConfig?.chatPrice ?? 0}
                {userWalletType.currency.label}/条
              </span>
            </div>
            <div className="flex-1 flex justify-center items-center gap-[5px]">
              <CommonIcon name="chargeConfigVoice" w={12} h={12} />
              <span className="text-[13px]">
                {userDetailed.chargeConfig?.voicePrice ?? 0}
                {userWalletType.currency.label}/分钟
              </span>
            </div>
            <div className="flex-1 flex justify-center items-center gap-[5px]">
              <CommonIcon name="chargeConfigVideo" w={12} h={12} />
              <span className="text-[13px]">
                {userDetailed.chargeConfig?.videoPrice ?? 0}
                {userWalletType.currency.label}/分钟
              </span>
            </div>
          </div>
          <div className="pt-[18px]">
            <div className="flex gap-2">
              <div
                className="flex-1 flex flex-col bg-scaffold-background items-center justify-center p-[10px] rounded-[5px]"
                onClick={handleFollowClick}
              >
                <span
                  className="text-hint-color text-[22px]"
                  style={{ zoom: 0.5 }}
                >
                  关注
                </span>
                <span className="text-base font-bold">
                  {userDetailed.followNumber ?? 0}
                </span>
              </div>
              <div
                className="flex-1 flex flex-col bg-scaffold-background items-center justify-center p-[10px] rounded-[5px]"
                onClick={handleFansClick}
              >
                <span
                  className="text-hint-color text-[22px]"
                  style={{ zoom: 0.5 }}
                >
                  粉丝
                </span>
                <span className="text-base font-bold">
                  {userDetailed.fansNumber ?? 0}
                </span>
              </div>
              <div
                className="flex-1 flex flex-col bg-scaffold-background items-center justify-center p-[10px] rounded-[5px]"
                onClick={handleMindWallClick}
              >
                <CommonIcon name="mind" w={18} h={18} />
                <span className="text-sm font-bold">心意墙</span>
              </div>
            </div>
          </div>
          {!isSelf && userDetailed.existViolation ? (
            <div className="py-[15px]">
              <NoticeBar
                icon={null}
                content={getSystemConfig("VIOLATION_USER_OTHER_TIPS")}
                className="h-[25px] text-[#F44336] bg-foreground/10 border-none"
              />
            </div>
          ) : null}
          {isSelf || resources.length ? (
            <div className="pt-[15px]">
              <div className="flex flex-col gap-[10px]">
                <span className="text-sm">颜照库</span>
                <ScrollArea>
                  <div className="flex gap-2">
                    {resources.map((resource, index) => (
                      <div key={resource.id} onClick={handleAlbumClick}>
                        <AlbumResourceBox
                          data={resource}
                          size={100}
                          buy={
                            index < needBuyIndex || !!userDetailed.album?.buy
                          }
                        />
                      </div>
                    ))}
                    {isSelf ? (
                      <div
                        className="w-[100px] h-[100px] rounded-[5px] bg-scaffold-background flex flex-col justify-center items-center"
                        onClick={handleAlbumClick}
                      >
                        <PlusIcon size={14} />
                        <span>上传</span>
                      </div>
                    ) : null}
                  </div>
                  <ScrollBar orientation="horizontal" />
                </ScrollArea>
              </div>
            </div>
          ) : null}
          <div className="pt-[10px]">
            <div
              className="flex flex-col gap-[10px]"
              onClick={isSelf ? handleSignatureClick : undefined}
            >
              <span className="text-sm">个性签名</span>
              <div className="flex gap-1">
                <span className="flex-1 text-sm text-hint-color whitespace-pre-wrap">
                  {userDetailed.signature ?? "-"}
                </span>
                {isSelf ? <ArrowIcon size={14} /> : null}
              </div>
            </div>
          </div>
          {!isSelf && userExpand?.contact ? (
            <div className="pt-[10px]">
              <ContactBuy
                data={userExpand.contact}
                onUpdate={() => {
                  refreshUserExpand();
                }}
              />
            </div>
          ) : null}
          {userExpand && isSelf ? (
            <div className="pt-[10px]">
              <ContactSetting
                data={userExpand.contact}
                onUpdate={() => {
                  refreshUserExpand();
                }}
              />
            </div>
          ) : null}
          <Divider className="border-divider" />
          <div>
            {userExpand?.groups?.map((group) => (
              <div key={group.title} className="flex flex-col gap-[10px]">
                <div className="flex items-center">
                  <span className="flex-1">{group.title}</span>
                  {group.linkTips ? (
                    <div
                      className="flex py-[10px]"
                      onClick={() => handleRouteClick(group.uri!)}
                    >
                      <span className="text-hint-color text-[13px]">
                        {group.linkTips}
                      </span>
                      <ArrowIcon size={15} className="text-hint-color" />
                    </div>
                  ) : null}
                </div>
                <div className="flex flex-col gap-[10px]">
                  {group.items?.map((item) => (
                    <div
                      key={item.title}
                      className="flex items-center gap-[10px]"
                      onClick={() => handleRouteClick(item.uri!)}
                    >
                      {item.cover ? (
                        <div className="flex-none w-[50px] h-[50px]">
                          <Image
                            src={item.cover}
                            width="100%"
                            height="100%"
                            fit="cover"
                            className="rounded-[10px]"
                            placeholder={<DefaultLoadImage />}
                            fallback={<DefaultLoadImage />}
                          />
                        </div>
                      ) : null}
                      <div>
                        <span className="text-xs text-hint-color line-clamp-1">
                          {item.title}
                        </span>
                        <span className="line-clamp-2 whitespace-pre-wrap">
                          {item.content}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
          {userExpand?.giftSummary?.length ? (
            <>
              <div className="pt-[10px]">
                <div className="flex flex-col gap-[10px]">
                  <span>礼物</span>
                  <div className="flex flex-wrap gap-[10px]">
                    {userExpand.giftSummary.map((gift, index) => (
                      <div
                        key={index}
                        className="flex flex-col items-center gap-[5px]"
                      >
                        <div className="w-[60px] h-[60px] bg-scaffold-background rounded-[5px] flex items-center justify-center">
                          <div className="w-[36px]">
                            <Image
                              src={gift.image}
                              width="100%"
                              height="auto"
                              fit="cover"
                              placeholder={<DefaultLoadImage />}
                              fallback={<DefaultLoadImage />}
                            />
                          </div>
                        </div>
                        <div className="text-sm text-[#728BA4]">
                          x{gift.number}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <Divider className="border-divider" />
            </>
          ) : null}
          {userExpand?.medals?.length ? (
            <div className="pt-[10px]">
              <div className="flex flex-col gap-[10px]">
                <div
                  className="flex"
                  onClick={isSelf ? handleMedalsClick : undefined}
                >
                  <span className="flex-1">勋章</span>
                  {isSelf ? <ArrowIcon /> : null}
                </div>
                <div className="flex flex-wrap gap-[10px]">
                  {userExpand.medals.map((medal, index) => (
                    <div
                      key={index}
                      className="w-[60px] h-[60px] bg-scaffold-background rounded-[5px] flex items-center justify-center"
                    >
                      <div className="w-[36px]">
                        <Image
                          src={medal.image}
                          width="100%"
                          height="auto"
                          fit="cover"
                          placeholder={<DefaultLoadImage />}
                          fallback={<DefaultLoadImage />}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : null}
          <div className="h-[100px]" />
        </div>
      </PageMain>
      {!isSelf ? (
        <div className="flex border-t border-solid border-divider px-[15px] py-[10px]">
          <div
            className="flex-1 flex flex-col items-center gap-[5px]"
            onClick={handleChatClick}
          >
            <CommonIcon name="chat" w={23} h={22} />
            <span className="text-[13px] text-hint-color">私聊</span>
          </div>
          <div
            className="flex-1 flex flex-col items-center gap-[5px]"
            onClick={handleVideoClick}
          >
            <CommonIcon name="video" w={23} h={22} />
            <span className="text-[13px] text-hint-color">音视频聊</span>
          </div>
          <div
            className="flex-1 flex flex-col items-center gap-[5px]"
            onClick={handleFollowChange}
          >
            <CommonIcon name="follow" w={24} h={21} />
            <span
              className={classNames(
                "text-[13px] text-hint-color",
                userDetailed.hasFollow ? "text-[#FF3460]" : null
              )}
            >
              {userDetailed.hasFollow ? "已关注" : "关注"}
            </span>
          </div>
          <div
            className="flex-1 flex flex-col items-center gap-[5px]"
            onClick={handleSendGiftClick}
          >
            <CommonIcon name="gift" w={23} h={22} />
            <span className="text-[13px] text-hint-color">送礼物</span>
          </div>
        </div>
      ) : null}
      <div className="absolute top-0 left-0 w-full h-[220px] z-[-2]">
        <Image
          src={userDetailed.cover?.images?.Thumbnail?.url}
          width="100%"
          height="100%"
          fit="cover"
          placeholder={<DefaultLoadImage />}
          fallback={<DefaultLoadImage />}
        />
        <div
          className="absolute top-0 left-0 w-full h-full z-[1]"
          style={{
            background: `linear-gradient(to bottom,hsla(var(--app-bar-background)/${opacity}) 0%,hsl(var(--app-bar-background)) 100%)`,
          }}
        />
      </div>
      <GiftModal open={open} onClose={closeModal} onSend={handleSendGift} />
    </PageWrapper>
  );
}
