import { rechargeCostConfig } from "@/lib/api/wallet";
import { queryClient } from "@/provider/query-client";
import { defer, LoaderFunction } from "react-router-dom";

export const loader: LoaderFunction = async () => {
  return defer({
    data: queryClient
      .fetchQuery({
        queryKey: ["recharge-cost-config"],
        queryFn: rechargeCostConfig,
      })
      .catch((err) => err),
  });
};
