import { CommonIcon } from "@/components/common-icon";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { useUserWallet } from "@/hooks/use-user-wallet";
import { RechargeCostResult } from "@/type/recharge-cost-result";
import { AspectRatio } from "@/ui/aspect-ratio";
import { PayBusinessType, userWalletType } from "@/utils/enums";
import classNames from "classnames";
import { useAsyncValue } from "react-router-dom";
import {
  rechargeCostVipBackground,
  rechargeCostVipSelectedBackground,
} from "@/components/image-list";
import { Image } from "antd-mobile";
import { useState } from "react";
import { Button } from "@/ui/button";
import { useConfigList } from "@/hooks/use-config-list";
import { usePay } from "@/hooks/use-pay";

export default function Recharge() {
  const { data } = useAsyncValue() as { data: RechargeCostResult[] };

  const { getBalanceOrEmptyByType } = useUserWallet();
  const { getSystemConfig } = useConfigList();

  const [selectedIndex, setSelectedIndex] = useState(0);

  const { showPay } = usePay({
    type: PayBusinessType.Recharge,
    params: {
      costId: data[selectedIndex].id,
    },
  });

  const handleRecharge = () => {
    showPay();
  };

  return (
    <PageWrapper className="relative z-10 bg-scaffold-background">
      <NavigationBar canBack title="充值" className="bg-transparent" />
      <PageMain>
        <div className="h-[125px] flex justify-around">
          <div className="flex flex-col gap-[10px] items-center justify-center">
            <span className="text-[35px] text-white">
              {getBalanceOrEmptyByType(userWalletType.currency.value)}
            </span>
            <div className="flex gap-[5px] items-center">
              <CommonIcon name="gold" h={23} w={23} />
              <span className="text-base text-white">
                当前{userWalletType.currency.label}
              </span>
            </div>
          </div>
        </div>
        <div className="flex flex-col rounded-t-[10px] bg-scaffold-background px-[15px] py-[10px]">
          <span className="p-[15px] text-[13px] text-hint-color">
            请选择充值套餐
          </span>
          <div className="grid grid-cols-2 gap-[15px]">
            {data.map((item, index) => (
              <AspectRatio key={item.id} ratio={1.5}>
                <div
                  className={classNames(
                    "w-full h-full relative flex justify-center items-center rounded-[5px] bg-app-bar-background border border-solid",
                    selectedIndex === index
                      ? "border-[#9D64FF] bg-[#9D64FF]/10"
                      : "border-app-bar-background bg-app-bar-background"
                  )}
                  onClick={() => setSelectedIndex(index)}
                >
                  <div className="flex flex-col items-center justify-center">
                    <span
                      className={classNames(
                        "text-[17px]",
                        selectedIndex === index ? "text-[#9D64FF]" : ""
                      )}
                    >
                      ￥{item.presentPrice}
                    </span>
                    <span className="text-sm text-hint-color">
                      {item.number}
                      {
                        Object.values(userWalletType).find(
                          (type) => type.value === item.walletType
                        )?.label
                      }
                    </span>
                    {item.giveNumber && item.giveNumber > 0 ? (
                      <span className="text-sm text-hint-color">
                        普通用户送{item.giveNumber}
                        {
                          Object.values(userWalletType).find(
                            (type) => type.value === item.walletType
                          )?.label
                        }
                      </span>
                    ) : null}
                  </div>
                  {item.vipGiveNumber && item.vipGiveNumber > 0 ? (
                    <div className="absolute top-0 right-0 h-[23px] z-10">
                      <Image
                        src={
                          selectedIndex === index
                            ? rechargeCostVipSelectedBackground
                            : rechargeCostVipBackground
                        }
                        width="100%"
                        height="100%"
                        placeholder={null}
                        fallback={null}
                        className="absolute z-[-1] top-0 right-0"
                      />
                      <span className="text-white text-[13px] pl-[10px] pr-[5px]">
                        VIP再送{item.vipGiveNumber}
                        {
                          Object.values(userWalletType).find(
                            (type) => type.value === item.walletType
                          )?.label
                        }
                      </span>
                    </div>
                  ) : null}
                </div>
              </AspectRatio>
            ))}
          </div>
          <div className="py-[25px]">
            <Button
              variant="primary"
              size="lg"
              className="w-full"
              onClick={handleRecharge}
            >
              充值
            </Button>
          </div>
          <div className="flex flex-col gap-[10px]">
            <span className="text-sm text-hint-color">说明</span>
            <span className="text-xs text-hint-color whitespace-pre-wrap">
              {getSystemConfig("WALLET_RECHARGE_TIPS")}
            </span>
          </div>
        </div>
      </PageMain>
      <div className="absolute left-0 top-0 w-full h-[200px] withdrawal-bg z-[-1]" />
    </PageWrapper>
  );
}
