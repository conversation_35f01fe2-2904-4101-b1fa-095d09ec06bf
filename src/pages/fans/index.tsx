import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { UserInfo } from "@/components/work/user-info";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { fansList } from "@/lib/api/user";
import { PageParams } from "@/type";
import { Divider } from "antd-mobile";
import { useSearchParams } from "react-router-dom";
import { Fragment } from "react/jsx-runtime";

export default function FansPage() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id") ?? undefined;
  const queryKey = ["fans-page", id];

  const queryFn = (params: PageParams) => {
    return fansList({ userId: id }, params);
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  return (
    <PageWrapper>
      <NavigationBar canBack title="粉丝列表" />
      <PageMain
        onRefresh={handleRefresh}
        className="bg-app-bar-background py-[15px]"
      >
        <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="flex flex-col">
              {data.map((userInfo, index) => (
                <Fragment key={userInfo.id}>
                  <div className="px-[15px]">
                    <UserInfo user={userInfo} size={50} />
                  </div>
                  {index !== data.length - 1 ? (
                    <Divider className="border-divider" />
                  ) : null}
                </Fragment>
              ))}
            </div>
          )}
        </ScrollLoadData>
      </PageMain>
    </PageWrapper>
  );
}
