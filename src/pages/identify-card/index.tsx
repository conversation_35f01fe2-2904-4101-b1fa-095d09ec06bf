import { CommonIcon } from "@/components/common-icon";
import { DefaultLoadImage } from "@/components/default-load-image";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { confirmDialog } from "@/components/work/confirm-dialog";
import { useConfigList } from "@/hooks/use-config-list";
import { useCountDownTimer } from "@/hooks/use-count-down-timer";
import { smsSendCode } from "@/lib/api/message";
import { UserIdentifyCardResult } from "@/type/user-identify-card-result";
import { Button } from "@/ui/button";
import { Form, FormControl, FormField, FormItem } from "@/ui/form";
import { Input } from "@/ui/input";
import { extractLeadingNumbers } from "@/utils";
import { useMutation } from "@tanstack/react-query";
import { Dialog, Image, Toast } from "antd-mobile";
import { useForm } from "react-hook-form";
import { useAsyncValue, useRevalidator } from "react-router-dom";
import { createIdentifyCard } from "@/lib/api/user";
import { useRef } from "react";
import { domToPng } from "modern-screenshot";
import "./index.less";
import { phoneRegex } from "@/utils/constant";

export default function IdentifyCardPage() {
  const { data: identifyCardData } = useAsyncValue() as {
    data: UserIdentifyCardResult;
  };
  const { revalidate } = useRevalidator();

  const { getSystemConfig } = useConfigList();

  const sendCodeMutation = useMutation({ mutationFn: smsSendCode });

  const identifyCardRef = useRef<HTMLDivElement>(null);

  const form = useForm<{ phone?: string; code?: string }>();
  const { isRunning, seconds, handleStart } = useCountDownTimer(60);

  const createMutation = useMutation({ mutationFn: createIdentifyCard });

  const checkPhone = async () => {
    const { phone } = form.getValues();

    if (!phone) {
      Toast.show({ content: "请输入手机号" });
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(phone)) {
      Toast.show({ content: "请输入正确的手机号" });
      return;
    }
    // 发送验证码
    Toast.show({
      icon: "loading",
      content: "加载中…",
      duration: 0,
    });

    const { ok, msg } = await sendCodeMutation.mutateAsync(phone);

    if (ok) {
      handleStart();
    } else {
      Toast.show({ content: msg });
    }
  };

  const handleCreate = async () => {
    const result = await confirmDialog.show(
      "您已创建身份卡，点击确定后之前的身份卡将不再可用!",
      "是否创建身份卡？"
    );

    if (!result) {
      return;
    }

    if (identifyCardData.bindPhone) {
      // 手机号验证模式
      Dialog.alert({
        title: "手机号验证",
        closeOnMaskClick: true,
        bodyClassName: "bg-scaffold-background identify-card-phone-dialog",
        content: (
          <Form {...form}>
            <div className="flex flex-col gap-8">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="flex items-center relative z-0">
                        <span className="absolute ml-[5px] z-[-1]">+86</span>
                        <Input
                          placeholder="请输入手机号"
                          className="pl-[45px] pr-[70px]"
                          maxLength={11}
                          {...field}
                          autoComplete="off"
                          value={extractLeadingNumbers(field.value)}
                        />
                        {!isRunning ? (
                          <Button
                            variant="primary"
                            size="sm"
                            className="absolute right-0"
                            onClick={checkPhone}
                          >
                            发送
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            variant="secondary"
                            className="absolute right-0"
                          >
                            {seconds}
                          </Button>
                        )}
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormControl>
                        <Input
                          placeholder="请输入验证码"
                          maxLength={6}
                          {...field}
                          autoComplete="off"
                          value={extractLeadingNumbers(field.value)}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
            </div>
          </Form>
        ),
        confirmText: (
          <Button variant="primary" size="lg" className="w-full">
            生成身份卡
          </Button>
        ),
        onConfirm: submitCreate,
      });

      return;
    }

    onCreateIdentifyCard();
  };

  const submitCreate = () => {
    const { phone, code } = form.getValues();

    if (!phone) {
      Toast.show({ content: "请输入手机号" });
      throw new Error();
    }

    if (!phoneRegex.test(phone)) {
      Toast.show({ content: "请输入正确的手机号" });
      throw new Error();
    }

    if (!code) {
      Toast.show({ content: "请输入验证码" });
      throw new Error();
    }

    onCreateIdentifyCard({ phone, code });
  };

  const onCreateIdentifyCard = async (info?: {
    phone: string;
    code: string;
  }) => {
    Toast.show({
      icon: "loading",
      content: "加载中...",
      duration: 0,
    });

    const { ok } = await createMutation.mutateAsync(info);

    if (ok) {
      revalidate();
    }
  };

  const handleSave = () => {
    if (!identifyCardRef.current) {
      return;
    }

    domToPng(identifyCardRef.current).then((dataUrl) => {
      const link = document.createElement("a");
      link.href = dataUrl;
      link.download = "身份卡.png";
      link.click(); // 模拟点击下载
    });
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="身份卡" />
      <PageMain className="p-[15px] bg-scaffold-background">
        <div className="flex flex-col gap-[10px]">
          <span className="text-sm">身份卡</span>
          <div
            className="p-[15px] bg-white flex justify-center items-center"
            ref={identifyCardRef}
          >
            {identifyCardData.qrcode ? (
              <div>
                <div className="flex w-full gap-[15px]">
                  <CommonIcon name="logo" w={50} h={50} />
                  <div className="flex-1 flex flex-col justify-evenly">
                    <span className="text-[15px] font-bold text-black">
                      {getSystemConfig("APP_SHOW_NAME")}身份卡
                    </span>
                    <span className="text-[13px] text-[#BABCBE]">
                      通过扫描此身份卡即可登录
                    </span>
                  </div>
                  <div className="w-[50px] h-[50px]">
                    <Image
                      src={identifyCardData.qrcode}
                      width="100%"
                      height="100%"
                      fit="contain"
                      placeholder={<DefaultLoadImage />}
                      fallback={<DefaultLoadImage />}
                    />
                  </div>
                </div>
                <div className="flex text-[13px] text-[#BABCBE] pt-[5px] whitespace-pre-wrap text-center">
                  {identifyCardData.tips}
                </div>
              </div>
            ) : (
              <span className="text-[#BABCBE]">
                暂未启用身份卡，请点击下方按钮启用
              </span>
            )}
          </div>
        </div>
        <div className="mt-[10px] flex flex-col gap-[20px]">
          <div className="flex flex-col gap-[10px]">
            <span className="text-sm">说明</span>
            <div className="whitespace-pre-wrap">
              {getSystemConfig("USER_IDENTIFY_CARD_NOTE")}
            </div>
          </div>
          <div>
            <Button
              variant="primary"
              size="lg"
              className="w-full"
              onClick={handleCreate}
            >
              {identifyCardData.qrcode ? "更换身份卡" : "创建身份卡"}
            </Button>
          </div>
          <div>
            <Button
              variant="secondary"
              size="lg"
              className="w-full"
              onClick={handleSave}
            >
              保存身份卡
            </Button>
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
