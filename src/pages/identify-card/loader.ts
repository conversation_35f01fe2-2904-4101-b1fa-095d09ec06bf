import { getIdentifyCard } from "@/lib/api/user";
import { queryClient } from "@/provider/query-client";
import { defer, LoaderFunction } from "react-router-dom";

export const identifyCardQueryKey = ["identify-card"];

export const loader: LoaderFunction = async () => {
  return defer({
    data: queryClient
      .fetchQuery({
        queryKey: identifyCardQueryKey,
        queryFn: getIdentifyCard,
      })
      .catch((err) => err),
  });
};
