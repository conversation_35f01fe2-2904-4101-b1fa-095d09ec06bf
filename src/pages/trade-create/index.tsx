import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { MediaUpload } from "@/components/work/media-upload";
import { Button } from "@/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/ui/form";
import { Input } from "@/ui/input";
import { Textarea } from "@/ui/textarea";
import { extractLeadingNumbers } from "@/utils";
import { UserTransactionType, userTransactionTypeConfig } from "@/utils/enums";
import { useForm } from "react-hook-form";
import { useSearchParams } from "react-router-dom";
import { useMutation } from "@tanstack/react-query";
import {
  userTransactionPublishCheck,
  userTransactionPublishUrl,
} from "@/lib/api/transaction";
import { useUploadQueueManager } from "@/hooks/use-upload-queue-manager";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { Toast } from "antd-mobile";
import { getRandomParams } from "@/utils/common";
import { EnabledLocation } from "@/components/work/enabled-location";

export default function TradeCreatePage() {
  const [searchParams] = useSearchParams();
  const type = (searchParams.get("type") ||
    `${UserTransactionType.出售}`) as unknown as UserTransactionType;

  const form = useForm();

  const { mutateAsync } = useMutation({
    mutationFn: userTransactionPublishCheck,
  });

  const { addToQueue } = useUploadQueueManager();
  const { navigateBack } = useNavigateRoute();

  const handleSubmit = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const data = form.getValues();

    const { cover, resources, ...rest } = data;

    const { ok } = await mutateAsync({
      type: Number(type),
      cover: cover ? getRandomParams() : undefined,
      resources: resources ? resources.map(() => getRandomParams()) : undefined,
      ...rest,
    });

    if (ok) {
      addToQueue(
        {
          title: "交易发布",
          url: userTransactionPublishUrl,
          params: {
            type: Number(type),
            ...rest,
            cover: cover?.[0],
            resources,
          },
        },
        () => {
          navigateBack();
        }
      );
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title={`发布${userTransactionTypeConfig[type]}`} />
      <PageMain>
        <div className="p-[15px]">
          <Form {...form}>
            <div className="flex flex-col gap-[20px]">
              <FormField
                control={form.control}
                name="cover"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>封面</FormLabel>
                      <FormControl>
                        <MediaUpload maxCount={1} {...field} />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>
                        {userTransactionTypeConfig[type]}标题
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          autoComplete="off"
                          placeholder={`请输入${userTransactionTypeConfig[type]}标题`}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="resources"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>资源</FormLabel>
                      <FormControl>
                        <MediaUpload maxCount={9} {...field} />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>
                        {userTransactionTypeConfig[type]}描述
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          autoComplete="off"
                          rows={5}
                          className="resize-none rounded-[10px] px-[15px] py-[10px]"
                          placeholder={`请输入${userTransactionTypeConfig[type]}描述`}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>
                        {Number(type) === UserTransactionType.出售
                          ? "价格"
                          : "预算"}
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          autoComplete="off"
                          maxLength={10}
                          placeholder={`请输入${
                            Number(type) === UserTransactionType.出售
                              ? "价格"
                              : "预算"
                          }`}
                          value={extractLeadingNumbers(field.value)}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              {Number(type) === UserTransactionType.出售 ? (
                <FormField
                  control={form.control}
                  name="freightPrice"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel>运费</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            maxLength={10}
                            autoComplete="off"
                            placeholder="请输入运费"
                            value={extractLeadingNumbers(field.value)}
                          />
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
              ) : null}
              <FormField
                control={form.control}
                name="deposit"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>保证金</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          maxLength={10}
                          autoComplete="off"
                          placeholder="请输入保证金"
                          value={extractLeadingNumbers(field.value)}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="stock"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>库存</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          maxLength={10}
                          autoComplete="off"
                          placeholder="请输入库存"
                          value={extractLeadingNumbers(field.value)}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="enabledLocation"
                render={({ field }) => {
                  return (
                    <FormItem className="flex justify-between items-center">
                      <FormLabel>位置信息</FormLabel>
                      <FormControl>
                        <EnabledLocation {...field} />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
            </div>
          </Form>
          <div className="my-[40px]">
            <Button variant="primary" className="w-full" onClick={handleSubmit}>
              立即发布
            </Button>
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
