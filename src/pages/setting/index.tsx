import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ArrowIcon } from "@/components/svg-icon";
import { ThemeIcon } from "@/components/theme-icon";
import { useTheme } from "@/provider/useTheme";
import { LayoutConfigSetting, SettingItem } from "@/type/layout-config";
import { Button } from "@/ui/button";
import { Dialog } from "antd-mobile";
import { useAsyncValue } from "react-router-dom";
import Cookies from "js-cookie";
import "./index.less";
import { queryClient } from "@/provider/query-client";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { getRoute } from "@/router/route-map";

const formatIcon = (icon: SettingItem["icon"], theme: string) => {
  const url = theme === "light" ? icon.light : icon.dark;

  // 通用格式化为 menus/xxx，支持字母、数字、下划线和连字符
  return url.replace(/.*\/(settings\/[\w-]+)\.png$/, "$1");
};

export default function Setting() {
  const { data: layoutConfig } = useAsyncValue() as {
    data: LayoutConfigSetting;
  };

  const { navigateRoute } = useNavigateRoute();

  const theme = useTheme();

  const confirmLogout = () => {
    // 退出登录
    // 清除token
    Cookies.remove("token");
    // 清空 react-query 缓存
    queryClient.clear();
    // 跳转到登录页面
    navigateRoute("/login");
  };

  const handleLogout = () => {
    Dialog.confirm({
      title: "提示",
      closeOnMaskClick: true,
      bodyClassName: "bg-scaffold-background setting-logout-dialog",
      content: <div className="text-center text-foreground">是否退出登录</div>,
      confirmText: <span className="text-[#FF005C]">确认</span>,
      cancelText: <span className="text-foreground">取消</span>,
      onConfirm: confirmLogout,
    });
  };

  const handleMenuClick = (uri: string) => {
    navigateRoute(getRoute(uri));
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="设置" />
      <PageMain>
        <div className="flex flex-col px-[15px]">
          {layoutConfig.items.map((item) => (
            <div
              key={item.uri}
              className="flex gap-[15px] items-center py-[10px]"
              onClick={() => handleMenuClick(item.uri)}
            >
              <ThemeIcon name={formatIcon(item.icon, theme)} w={23} h={23} />
              <div className="flex-1 flex flex-col">
                <span className="text-base">{item.title}</span>
                <span className="text-[13px] text-[#c2c2c2]">
                  {item.subtitle}
                </span>
              </div>
              <ArrowIcon />
            </div>
          ))}
          <div className="px-[15px] mt-[50px]">
            <Button
              variant="secondary"
              size="lg"
              className="w-full"
              onClick={handleLogout}
            >
              退出登录
            </Button>
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
