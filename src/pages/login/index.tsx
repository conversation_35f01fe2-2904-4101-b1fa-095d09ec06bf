import { NavigationBar } from "@/components/navigation-bar";
import { But<PERSON> } from "@/ui/button";
import { LoginMessage } from "./components/login-message";
import { ThirdLogin } from "./components/third-login";
import { LoginMethod } from "@/utils/enums";
import { useMemo } from "react";
import { useLoginMethod } from "@/hooks/use-login-method";
import { PageWrapper } from "@/components/page-wrapper";
import { PageMain } from "@/components/page-main";
import { loginWelcomeMsg } from "@/utils/constant";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { ThemeIcon } from "@/components/theme-icon";
import { useLoginAction } from "./hooks/use-login-action";

export default function Login() {
  const { navigateRoute } = useNavigateRoute();
  const { isSupportLoginMethod } = useLoginMethod();
  const { handleCustomerServiceClick } = useLoginAction();

  const renderLogin = useMemo(() => {
    if (!isSupportLoginMethod(LoginMethod.手机号快捷登录)) {
      // 短信验证码/密码登录入口，当手机号快捷登录不可用时，此内容晋升为主按钮
      return isSupportLoginMethod(LoginMethod.手机短信验证码登录) ? (
        <Button
          variant="primary"
          size="lg"
          onClick={() => navigateRoute("/login/code")}
        >
          短信/密码登录
        </Button>
      ) : (
        <Button
          variant="primary"
          size="lg"
          onClick={() => navigateRoute("/login/pwd")}
        >
          短信/密码登录
        </Button>
      );
    }

    return isSupportLoginMethod(LoginMethod.手机短信验证码登录) ? (
      <Button
        variant="secondary"
        size="lg"
        onClick={() => navigateRoute("/login/code")}
      >
        其他号码登录
      </Button>
    ) : (
      <Button
        variant="secondary"
        size="lg"
        onClick={() => navigateRoute("/login/pwd")}
      >
        其他号码登录
      </Button>
    );
  }, [isSupportLoginMethod, navigateRoute]);

  return (
    <PageWrapper>
      <NavigationBar
        action={
          <span onClick={handleCustomerServiceClick}>
            <ThemeIcon className="items-end" name="customer-service" />
          </span>
        }
      />
      <PageMain>
        <div className="min-h-[617px] flex flex-col">
          <LoginMessage msg={loginWelcomeMsg} />
          <div className="flex flex-col mx-[25px] gap-8 font-medium">
            {/* {isSupportLoginMethod(LoginMethod.手机号快捷登录) ? (
              <Button variant="primary" size="lg">
                本机号码一键登录
              </Button>
            ) : null} */}
            {renderLogin}
          </div>
          <ThirdLogin />
        </div>
      </PageMain>
    </PageWrapper>
  );
}
