import { useConfigList } from "@/hooks/use-config-list";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { useLoginInfo } from "@/store/login-info";
import { UserLoginResult } from "@/type/user-login-result";
import { getDeviceId } from "@/utils";

export const useLoginAction = () => {
  const { navigateRoute } = useNavigateRoute();

  const { getSystemConfig } = useConfigList();
  const { setLoginInfo } = useLoginInfo();
  const { data: userInfo } = useFullUserInfo();

  const userSexInfoShow = getSystemConfig("USER_SEX_INFO_SHOW");

  const loginAfterAction = (data: UserLoginResult) => {
    // 缓存登录信息
    setLoginInfo({
      loginData: data,
    });

    if (!data.bindPhone) {
      // 如果没有绑定手机，则跳转到绑定手机页面
      // 暂无此页面
      return navigateRoute("/login/bind-phone");
    }

    if (!data.initialization) {
      // 如果没有初始化信息
      if (data.data.gender === undefined) {
        navigateRoute("/login/gender-info");
        return;
      }

      if (userSexInfoShow === "1") {
        navigateRoute("/login/tag-info");
        return;
      }

      navigateRoute("/login/basic-info");
    }

    // 进入首页
    return navigateRoute("/home");
  };

  const handleCustomerServiceClick = () => {
    let url = getSystemConfig("OFFICIAL_CUSTOMER_SERVICE_TARGET");

    if (!url) {
      return;
    }

    let data: Record<string, string> = userInfo as Record<string, string>;
    if (!data) {
      data = { id: getDeviceId(), nickname: "未登录用户", avatarUrl: "" };
    }

    for (const key in data) {
      url = url?.replace(`\${${key}}`, data[key]);
    }

    window.open(url, "_blank");
  };

  return { loginAfterAction, handleCustomerServiceClick };
};
