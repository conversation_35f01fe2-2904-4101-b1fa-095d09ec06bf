import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { bindPhoneMsg, phoneRegex } from "@/utils/constant";
import { LoginMessage } from "../../components/login-message";
import { Form, FormControl, FormField, FormItem } from "@/ui/form";
import { useForm } from "react-hook-form";
import { Input } from "@/ui/input";
import { extractLeadingNumbers } from "@/utils";
import { Button } from "@/ui/button";
import { useCountDownTimer } from "@/hooks/use-count-down-timer";
import { Toast } from "antd-mobile";
import { useMutation } from "@tanstack/react-query";
import { smsSendCode } from "@/lib/api/message";
import { loginBindPhone } from "@/lib/api/user";
import { useLoginAction } from "../../hooks/use-login-action";
import { ThemeIcon } from "@/components/theme-icon";

export default function BindPhonePage() {
  const form = useForm<{ phone?: string; code?: string }>();

  const sendCodeMutation = useMutation({ mutationFn: smsSendCode });
  const bindPhoneMutation = useMutation({ mutationFn: loginBindPhone });

  const { isRunning, seconds, handleStart } = useCountDownTimer(60);

  const { loginAfterAction, handleCustomerServiceClick } = useLoginAction();

  const checkPhone = async () => {
    const { phone } = form.getValues();

    if (!phone) {
      Toast.show({ content: "请输入手机号" });
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(phone)) {
      Toast.show({ content: "请输入正确的手机号" });
      return;
    }
    // 发送验证码
    Toast.show({
      icon: "loading",
      content: "加载中…",
      duration: 0,
    });

    const { ok, msg } = await sendCodeMutation.mutateAsync(phone);

    if (ok) {
      handleStart();
    } else {
      Toast.show({ content: msg });
    }
  };

  const handleSumbit = async () => {
    const { phone, code } = form.getValues();

    if (!phone) {
      Toast.show({ content: "请输入手机号" });
      return;
    }

    if (!phoneRegex.test(phone)) {
      Toast.show({ content: "请输入正确的手机号" });
      return;
    }

    if (!code) {
      Toast.show({ content: "请输入验证码" });
      return;
    }

    Toast.show({
      icon: "loading",
      content: "加载中…",
      duration: 0,
    });

    const { ok, data } = await bindPhoneMutation.mutateAsync({
      token: "",
      phone,
      code,
    });

    if (ok && data) {
      loginAfterAction(data);
    }
  };

  return (
    <PageWrapper>
      <NavigationBar
        title="绑定手机"
        canBack
        action={
          <span onClick={handleCustomerServiceClick}>
            <ThemeIcon className="items-end" name="customer-service" />
          </span>
        }
      />
      <PageMain>
        <div className="min-h-[617px] flex flex-col">
          <LoginMessage msg={bindPhoneMsg} />
          <div className="mx-[25px] flex flex-col">
            <Form {...form}>
              <div className="flex flex-col gap-5">
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="flex items-center relative z-0">
                          <span className="absolute ml-[5px] z-[-1]">+86</span>
                          <Input
                            placeholder="请输入手机号"
                            className="pl-[45px] pr-[70px]"
                            maxLength={11}
                            {...field}
                            autoComplete="off"
                            value={extractLeadingNumbers(field.value)}
                          />
                          {!isRunning ? (
                            <Button
                              variant="primary"
                              size="sm"
                              className="absolute right-0"
                              onClick={checkPhone}
                            >
                              发送
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="secondary"
                              className="absolute right-0"
                            >
                              {seconds}
                            </Button>
                          )}
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormControl>
                          <Input
                            placeholder="请输入验证码"
                            maxLength={6}
                            {...field}
                            autoComplete="off"
                            value={extractLeadingNumbers(field.value)}
                          />
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
              </div>
            </Form>
            <Button
              className="mt-[40px]"
              variant="primary"
              size="lg"
              onClick={handleSumbit}
            >
              绑定
            </Button>
            <span className="mt-[20px] text-center text-hint-color">
              根据平台监管要求，所有用户必须绑定手机号，我们不会以任何形式泄露您的信息，请放心绑定！
            </span>
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
