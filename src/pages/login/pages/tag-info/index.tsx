import { NavigationBar } from "@/components/navigation-bar";
import { extentConfig, roleConfig, sexualConfig } from "@/utils/enums";
import { TagItem } from "../../components/tag-item";
import { But<PERSON> } from "@/ui/button";
import { PageWrapper } from "@/components/page-wrapper";
import { PageMain } from "@/components/page-main";
import { useLoginInfo } from "@/store/login-info";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { ThemeIcon } from "@/components/theme-icon";
import { useLoginAction } from "../../hooks/use-login-action";

export default function GenderInfo() {
  const { loginInfo, setLoginInfo } = useLoginInfo();

  const { navigateRoute } = useNavigateRoute();
  const { handleCustomerServiceClick } = useLoginAction();

  const disabled =
    loginInfo.role === undefined ||
    loginInfo.sexualOrientation === undefined ||
    loginInfo.extent === undefined;

  return (
    <PageWrapper>
      <NavigationBar
        canBack
        action={
          <span onClick={handleCustomerServiceClick}>
            <ThemeIcon className="items-end" name="customer-service" />
          </span>
        }
      />
      <PageMain>
        <div className="flex flex-col mx-[18px]">
          <div className="mt-[24px] flex flex-col gap-4 mb-[44px]">
            <span className="text-2xl font-medium">请选择几个标签描述你</span>
          </div>
          <div className="flex flex-col gap-9 mb-[60px]">
            <TagItem
              title="角色"
              config={roleConfig}
              value={loginInfo.role}
              onChange={(role) => setLoginInfo({ role })}
            />
            <TagItem
              title="性取向"
              config={sexualConfig}
              value={loginInfo.sexualOrientation}
              onChange={(sexualOrientation) =>
                setLoginInfo({ sexualOrientation })
              }
            />
            <TagItem
              title="程度"
              config={extentConfig}
              value={loginInfo.extent}
              onChange={(extent) => setLoginInfo({ extent })}
            />
          </div>
          <Button
            variant="primary"
            size="lg"
            disabled={disabled}
            onClick={() => navigateRoute("/login/basic-info")}
          >
            下一步
          </Button>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
