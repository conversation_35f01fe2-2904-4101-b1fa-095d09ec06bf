import classNames from "classnames";
import { CommonIcon } from "@/components/common-icon";
import { NavigationBar } from "@/components/navigation-bar";
import { genderConfig } from "@/utils/enums";
import { Image } from "antd-mobile";
import { Button } from "@/ui/button";
import { PageWrapper } from "@/components/page-wrapper";
import { PageMain } from "@/components/page-main";
import { useLoginInfo } from "@/store/login-info";
import { useConfigList } from "@/hooks/use-config-list";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { ThemeIcon } from "@/components/theme-icon";
import { useLoginAction } from "../../hooks/use-login-action";

export default function GenderInfo() {
  const { loginInfo, setLoginInfo } = useLoginInfo();

  const { navigateRoute } = useNavigateRoute();
  const { getSystemConfig } = useConfigList();
  const { handleCustomerServiceClick } = useLoginAction();

  const userSexInfoShow = getSystemConfig("USER_SEX_INFO_SHOW");

  const handleSubmit = () => {
    if (userSexInfoShow === "1") {
      navigateRoute("/login/tag-info");
    } else {
      navigateRoute("/login/basic-info");
    }
  };

  return (
    <PageWrapper>
      <NavigationBar
        canBack
        action={
          <span onClick={handleCustomerServiceClick}>
            <ThemeIcon className="items-end" name="customer-service" />
          </span>
        }
      />
      <PageMain>
        <div className="flex flex-col mx-[18px]">
          <div className="mt-[24px] flex flex-col gap-3 mb-[67px]">
            <span className="text-2xl font-medium">您的性别是？</span>
            <span className="text-[#9AA1B1] text-base">
              选择后将不能修改哦～请您谨慎选择
            </span>
          </div>
          <div className="flex gap-3 mb-[88px]">
            {genderConfig.map((gender) => (
              <div
                key={gender.value}
                className={classNames(
                  "group flex-1 flex flex-col gap-4 items-center",
                  loginInfo.gender === gender.value ? "active" : ""
                )}
                onClick={() => setLoginInfo({ gender: gender.value })}
              >
                <div className="relative">
                  <Image src={gender.url} alt="" width="100%" height="auto" />
                  {loginInfo.gender === gender.value ? (
                    <span className="absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]">
                      <CommonIcon name="check-circle" w={32} h={32} />
                    </span>
                  ) : null}
                </div>
                <span className="text-base group-[.active]:text-[#F1BE67]">
                  {gender.title}
                </span>
              </div>
            ))}
          </div>
          <Button
            variant="primary"
            size="lg"
            disabled={loginInfo.gender === undefined}
            onClick={handleSubmit}
          >
            下一步
          </Button>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
