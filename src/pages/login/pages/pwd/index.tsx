import { NavigationBar } from "@/components/navigation-bar";
import { LoginMessage } from "../../components/login-message";
import { PwdLogin } from "../../components/pwd-login";
import { ThirdLogin } from "../../components/third-login";
import { PageWrapper } from "@/components/page-wrapper";
import { PageMain } from "@/components/page-main";
import { loginWelcomeMsg } from "@/utils/constant";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { ThemeIcon } from "@/components/theme-icon";
import { useLoginAction } from "../../hooks/use-login-action";

export default function PwdLoginPage() {
  const { navigateRoute } = useNavigateRoute();
  const { handleCustomerServiceClick } = useLoginAction();

  const handleChangeCode = () => {
    navigateRoute("/login/code", {}, { replace: true });
  };

  return (
    <PageWrapper>
      <NavigationBar
        title="用户名密码登录"
        canBack
        action={
          <span onClick={handleCustomerServiceClick}>
            <ThemeIcon className="items-end" name="customer-service" />
          </span>
        }
      />
      <PageMain>
        <div className="min-h-[617px] flex flex-col">
          <LoginMessage msg={loginWelcomeMsg} />
          <div className="mx-[25px]">
            <PwdLogin onChangeMode={handleChangeCode} />
          </div>
          <ThirdLogin />
        </div>
      </PageMain>
    </PageWrapper>
  );
}
