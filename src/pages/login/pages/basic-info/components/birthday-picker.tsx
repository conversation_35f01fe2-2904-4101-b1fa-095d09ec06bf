import { useState } from "react";
import { DatePicker } from "antd-mobile";
import dayjs from "dayjs";
import { Input } from "@/ui/input";

interface Props {
  value?: string;
  onChange?: (value: string) => void;
}

export const BirthdayPicker = (props: Props) => {
  const { value, onChange } = props;

  const [visible, setVisible] = useState(false);

  const formatDate = value ? dayjs(value).toDate() : undefined;

  const min = new Date(1970, 0, 1);
  const max = dayjs().subtract(18, "year").toDate();

  return (
    <div>
      <Input
        placeholder="请选择生日时间"
        value={value}
        onClick={() => setVisible(true)}
      />
      <DatePicker
        className="text-black"
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        min={min}
        max={max}
        cancelText={<span className="text-[#999]">取消</span>}
        confirmText={<span className="text-[#9d64ff]">确认</span>}
        onConfirm={(val) => {
          onChange?.(dayjs(val).format("YYYY-MM-DD"));
        }}
        value={formatDate}
      />
    </div>
  );
};
