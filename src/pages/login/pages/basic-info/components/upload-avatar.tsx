import { forwardRef, useRef } from "react";
import { ImageUploader, ImageUploaderRef } from "antd-mobile";
import { uploadFile } from "@/utils/upload-file";
import { useUploadInfo } from "@/hooks/use-upload-info";
import { Avatar } from "@/components/avatar";

interface Props {
  value?: {
    id?: string;
    url?: string;
  };
  onChange?: (value: { id: string; url: string }) => void;
}

export const UploadAvatar = forwardRef<HTMLInputElement, Props>(
  (props, ref) => {
    const { value, onChange } = props;

    const { imageUploadInfo } = useUploadInfo();

    const inputRef = useRef<ImageUploaderRef>(null);

    return (
      <div ref={ref}>
        <Avatar
          src={value?.url ?? ""}
          size={83}
          onClick={() => inputRef.current?.nativeElement?.click()}
        />
        <ImageUploader
          ref={inputRef}
          className="hidden"
          upload={async (file) => {
            const res = await uploadFile(file, imageUploadInfo!, {
              showDialog: true,
            });
            onChange?.({
              id: res?.id ?? "",
              url: res?.images?.Original?.url ?? "",
            });

            return {
              url: res?.images?.Original?.url ?? "",
            };
          }}
        />
      </div>
    );
  }
);
