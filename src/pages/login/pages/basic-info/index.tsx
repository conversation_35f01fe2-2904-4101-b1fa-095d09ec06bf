import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { NavigationBar } from "@/components/navigation-bar";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/form";
import { Input } from "@/ui/input";
import { Button } from "@/ui/button";
import { PageWrapper } from "@/components/page-wrapper";
import { PageMain } from "@/components/page-main";
import { useLoginInfo } from "@/store/login-info";
import { UploadAvatar } from "./components/upload-avatar";
import { useDefaultResource } from "@/hooks/use-default-avatar";
import { BirthdayPicker } from "./components/birthday-picker";
import { loginInitialization } from "@/lib/api/user";
import { useMutation } from "@tanstack/react-query";
import { useLoginAction } from "../../hooks/use-login-action";
import { Toast } from "antd-mobile";
import { ThemeIcon } from "@/components/theme-icon";

const formSchema = z.object({
  avatar: z.object({
    id: z.string(),
    url: z.string(),
  }),
  nickname: z.string({ message: "" }),
  birthday: z.string({ message: "" }),
  inviteCode: z.string({ message: "" }).optional(),
});

export default function BasicInfo() {
  const { loginInfo } = useLoginInfo();
  const { getDefaultAvatar } = useDefaultResource();
  const { loginAfterAction, handleCustomerServiceClick } = useLoginAction();

  const { data: user, token } = loginInfo.loginData ?? {};
  const defaultAvatar = getDefaultAvatar();

  const initializationMutation = useMutation({
    mutationFn: loginInitialization,
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      avatar: user?.avatar
        ? { id: user.avatar, url: user.avatarUrl }
        : { id: defaultAvatar?.id, url: defaultAvatar?.images?.Original?.url },
      nickname: user?.nickname,
    },
  });

  const handleSubmit = async (values: z.infer<typeof formSchema>) => {
    const { gender, role, sexualOrientation, extent } = loginInfo;

    if (!token) {
      return;
    }

    Toast.show({
      icon: "loading",
      content: "请求中…",
      duration: 0,
    });

    const { ok, data } = await initializationMutation.mutateAsync({
      token,
      gender,
      role,
      sexualOrientation,
      extent,
      ...values,
      avatar: values.avatar.id,
    });

    if (ok && data) {
      loginAfterAction(data);
    }
  };

  return (
    <PageWrapper>
      <NavigationBar
        canBack
        title="完善资料"
        action={
          <span onClick={handleCustomerServiceClick}>
            <ThemeIcon className="items-end" name="customer-service" />
          </span>
        }
      />
      <PageMain>
        <div className="flex flex-col gap-6 mx-[15px] mt-5">
          <Form {...form}>
            <form
              className="flex flex-col"
              onSubmit={form.handleSubmit(handleSubmit)}
            >
              <div className="flex flex-col gap-8">
                <FormField
                  control={form.control}
                  name="avatar"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormControl>
                          <div className="flex flex-col items-center gap-2">
                            <UploadAvatar {...field} />
                            <span className="text-[#999]">点击更换头像</span>
                          </div>
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
                <FormField
                  control={form.control}
                  name="nickname"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel>昵称</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="请输入昵称"
                            autoComplete="off"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
                <FormField
                  control={form.control}
                  name="birthday"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel>生日</FormLabel>
                        <FormControl>
                          <div>
                            <BirthdayPicker {...field} />
                          </div>
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
                <FormField
                  control={form.control}
                  name="inviteCode"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel>邀请码</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="请输入邀请码（选填）"
                            autoComplete="off"
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
              </div>
              <Button
                type="submit"
                variant="primary"
                size="lg"
                className="mx-[33px] my-[25px]"
              >
                完成
              </Button>
            </form>
          </Form>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
