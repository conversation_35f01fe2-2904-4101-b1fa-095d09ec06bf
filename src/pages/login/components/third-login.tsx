import { CommonIcon } from "@/components/common-icon";
import { useLoginMethod } from "@/hooks/use-login-method";
import { loginAuthToken } from "@/lib/api/user";
import { getDeviceId } from "@/utils";
import { LoginMethod, UserAuthType } from "@/utils/enums";
import { useMutation } from "@tanstack/react-query";
import { Badge, Toast } from "antd-mobile";
import { useLoginAction } from "../hooks/use-login-action";

export const ThirdLogin = () => {
  const authTokenMutation = useMutation({ mutationFn: loginAuthToken });

  const { isSupportLoginMethod } = useLoginMethod();

  const { loginAfterAction } = useLoginAction();

  const handleAuthToken = async () => {
    Toast.show({
      icon: "loading",
      content: "加载中…",
      duration: 0,
    });

    const { ok, data } = await authTokenMutation.mutateAsync({
      token: getDeviceId(),
      type: UserAuthType.deviceId,
    });

    if (ok && data) {
      loginAfterAction(data);
    }
  };

  return (
    <div className="mt-auto">
      <div className="flex justify-center text-[13px] text-hint-color">
        <span>其他登录方式</span>
      </div>
      <div className="flex py-[24px] justify-evenly px-[20vw]">
        {/* {isSupportLoginMethod(LoginMethod.QQ登录) ? (
          <CommonIcon name="wechat" w={45} h={45} />
        ) : null}
        {isSupportLoginMethod(LoginMethod.微信登录) ? (
          <CommonIcon name="qq" w={45} h={45} />
        ) : null} */}
        {isSupportLoginMethod(LoginMethod.设备ID登录) ? (
          <Badge content="New" style={{ "--right": "10px", "--top": "2px" }}>
            <CommonIcon name="device" w={45} h={45} onClick={handleAuthToken} />
          </Badge>
        ) : null}
      </div>
    </div>
  );
};
