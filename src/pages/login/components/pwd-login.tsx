import { useForm } from "react-hook-form";
import { extractLeadingNumbers } from "@/utils";
import { Form, FormControl, FormField, FormItem } from "@/ui/form";
import { Input } from "@/ui/input";
import { Button } from "@/ui/button";
import { Dialog, Toast } from "antd-mobile";
import { phoneRegex } from "@/utils/constant";
import { useMutation } from "@tanstack/react-query";
import { loginPassword } from "@/lib/api/user";
import { useLoginMethod } from "@/hooks/use-login-method";
import { LoginMethod } from "@/utils/enums";
import { useLoginAction } from "../hooks/use-login-action";

interface Props {
  onChangeMode: () => void;
}

export const PwdLogin = (props: Props) => {
  const { onChangeMode } = props;

  const { isSupportLoginMethod } = useLoginMethod();

  const loginMutation = useMutation({ mutationFn: loginPassword });

  const { loginAfterAction } = useLoginAction();

  const form = useForm<{ phone?: string; password?: string }>();

  const handleSumbit = async () => {
    const { phone, password } = form.getValues();

    if (!phone) {
      Toast.show({ content: "请输入手机号" });
      return;
    }

    if (!phoneRegex.test(phone)) {
      Toast.show({ content: "请输入正确的手机号" });
      return;
    }

    if (!password) {
      Toast.show({ content: "请输入密码" });
      return;
    }

    Toast.show({
      icon: "loading",
      content: "加载中…",
      duration: 0,
    });

    const { ok, data } = await loginMutation.mutateAsync({
      phone,
      password,
    });

    if (ok && data) {
      loginAfterAction(data);
    }
  };

  const handleForgotPassword = () => {
    Dialog.alert({
      title: "提示",
      closeOnMaskClick: true,
      bodyClassName: "bg-app-bar-background",
      content: (
        <div className="text-center text-foreground">
          如果你遗忘了登录密码，请使用短信验证码登录，随后可进入[设置 -
          安全设置]进行修改密码。
        </div>
      ),
      confirmText: (
        <div className="text-primary-foreground text-base">知道了</div>
      ),
    });
  };

  return (
    <div className="flex flex-col">
      <Form {...form}>
        <div className="flex flex-col gap-8">
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex items-center relative z-0">
                    <span className="absolute ml-[5px] z-[-1]">+86</span>
                    <Input
                      placeholder="请输入手机号码"
                      className="pl-[45px]"
                      maxLength={11}
                      {...field}
                      autoComplete="off"
                      value={extractLeadingNumbers(field.value)}
                    />
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => {
              return (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder="请输入密码"
                      type="password"
                      {...field}
                      autoComplete="off"
                    />
                  </FormControl>
                </FormItem>
              );
            }}
          />
        </div>
      </Form>
      <div className="mt-[20px] mb-[25px] flex justify-between">
        <span
          className="text-secondary-foreground"
          onClick={handleForgotPassword}
        >
          忘记密码?
        </span>
        {isSupportLoginMethod(LoginMethod.手机短信验证码登录) ? (
          <span className="text-secondary-foreground" onClick={onChangeMode}>
            手机验证码登录
          </span>
        ) : null}
      </div>
      <Button variant="primary" size="lg" onClick={handleSumbit}>
        登录
      </Button>
    </div>
  );
};
