import { useForm } from "react-hook-form";
import { extractLeadingNumbers } from "@/utils";
import { useCountDownTimer } from "@/hooks/use-count-down-timer";
import { Form, FormControl, FormField, FormItem } from "@/ui/form";
import { Input } from "@/ui/input";
import { Button } from "@/ui/button";
import { Toast } from "antd-mobile";
import { useMutation } from "@tanstack/react-query";
import { smsSendCode } from "@/lib/api/message";
import { loginSmsCode } from "@/lib/api/user";
import { phoneRegex } from "@/utils/constant";
import { useLoginMethod } from "@/hooks/use-login-method";
import { LoginMethod } from "@/utils/enums";
import { useLoginAction } from "../hooks/use-login-action";

interface Props {
  onChangeMode: () => void;
}

export const CodeLogin = (props: Props) => {
  const { onChangeMode } = props;
  const { isSupportLoginMethod } = useLoginMethod();

  const sendCodeMutation = useMutation({ mutationFn: smsSendCode });
  const loginMutation = useMutation({ mutationFn: loginSmsCode });

  const { loginAfterAction } = useLoginAction();

  const form = useForm<{ phone?: string; code?: string }>();
  const { isRunning, seconds, handleStart } = useCountDownTimer(60);

  const checkPhone = async () => {
    const { phone } = form.getValues();

    if (!phone) {
      Toast.show({ content: "请输入手机号" });
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(phone)) {
      Toast.show({ content: "请输入正确的手机号" });
      return;
    }
    // 发送验证码
    Toast.show({
      icon: "loading",
      content: "加载中…",
      duration: 0,
    });

    const { ok, msg } = await sendCodeMutation.mutateAsync(phone);

    if (ok) {
      handleStart();
    } else {
      Toast.show({ content: msg });
    }
  };

  const handleSumbit = async () => {
    const { phone, code } = form.getValues();

    if (!phone) {
      Toast.show({ content: "请输入手机号" });
      return;
    }

    if (!phoneRegex.test(phone)) {
      Toast.show({ content: "请输入正确的手机号" });
      return;
    }

    if (!code) {
      Toast.show({ content: "请输入验证码" });
      return;
    }

    Toast.show({
      icon: "loading",
      content: "加载中…",
      duration: 0,
    });

    const { ok, data } = await loginMutation.mutateAsync({ phone, code });

    if (ok && data) {
      loginAfterAction(data);
    }
  };

  return (
    <div className="flex flex-col">
      <Form {...form}>
        <div className="flex flex-col gap-8">
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex items-center relative z-0">
                    <span className="absolute ml-[5px] z-[-1]">+86</span>
                    <Input
                      placeholder="请输入手机号"
                      className="pl-[45px] pr-[70px]"
                      maxLength={11}
                      {...field}
                      autoComplete="off"
                      value={extractLeadingNumbers(field.value)}
                    />
                    {!isRunning ? (
                      <Button
                        variant="primary"
                        size="sm"
                        className="absolute right-0"
                        onClick={checkPhone}
                      >
                        发送
                      </Button>
                    ) : (
                      <Button
                        size="sm"
                        variant="secondary"
                        className="absolute right-0"
                      >
                        {seconds}
                      </Button>
                    )}
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="code"
            render={({ field }) => {
              return (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder="请输入验证码"
                      maxLength={6}
                      {...field}
                      autoComplete="off"
                      value={extractLeadingNumbers(field.value)}
                    />
                  </FormControl>
                </FormItem>
              );
            }}
          />
        </div>
      </Form>
      {isSupportLoginMethod(LoginMethod.密码登录) ? (
        <div className="mt-[20px] mb-[25px] flex justify-end">
          <span className="text-secondary-foreground" onClick={onChangeMode}>
            账号密码登录
          </span>
        </div>
      ) : null}
      <Button variant="primary" size="lg" onClick={handleSumbit}>
        登录
      </Button>
    </div>
  );
};
