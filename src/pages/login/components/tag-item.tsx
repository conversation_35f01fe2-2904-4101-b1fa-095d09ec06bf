interface Props {
  title: string;
  value?: number;
  config: Array<{
    value: number;
    title: string;
  }>;
  onChange: (value: number) => void;
}

export const TagItem = (props: Props) => {
  const { title, value, config, onChange } = props;

  return (
    <div className="w-full flex flex-col gap-3">
      <span className="">{title}</span>
      <div className="flex gap-3">
        {config.map((item) => (
          <span
            key={item.value}
            style={value === item.value ? { backgroundColor: "#FF595D" } : {}}
            className="flex-1 h-[41px] flex justify-center items-center bg-foreground/15 rounded-[10px]"
            onClick={() => onChange(item.value)}
          >
            {item.title}
          </span>
        ))}
      </div>
    </div>
  );
};
