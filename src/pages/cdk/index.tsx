import { useState } from "react";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { Button } from "@/ui/button";
import { Textarea } from "@/ui/textarea";
import { useConfigList } from "@/hooks/use-config-list";
import { useMutation } from "@tanstack/react-query";
import { cdkActive } from "@/lib/api/wallet";
import { Toast } from "antd-mobile";
import { tipsDialog } from "@/components/work/tips-dialog";
import { queryClient } from "@/provider/query-client";
import { userWalletQueryKey } from "@/hooks/use-user-wallet";

export default function CDKPage() {
  const [cdk, setCdk] = useState("");

  const { getSystemConfig } = useConfigList();

  const activeCdkMutation = useMutation({
    mutationFn: cdkActive,
  });

  const handleActiveCdk = async () => {
    Toast.show({ content: "请稍后...", duration: 0 });

    const { ok } = await activeCdkMutation.mutateAsync(cdk);

    if (ok) {
      tipsDialog.success({
        title: "激活成功",
        content: "卡密激活成功",
        confirmText: "知道了",
      });
      // 更新用户钱包
      queryClient.refetchQueries({
        queryKey: userWalletQueryKey,
      });
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="卡密充值" />
      <PageMain className="bg-scaffold-background">
        <div className="flex flex-col p-[15px] gap-5">
          <Textarea
            rows={5}
            className="rounded-[10px] bg-app-bar-background border-none"
            placeholder="请输入卡密"
            value={cdk}
            onChange={(e) => setCdk(e.target.value)}
          />
          <div>
            <Button
              variant="primary"
              className="w-full h-[50px] text-[18px]"
              disabled={!cdk.trim()}
              onClick={handleActiveCdk}
            >
              立即验证
            </Button>
          </div>
          <div>
            <span className="bg-[#FF595D] inline-flex items-center rounded px-[5px] mr-[5px]">
              <span className="text-[22px] text-white" style={{ zoom: 0.5 }}>
                兑换规则
              </span>
            </span>
            <span className="text-xs">{getSystemConfig("CDK_TIPS")}</span>
          </div>
          <div>
            <a
              className="text-[#6712FF]"
              href={getSystemConfig("CDK_BUY_LINK") ?? ""}
            >
              {getSystemConfig("CDK_BUY_TIPS")}
            </a>
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
