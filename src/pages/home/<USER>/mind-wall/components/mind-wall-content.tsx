import { MindWallResult } from "@/type/mind-wall-result";
import { MindWallItem } from "./mind-wall-item";

interface Props {
  data: MindWallResult[];
}

export const MindWallContent = (props: Props) => {
  const { data } = props;

  return (
    <div className="flex flex-col gap-[15px] my-[15px]">
      {data.map((item) => (
        <MindWallItem key={item.id} data={item} />
      ))}
    </div>
  );
};
