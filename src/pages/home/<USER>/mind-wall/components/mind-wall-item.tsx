import classNames from "classnames";
import { CommonIcon } from "@/components/common-icon";
import { UserAvatar } from "@/components/user-avatar";
import { MindWallResult } from "@/type/mind-wall-result";
import { UserTagList } from "@/components/work/user-tag-list";
import { <PERSON><PERSON><PERSON><PERSON>, ScrollBar } from "@/ui/scroll-area";
import { Image } from "antd-mobile";
import { useState } from "react";
import { useEffect } from "react";
import { useMutation } from "@tanstack/react-query";
import { mindWallOperationLike } from "@/lib/api/mind-wall";
import { MouseEvent } from "react";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import {
  UserInfoModal,
  UserInfoModalRef,
} from "@/components/work/user-info-modal";
import { useRef } from "react";

interface Props {
  isRadius?: boolean;
  clickJump?: boolean;
  data: MindWallResult;
}

export const MindWallItem = (props: Props) => {
  const { data: propsData, isRadius = true, clickJump = true } = props;
  const [data, setData] = useState(propsData);

  const { navigateRoute } = useNavigateRoute();

  const userInfoModalRef = useRef<UserInfoModalRef>(null);

  const openUserInfoModal = (id: string) => {
    userInfoModalRef.current?.open(id);
  };

  useEffect(() => {
    setData(propsData);
  }, [propsData]);

  const { mutateAsync } = useMutation({
    mutationFn: mindWallOperationLike,
  });

  const handleLike = async (e: MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();

    const { ok } = await mutateAsync({ id: data.id!, like: !data.hasLike });

    if (ok) {
      setData((prev) => {
        return {
          ...prev,
          hasLike: !prev.hasLike,
          likeNumber: prev.hasLike
            ? (prev.likeNumber || 0) - 1
            : (prev.likeNumber || 0) + 1,
        };
      });
    }
  };

  const handleClick = () => {
    if (clickJump) {
      navigateRoute("/mind-wall-detailed", {
        id: data.id,
      });
    }
  };

  return (
    <div
      className={classNames(
        "py-3 px-4",
        "mind-wall-item-bg",
        "flex flex-col gap-3 items-center",
        isRadius ? "rounded-[20px]" : ""
      )}
      onClick={handleClick}
    >
      <div className="flex mt-2 w-full">
        <div
          className="flex gap-[5px] items-center flex-1 w-0"
          onClick={(e) => {
            e.stopPropagation();
            openUserInfoModal(data.giverUser?.id!);
          }}
        >
          <div className="flex-1 flex flex-col w-0">
            <span className="font-medium line-clamp-1 text-right">
              {data.giverUser?.nickname}
            </span>
            {data.giverUser ? (
              <ScrollArea>
                <UserTagList user={data.giverUser} />
                <ScrollBar orientation="horizontal" />
              </ScrollArea>
            ) : null}
          </div>
          <UserAvatar
            src={data.giverUser?.avatarUrl ?? ""}
            isVip={data.giverUser?.vip}
            size={45}
          />
        </div>
        <div className="flex flex-col gap-1 items-center flex-none">
          <CommonIcon name="mind-wall-love" w={51} h={22} />
          <div className="w-[20px] h-[20px] bg-[#FF005C] rounded-full text-xs flex items-center justify-center">
            送
          </div>
        </div>
        <div
          className="flex gap-[5px] items-center flex-1 w-0"
          onClick={(e) => {
            e.stopPropagation();
            openUserInfoModal(data.user?.id!);
          }}
        >
          <UserAvatar src={data.user?.avatarUrl ?? ""} size={45} />
          <div className="flex-1 w-0">
            <span className="font-medium line-clamp-1">
              {data.user?.nickname}
            </span>
            {data.user ? (
              <ScrollArea>
                <UserTagList user={data.user} />
                <ScrollBar orientation="horizontal" />
              </ScrollArea>
            ) : null}
          </div>
        </div>
      </div>

      <div className="flex items-center gap-[5px]">
        <div className="text-[#FF005C] font-medium">
          {data.title} · {data.totalPrice}
        </div>
        <Image src={data.resource} className="h-[25px] w-auto" fit="cover" />
      </div>
      <div className="mind-wall-item-message-bg self-start p-[10px] mx-[15px]">
        <div className="text-[15px] line-clamp-1">{data.content}</div>
      </div>
      <div className="flex gap-3 self-end">
        <div className="flex gap-1 items-center">
          <CommonIcon name="comment" w={12} h={12} />
          <span className="text-xs text-[#b2b2b2]">
            {data.commentNumber || "评论"}
          </span>
        </div>
        <div className="flex gap-1 items-center" onClick={handleLike}>
          <CommonIcon
            name={data.hasLike ? "like-selected" : "like"}
            w={12}
            h={12}
          />
          <span className="text-xs text-[#b2b2b2]">
            {data.likeNumber || "点赞"}
          </span>
        </div>
      </div>
      <UserInfoModal ref={userInfoModalRef} />
    </div>
  );
};
