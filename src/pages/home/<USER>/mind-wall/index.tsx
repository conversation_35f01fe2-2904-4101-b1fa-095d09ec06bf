import { PageWrapper } from "../components/page-wrapper";
import { MindWallResult } from "@/type/mind-wall-result";
import { MindWallContent } from "./components/mind-wall-content";

export default function MindWallPage() {
  return (
    <PageWrapper<MindWallResult>
      content={{
        type: "mind-wall",
        data: [
          {
            key: 0,
            label: "",
            url: "/mind-wall/mind-wall/mind-wall/newest",
            params: {},
          },
        ],
        children: (data) => <MindWallContent data={data} />,
      }}
    />
  );
}
