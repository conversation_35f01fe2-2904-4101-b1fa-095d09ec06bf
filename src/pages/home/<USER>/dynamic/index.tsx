import { useEffect, useState } from "react";
import { LayoutConfigDynamicHome } from "@/type/layout-config";
import { DynamicContent } from "./components/dynamic-content";
import { PageWrapper } from "../components/page-wrapper";
import { DynamicResult } from "@/type/dynamic-result";
import { ExtraContent } from "./components/extra-content";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { useQuery } from "@tanstack/react-query";
import { layoutConfigOptions } from "@/utils/query-options";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { CommonIcon } from "@/components/common-icon";

export default function DynamicPage() {
  const { data: config } = useQuery(
    layoutConfigOptions(LayoutConfigKey.DynamicHome)
  );

  const data = config?.data as LayoutConfigDynamicHome;

  const { navigateRoute } = useNavigateRoute();

  const tabList =
    data?.tabs.items.map((item, index) => ({
      ...item,
      key: index,
    })) ?? [];

  const [value, setValue] = useState(0);

  useEffect(() => {
    if (data?.tabs.defaultIndex) {
      setValue(data.tabs.defaultIndex);
    }
  }, [data?.tabs.defaultIndex]);

  const handleToViolationList = () => {
    navigateRoute("/violation-list");
  };

  const handleToDynamicPublish = () => {
    navigateRoute("/dynamic-publish");
  };

  return (
    <>
      <PageWrapper<DynamicResult>
        filter={{
          filter: data?.filter,
          value,
          onChange: setValue,
          tabs: tabList,
          moreAction: (
            <span
              className="bg-black-room-gradient text-xs h-[25px] leading-[25px] rounded-[13px] px-[4px]"
              onClick={handleToViolationList}
            >
              <span className="text-[#7D6096] text-xs dark:text-[#CEE0FF]">
                小黑屋
              </span>
            </span>
          ),
        }}
        content={{
          type: "dynamic",
          data: tabList,
          children: (data) => <DynamicContent data={data} />,
          extraContent: (config) => <ExtraContent config={config} />,
        }}
      />
      <div
        className="fixed bottom-[105px] right-[15px]"
        onClick={handleToDynamicPublish}
      >
        <CommonIcon name="add" w={42} h={42} />
      </div>
    </>
  );
}
