import { Fragment } from "react";
import { Divider } from "antd-mobile";
import { DynamicItem } from "@/components/work/dynamic-item";
import { DynamicResult } from "@/type/dynamic-result";

interface Props {
  data: DynamicResult[];
}

export const DynamicContent = (props: Props) => {
  const { data } = props;

  return (
    <div className="flex flex-col pt-2">
      {data.map((item, index) => (
        <Fragment key={item.id}>
          <DynamicItem data={item} />
          {index !== data.length - 1 ? (
            <Divider className="border-divider" />
          ) : null}
        </Fragment>
      ))}
    </div>
  );
};
