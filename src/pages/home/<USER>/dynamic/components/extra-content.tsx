import { getHomeBannerList } from "@/lib/api/system";
import { getRoute } from "@/router/route-map";
import { CommunityTabType } from "@/type/layout-config";
import { useQuery } from "@tanstack/react-query";
import { Image, Swiper } from "antd-mobile";
import { Topic } from "../../components/topic";
import { topicHot } from "@/lib/api/dynamic";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

interface Props {
  config: CommunityTabType;
}
export const ExtraContent = (props: Props) => {
  const { config } = props;

  const { navigateRoute } = useNavigateRoute();

  const { data: bannerData } = useQuery({
    queryKey: ["dynamic", "banner", config],
    queryFn: getHomeBannerList,
    enabled: Boolean(config.custom?.showBanner),
  });

  const { data: topicData } = useQuery({
    queryKey: ["dynamic", "topic", "hot", config],
    queryFn: topicHot,
    enabled: Bo<PERSON>an(config.custom?.showTopic),
  });

  const jumbUri = (uri?: string) => {
    if (uri) {
      navigateRoute(getRoute(uri));
    }
  };

  return (
    <>
      {bannerData?.data?.length ? (
        <Swiper className="my-[10px]">
          {bannerData.data.map((item, i) => (
            <Swiper.Item key={i}>
              <Image
                src={item.image}
                className="h-[125px] w-auto rounded-[5px]"
                fit="cover"
                onClick={() => jumbUri(item.uri)}
              />
            </Swiper.Item>
          ))}
        </Swiper>
      ) : null}
      {topicData?.data?.length ? <Topic data={topicData.data} /> : null}
    </>
  );
};
