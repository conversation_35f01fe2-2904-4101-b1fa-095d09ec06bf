import { CommonIcon } from "@/components/common-icon";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { useTheme } from "@/provider/useTheme";
import { getRoute } from "@/router/route-map";
import { Action, LayoutConfigHome } from "@/type/layout-config";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { layoutConfigOptions } from "@/utils/query-options";
import { useQuery } from "@tanstack/react-query";

const formatIcon = (icon: Action["icon"], theme: string) => {
  const url = theme === "light" ? icon.light : icon.dark;

  // 通用格式化为 menus/xxx，支持字母、数字、下划线和连字符
  return url.replace(/.*\/([^/]+)\.png$/, "$1");
};

interface Props {
  defaultIndex?: number;
}

export const Search = (props: Props) => {
  const { defaultIndex = 0 } = props;

  const { data } = useQuery(
    layoutConfigOptions<LayoutConfigHome>(LayoutConfigKey.Home)
  );

  const searchBar = data?.data?.searchBar;

  const { navigateRoute } = useNavigateRoute();
  const theme = useTheme();

  const handleSearch = () => {
    navigateRoute("/user-search-home", { defaultIndex });
  };

  const handleRoute = (item: Action) => {
    navigateRoute(getRoute(item.uri));
  };

  return (
    <div className="w-full flex items-center gap-[10px]">
      <div
        className="flex-1 flex gap-1 py-[7px] px-[10px] items-center bg-foreground/10 rounded-full"
        onClick={handleSearch}
      >
        <CommonIcon name="search" w={13} h={13} />
        <span className="text-[#999] text-sm">{searchBar?.content}</span>
      </div>
      {searchBar?.action?.length ? (
        <div className="flex gap-[10px]">
          {searchBar.action.map((item, index) => (
            <span key={index} onClick={() => handleRoute(item)}>
              <CommonIcon
                name={formatIcon(item.icon, theme)}
                w={item.icon.width / 2}
                h={item.icon.width / 2}
              />
            </span>
          ))}
        </div>
      ) : null}
    </div>
  );
};
