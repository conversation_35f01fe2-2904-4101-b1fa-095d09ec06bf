import { getRoute } from "@/router/route-map";
import { LayoutConfigCommunity } from "@/type/layout-config";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { layoutConfigOptions } from "@/utils/query-options";
import { useQuery } from "@tanstack/react-query";
import { Navigate } from "react-router-dom";

/**
 * 主要用于中间层跳转
 *
 * 不会存在其他 ui
 */
export default function Home() {
  const { data: config } = useQuery(
    layoutConfigOptions(LayoutConfigKey.Community)
  );

  const data = config?.data as LayoutConfigCommunity;

  const { defaultIndex = 0, items = [] } = data?.tabs || {};

  const route = getRoute(items[defaultIndex]?.uri);

  if (route) {
    return <Navigate to={route} replace />;
  }

  return null;
}
