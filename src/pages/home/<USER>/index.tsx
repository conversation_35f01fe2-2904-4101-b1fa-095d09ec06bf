import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { mindWallPageByUserId } from "@/lib/api/mind-wall";
import { PageParams } from "@/type";
import { useSearchParams } from "react-router-dom";
import { MindWallItem } from "../community/mind-wall/components/mind-wall-item";

export default function UserMindWallPage() {
  const [searchParams] = useSearchParams();
  const userId = searchParams.get("id") || "";

  const queryKey = ["user-mind-wall", userId];
  const queryFn = (params: PageParams) =>
    mindWallPageByUserId({ userId }, params);

  return (
    <PageWrapper>
      <NavigationBar canBack title="心意墙" />
      <PageMain>
        <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
          {(data) => {
            return (
              <div className="flex flex-col gap-[15px] p-[15px]">
                {data.map((item) => {
                  return <MindWallItem key={item.id} data={item} />;
                })}
              </div>
            );
          }}
        </ScrollLoadData>
      </PageMain>
    </PageWrapper>
  );
}
