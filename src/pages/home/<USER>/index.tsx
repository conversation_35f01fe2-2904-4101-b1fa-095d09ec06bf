import { useQuery } from "@tanstack/react-query";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { MyUserInfo } from "./components/my-user-info";
import { useConfigList } from "@/hooks/use-config-list";
import { Button } from "@/ui/button";
import { getFirstRecharge, getReback } from "@/lib/api/system";
import { useDefaultResource } from "@/hooks/use-default-avatar";
import { Image } from "antd-mobile";
import { DefaultLoadImage } from "@/components/default-load-image";
import { MyMenus } from "./components/my-menus";
import { StateView } from "@/components/state-view";
import { ScrollArea } from "@/ui/scroll-area";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { getRoute } from "@/router/route-map";

export default function MyPage() {
  const { data: userInfo } = useFullUserInfo();
  const { getSystemConfig } = useConfigList();
  const { firstRechargeBanner, returnUserBanner } = useDefaultResource();
  const { navigateRoute } = useNavigateRoute();

  const { data: reback, isLoading: rebackLoading } = useQuery({
    queryKey: ["reback"],
    queryFn: getReback,
  });

  const { data: firstRecharge, isLoading: firstRechargeLoading } = useQuery({
    queryKey: ["firstRecharge"],
    queryFn: getFirstRecharge,
  });

  const showReback = reback?.data && !!returnUserBanner?.length;
  const showRecharge = firstRecharge?.data && !!firstRechargeBanner?.length;

  const handleUnban = () => {
    navigateRoute("/self-unban");
  };

  return (
    <div className="flex flex-col w-full h-full pb-[15px]">
      <ScrollArea className="h-full">
        <MyUserInfo />
        <StateView isLoading={rebackLoading || firstRechargeLoading}>
          <div className="relative flex-1 h-0 bg-app-bar-background p-[15px]">
            <div className="flex flex-col gap-[15px]">
              {userInfo?.existViolation ? (
                // 违规情况
                <div className="flex items-center gap-[10px] bg-[#FFDEEA] dark:bg-[#2A0412] rounded-[4px] px-[10px] py-[8px]">
                  <span className="text-[13px] text-[#FF005C]">
                    {getSystemConfig("VIOLATION_USER_SELF_TIPS")}
                  </span>
                  <Button
                    variant="primary"
                    className="text-[13px] h-[28px]"
                    onClick={handleUnban}
                  >
                    自助解封
                  </Button>
                </div>
              ) : null}
              {showReback ? (
                // 回归礼包
                <div>
                  <Image
                    width="100%"
                    height="auto"
                    src={returnUserBanner[0].resourceUrl}
                    fit="cover"
                    placeholder={<DefaultLoadImage />}
                    fallback={<DefaultLoadImage />}
                  />
                </div>
              ) : null}
              {showRecharge ? (
                // 首充礼包
                <div
                  onClick={() => {
                    navigateRoute(getRoute(firstRecharge?.data?.url || ""));
                  }}
                >
                  <Image
                    width="100%"
                    height="auto"
                    src={firstRechargeBanner[0].resourceUrl}
                    fit="cover"
                    placeholder={<DefaultLoadImage />}
                    fallback={<DefaultLoadImage />}
                  />
                </div>
              ) : null}
              <div>
                <MyMenus />
              </div>
            </div>
          </div>
        </StateView>
      </ScrollArea>
    </div>
  );
}
