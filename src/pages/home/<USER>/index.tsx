import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>B<PERSON> } from "@/ui/scroll-area";
import { Search } from "../components/search";
import { LayoutConfigLiveHome } from "@/type/layout-config";
import { Tabs } from "@/components/work/tabs";
import { useEffect, useState } from "react";
import { PageMain } from "@/components/page-main";
import { TabContent } from "./components/tab-content";
import { PageWrapper } from "@/components/page-wrapper";
import { useQuery } from "@tanstack/react-query";
import { layoutConfigOptions } from "@/utils/query-options";
import { LayoutConfigKey } from "@/utils/layout-config-key";

export default function LivePage() {
  const { data: config } = useQuery(
    layoutConfigOptions(LayoutConfigKey.LiveHome)
  );

  const data = config?.data as LayoutConfigLiveHome;

  const defaultIndex = data?.tabs.defaultIndex;
  const tabs = data?.tabs.items ?? [];
  const newTabs = [...tabs, { label: "回放", url: "livePlayback" }];

  const [activeKey, setActiveKey] = useState(0);

  useEffect(() => {
    if (defaultIndex) {
      setActiveKey(defaultIndex);
    }
  }, [defaultIndex]);

  return (
    <PageWrapper>
      <div className="mx-[15px] pt-[8px] pb-[4px]">
        <Search defaultIndex={5} />
      </div>
      <div className="mx-[15px]">
        <ScrollArea>
          <div>
            <Tabs tabs={newTabs} value={activeKey} onChange={setActiveKey} />
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>
      <PageMain needScroll={false}>
        {newTabs.map((tab, index) => (
          <TabContent
            key={index}
            index={index}
            isActive={index === activeKey}
            data={tab}
          />
        ))}
      </PageMain>
    </PageWrapper>
  );
}
