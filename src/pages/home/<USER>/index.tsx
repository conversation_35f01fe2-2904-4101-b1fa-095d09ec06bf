import { Navigate } from "react-router-dom";
import { LayoutConfigMessage } from "@/type/layout-config";
import { getRoute } from "@/router/route-map";
import { useQuery } from "@tanstack/react-query";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { layoutConfigOptions } from "@/utils/query-options";

export default function MessagePage() {
  const { data: config } = useQuery(
    layoutConfigOptions(LayoutConfigKey.Message)
  );

  const data = config?.data as LayoutConfigMessage;

  const { defaultIndex = 0, items = [] } = data?.tabs || {};

  const route = getRoute(items[defaultIndex]?.uri);

  if (route) {
    return <Navigate to={route} replace />;
  }

  return null;
}
