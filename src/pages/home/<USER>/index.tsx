import { CommonIcon } from "@/components/common-icon";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { Tabs } from "@/components/work/tabs";
import { LayoutConfigVideo } from "@/type/layout-config";
import { ScrollA<PERSON>, ScrollBar } from "@/ui/scroll-area";
import { useEffect, useState } from "react";
import { TabContent } from "./components/tab-content";
import classNames from "classnames";
import { useQuery } from "@tanstack/react-query";
import { getVideoLayout } from "@/lib/api/video";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

export default function VideoPage() {
  const { data } = useQuery({
    queryKey: ["video-layout"],
    queryFn: getVideoLayout,
    staleTime: Infinity,
  });

  const { filter, tabs } = (data?.data ?? {}) as LayoutConfigVideo;

  const newTabs = [
    {
      label: "短视频",
      url: "shortVideo",
    },
    ...(tabs?.items ?? []),
  ];

  const [activeKey, setActiveKey] = useState(0);

  useEffect(() => {
    if (tabs?.defaultIndex) {
      setActiveKey(tabs.defaultIndex);
    }
  }, [tabs?.defaultIndex]);

  const { navigateRoute } = useNavigateRoute();

  const handleAdd = () => {
    if (activeKey === 0) {
      navigateRoute("/short-video-publish");
    } else {
      navigateRoute("/video-publish");
    }
  };

  return (
    <PageWrapper>
      <div
        className={classNames(
          "p-[15px] pb-0 flex w-full items-center",
          activeKey === 0 ? "fixed top-0 z-10" : ""
        )}
      >
        <div className="flex-1 w-0">
          <ScrollArea>
            <Tabs
              tabs={newTabs}
              value={activeKey}
              itemClassName={activeKey === 0 ? "!text-white" : ""}
              onChange={setActiveKey}
            />
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </div>
        <div>
          <CommonIcon name="search" w={15} h={15} />
        </div>
      </div>
      <PageMain needScroll={false}>
        {newTabs.map((tab, index) => (
          <TabContent
            key={index}
            index={index}
            isActive={index === activeKey}
            data={tab}
            filter={filter}
          />
        ))}
      </PageMain>
      <div className="fixed bottom-[105px] right-[15px]" onClick={handleAdd}>
        <CommonIcon name="add" w={42} h={42} />
      </div>
    </PageWrapper>
  );
}
