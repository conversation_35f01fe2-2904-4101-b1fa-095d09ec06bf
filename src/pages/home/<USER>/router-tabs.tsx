import { communityTabBg } from "@/components/image-list";
import classNames from "classnames";
import { Link, useLocation } from "react-router-dom";

interface Props {
  tabs: Array<{ to: string; title: string }>;
}

export const RouterTabs = (props: Props) => {
  const { tabs } = props;

  const { pathname } = useLocation();

  return (
    <div className="flex gap-2 items-center">
      {tabs.map((tab) => (
        <Link
          key={tab.to}
          to={tab.to}
          className={classNames(
            "text-[#666] dark:text-foreground",
            pathname === tab.to
              ? `bg-[length:50px_25px] bg-no-repeat bg-right text-lg font-medium`
              : ""
          )}
          style={{
            backgroundImage:
              pathname === tab.to ? `url(${communityTabBg})` : undefined,
          }}
        >
          <div className="my-[5px] mr-[7px]">
            <span>{tab.title}</span>
          </div>
        </Link>
      ))}
    </div>
  );
};
