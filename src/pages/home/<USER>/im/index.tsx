import { CommonIcon } from "@/components/common-icon";
import { noRecord } from "@/components/image-list";
import { useConfigList } from "@/hooks/use-config-list";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { User } from "@/type/user";
import { ConversationList } from "@tencentcloud/chat-uikit-react";
import { Image } from "antd-mobile";
import { ImItem } from "../components/im-item";

export default function ImPage() {
  const { data: userInfo } = useFullUserInfo();
  const { getSystemConfig } = useConfigList();
  const customerServiceTarget = getSystemConfig(
    "OFFICIAL_CUSTOMER_SERVICE_TARGET"
  );

  const handleOpenCustomerService = () => {
    if (userInfo && customerServiceTarget) {
      let url = customerServiceTarget;
      for (const key in userInfo) {
        const value = userInfo[key as keyof User];
        if (value) {
          url = url.replace(`\${${key}}`, value.toString());
        }
      }

      window.open(url, "_self");
    }
  };

  const noRecordRender = (
    <div className="flex flex-col gap-[15px] items-center justify-center mt-[50px]">
      <Image
        src={noRecord}
        fit="cover"
        width={`${150 / 16}rem`}
        height="auto"
        placeholder={null}
      />
      <span>没有会话消息~</span>
    </div>
  );

  return (
    <div className="h-full flex flex-col">
      <div
        className="flex gap-2 items-center px-[15px] "
        onClick={handleOpenCustomerService}
      >
        <CommonIcon name="customerService" w={50} h={50} />
        <span className="text-base">官方客服</span>
      </div>
      <ConversationList
        className="flex-1 h-0 px-[15px] overflow-y-auto"
        enableSearch={false}
        enableCreate={false}
        Preview={ImItem}
        PlaceholderEmptyList={noRecordRender}
        PlaceholderLoading={noRecordRender}
        PlaceholderLoadError={noRecordRender}
      />
    </div>
  );
}
