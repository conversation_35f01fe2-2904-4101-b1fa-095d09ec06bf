import { ScrollLoadData } from "@/components/scroll-load-data";
import { getLayoutPageList } from "@/lib/api/layout";
import { PageParams } from "@/type";
import { BaseUrlConfig } from "@/type/layout-config";
import { useEffect, useState } from "react";
import { PullToRefresh } from "@/components/pull-to-refresh";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { ScrollArea } from "@/ui/scroll-area";
import { LiveRoomSimpleResult } from "@/type/live_room_simple_result";
import { PlaybackTabItem } from "./playback-tab-item";

interface Props {
  index: number;
  isActive: boolean;
  data: BaseUrlConfig;
}

export const PlaybackTabContent = (props: Props) => {
  const { index, isActive, data } = props;

  const [isInit, setIsInit] = useState(false);

  // index 为了处理当params为 {}时，刷新时会刷新 所有数据，因为 只有url
  const queryKey = [data.url, index, data.params];
  const queryFn = (params: PageParams) => {
    return getLayoutPageList<LiveRoomSimpleResult[]>({
      url: data.url,
      params,
      data: { ...data.params },
    });
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  useEffect(() => {
    if (isActive) {
      setIsInit(true);
    }
  }, [isActive]);

  if (!isInit) {
    return null;
  }

  return (
    <ScrollArea className={isActive ? "h-full" : "hidden"}>
      <PullToRefresh onRefresh={handleRefresh}>
        <ScrollLoadData size={10} queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="p-[15px] grid grid-cols-2 gap-[15px] md:grid-cols-3 lg:grid-cols-4">
              {data.map((item) => (
                <PlaybackTabItem key={item.id} data={item} />
              ))}
            </div>
          )}
        </ScrollLoadData>
      </PullToRefresh>
    </ScrollArea>
  );
};
