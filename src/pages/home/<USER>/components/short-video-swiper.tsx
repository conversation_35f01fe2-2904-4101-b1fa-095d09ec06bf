/* eslint-disable @typescript-eslint/ban-ts-comment */
import { ApiResponse } from "@/lib/request";
import { PageParams } from "@/type";
import { ShortVideoResult } from "@/type/short-video-result";
import { QueryFunctionContext, useInfiniteQuery } from "@tanstack/react-query";
import { Swiper, SwiperRef } from "antd-mobile";
import { ShortVideoPlayer } from "./short-video-player";
import { useEffect, useMemo, useRef, useState } from "react";
import { Button } from "@/ui/button";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { queryClient } from "@/provider/query-client";
import { produce } from "immer";

interface Props {
  showTips?: boolean;
  defaultIndex?: number;
  queryKey: any[];
  queryFn: (params: PageParams) => Promise<ApiResponse<ShortVideoResult[]>>;
}

export const ShortVideoSwiper = (props: Props) => {
  const { queryKey, queryFn, showTips = true, defaultIndex = 0 } = props;

  const [activeIndex, setActiveIndex] = useState(defaultIndex);

  const swiperRef = useRef<SwiperRef>(null);

  // reload
  const { handleRefresh } = usePageListRefresh(queryKey);

  const {
    data: pageList,
    isFetching,
    hasNextPage,
    fetchNextPage,
  } = useInfiniteQuery({
    queryKey,
    queryFn: (ctx: QueryFunctionContext<string[], PageParams>) =>
      queryFn(ctx.pageParam),
    initialPageParam: {
      page: 1,
      size: 5,
    },
    getNextPageParam(lastPageData, _allPagesData, lastPageParam) {
      const page = lastPageParam.page + 1;
      const size = lastPageParam.size;

      const len = lastPageData.data?.length || 0;

      if (len < size) {
        //  没有下一页了
        return null;
      }

      return {
        page,
        size,
      };
    },
    staleTime: Infinity,
  });

  const datasource: ShortVideoResult[] = useMemo(
    () => pageList?.pages.map((page) => page.data ?? []).flat() || [],
    [pageList]
  );

  const handleIndexChange = (index: number) => {
    setActiveIndex(index);

    if (!isFetching && hasNextPage && index === datasource.length) {
      //  加载下一页
      fetchNextPage();
    }
  };

  const handleReplaceData = (index: number, data: ShortVideoResult) => {
    // 替换数据
    queryClient.setQueryData(
      queryKey,
      produce((pageData: any) => {
        const pageIndex = Math.floor(index / 5);
        const itemIndex = index % 5;
        pageData.pages[pageIndex].data[itemIndex] = data;
      })
    );
  };

  const isFirstRender = useRef(true);
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      swiperRef.current?.swipeTo(defaultIndex);
    }
  }, [defaultIndex, datasource]);

  return (
    <div className="absolute top-0 left-0 w-full h-full">
      <Swiper
        ref={swiperRef}
        direction="vertical"
        indicator={false}
        style={{ height: "100%" }}
        onIndexChange={handleIndexChange}
      >
        {
          [
            ...datasource.map((item, index) => (
              <Swiper.Item key={item.id}>
                <ShortVideoPlayer
                  isActive={index === activeIndex}
                  index={index}
                  data={item}
                  reload={handleRefresh}
                  onReplaceData={handleReplaceData}
                />
              </Swiper.Item>
            )),
            showTips && isFetching ? (
              <Swiper.Item key="loading">
                <div className="absolute top-0 left-0 w-full h-full text-sm bg-black text-white flex items-center justify-center">
                  视频正在加载中，请耐心等待~
                </div>
              </Swiper.Item>
            ) : null,
            showTips && !isFetching ? (
              <Swiper.Item key="no-more">
                <div className="absolute top-0 left-0 w-full h-full text-sm bg-black text-white flex items-center justify-center">
                  <div className="flex flex-col gap-[10px] items-center justify-center">
                    <span className="text-sm text-white">
                      看起来你已经浏览完了所有视频~
                    </span>
                    <Button
                      variant="primary"
                      className="px-[15px]"
                      onClick={handleRefresh}
                    >
                      重新加载
                    </Button>
                  </div>
                </div>
              </Swiper.Item>
            ) : null,
          ].filter(Boolean) as React.ReactElement[]
        }
      </Swiper>
    </div>
  );
};
