import { StateView } from "@/components/state-view";
import { FilterTabs } from "@/components/work/filter-tabs";
import { LayoutConfigLivePlayback } from "@/type/layout-config";
import { ScrollA<PERSON>, ScrollBar } from "@/ui/scroll-area";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { layoutConfigOptions } from "@/utils/query-options";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { PlaybackTabContent } from "./playback-tab-content";
import classNames from "classnames";

export const LivePlayback = (props: { className: string }) => {
  const { className } = props;

  const { data: layoutConfig, isLoading } = useQuery(
    layoutConfigOptions<LayoutConfigLivePlayback>(
      LayoutConfigKey.LivePlaybackHome
    )
  );

  const [value, setValue] = useState(-1);

  const tabs =
    layoutConfig?.data?.tabs.items.map((item, index) => ({
      ...item,
      key: index,
    })) ?? [];

  useEffect(() => {
    setValue(layoutConfig?.data?.tabs.defaultIndex ?? -1);
  }, [layoutConfig]);

  return (
    <div className={classNames("h-full", className)}>
      <StateView isLoading={isLoading} isEmpty={!layoutConfig?.data}>
        <div className="h-full flex flex-col">
          <div className="mx-[15px]">
            <ScrollArea>
              <div>
                <FilterTabs value={value} tabs={tabs} onChange={setValue} />
              </div>
              <ScrollBar orientation="horizontal" />
            </ScrollArea>
          </div>
          <div className="flex-1 h-0">
            {tabs.map((tab, index) => (
              <PlaybackTabContent
                key={index}
                index={index}
                isActive={index === value}
                data={tab}
              />
            ))}
          </div>
        </div>
      </StateView>
    </div>
  );
};
