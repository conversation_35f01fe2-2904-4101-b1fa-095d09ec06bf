import { ReactNode, useState } from "react";
import { Sc<PERSON><PERSON><PERSON>, ScrollBar } from "@/ui/scroll-area";
import { FilterTabs } from "@/components/work/filter-tabs";
import { PageContent } from "./page-content";
import { CommunityTabType, Filter } from "@/type/layout-config";
import { MoreIcon } from "@/components/svg-icon";
import { FilterPopup } from "@/components/work/filter-popup";

interface Props<T> {
  filter?: {
    filter?: Filter[];
    value: number;
    onChange: (value: number) => void;
    tabs: CommunityTabType[];
    moreAction?: ReactNode;
  };
  content: {
    type: string;
    data: CommunityTabType[];
    children: (data: T[]) => React.ReactNode;
    extraContent?: (config: CommunityTabType) => ReactNode;
  };
}

export const PageWrapper = <T,>(props: Props<T>) => {
  const { filter, content } = props;

  const [visible, setVisible] = useState(false);
  const [filterValue, setFilterValue] = useState<Record<string, any>>({});

  const showFilter =
    filter?.filter?.length && !filter.tabs[filter.value].disabledFilter;

  return (
    <div className="h-full w-full flex flex-col px-[15px]">
      {filter ? (
        <div className="flex flex-none gap-1">
          <div className="flex-1 w-0">
            <ScrollArea>
              <FilterTabs
                value={filter.value}
                tabs={filter.tabs}
                onChange={filter.onChange}
              />
              <ScrollBar orientation="horizontal" />
            </ScrollArea>
          </div>
          <div className="flex-none flex gap-2">
            {filter.moreAction}
            {showFilter ? (
              <>
                <span
                  className="w-[25px] h-[25px] rounded-full bg-foreground/50 flex justify-center items-center"
                  onClick={() => setVisible(true)}
                >
                  <MoreIcon size={20} className="text-app-bar-background" />
                </span>
                <FilterPopup
                  title="过滤"
                  visible={visible}
                  value={filterValue}
                  onChange={setFilterValue}
                  data={filter?.filter ?? []}
                  onClose={() => setVisible(false)}
                />
              </>
            ) : null}
          </div>
        </div>
      ) : null}
      <div className="flex-1 h-0">
        {content.data.map((item) => (
          <PageContent<T>
            key={item.key}
            index={item.key}
            type={content.type}
            filterValue={filterValue}
            isActive={item.key === (filter?.value ?? 0)}
            data={item}
            extraContent={content.extraContent}
          >
            {content.children}
          </PageContent>
        ))}
      </div>
    </div>
  );
};
