import { getLayoutPageList } from "@/lib/api/layout";
import { PageParams } from "@/type";
import { BaseUrlConfig } from "@/type/layout-config";
import { useEffect, useState } from "react";
import { ShortVideoResult } from "@/type/short-video-result";
import { ShortVideoSwiper } from "./short-video-swiper";

interface Props {
  index: number;
  isActive: boolean;
  data: BaseUrlConfig;
}

export const ShortVideoTabContent = (props: Props) => {
  const { index, isActive, data } = props;

  const [isInit, setIsInit] = useState(false);

  // index 为了处理当params为 {}时，刷新时会刷新 所有数据，因为 只有url
  const queryKey = [data.url, index, data.params];
  const queryFn = (params: PageParams) => {
    return getLayoutPageList<ShortVideoResult[]>({
      url: data.url,
      params,
      data: { ...data.params },
    });
  };

  useEffect(() => {
    if (isActive) {
      setIsInit(true);
    }
  }, [isActive]);

  if (!isInit) {
    return null;
  }

  return (
    <div className={isActive ? "block" : "hidden"}>
      <ShortVideoSwiper queryKey={queryKey} queryFn={queryFn} />
    </div>
  );
};
