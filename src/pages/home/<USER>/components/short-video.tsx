import { StateView } from "@/components/state-view";
import { FilterTabs } from "@/components/work/filter-tabs";
import { LayoutConfigShortVideo } from "@/type/layout-config";
import { ScrollArea, ScrollBar } from "@/ui/scroll-area";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { layoutConfigOptions } from "@/utils/query-options";
import { useQuery } from "@tanstack/react-query";
import classNames from "classnames";
import { useEffect, useState } from "react";
import { ShortVideoTabContent } from "./short-video-tab-content";

interface Props {
  className?: string;
}

export const ShortVideo = (props: Props) => {
  const { className } = props;
  const { data: shortVideoLayoutConfig, isLoading } = useQuery(
    layoutConfigOptions<LayoutConfigShortVideo>(LayoutConfigKey.ShortVideoHome)
  );

  const [value, setValue] = useState(-1);

  const tabs =
    shortVideoLayoutConfig?.data?.tabs.items.map((item, index) => ({
      ...item,
      key: index,
    })) ?? [];

  useEffect(() => {
    setValue(shortVideoLayoutConfig?.data?.tabs.defaultIndex ?? -1);
  }, [shortVideoLayoutConfig]);

  return (
    <div className={classNames("h-full", className)}>
      <StateView isLoading={isLoading} isEmpty={!shortVideoLayoutConfig?.data}>
        <div className="h-full flex flex-col">
          <div className="mx-[15px] fixed top-[53px] z-10">
            <ScrollArea>
              <div>
                <FilterTabs
                  itemClassName="text-white"
                  value={value}
                  tabs={tabs}
                  onChange={setValue}
                />
              </div>
              <ScrollBar orientation="horizontal" />
            </ScrollArea>
          </div>
          <div className="flex-1 h-0">
            {tabs.map((tab, index) => (
              <ShortVideoTabContent
                key={index}
                index={index}
                isActive={index === value}
                data={tab}
              />
            ))}
          </div>
        </div>
      </StateView>
    </div>
  );
};
