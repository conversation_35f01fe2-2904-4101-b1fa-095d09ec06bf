import { Image } from "antd-mobile";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useUserWallet } from "@/hooks/use-user-wallet";
import { DefaultLoadImage } from "@/components/default-load-image";
import { UserInfo } from "@/components/work/user-info";
import { ArrowIcon } from "@/components/svg-icon";
import { userWalletType } from "@/utils/enums";
import { vipBanner } from "@/components/image-list";
import { Button } from "@/ui/button";
import { Link } from "react-router-dom";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

export const MyUserInfo = () => {
  const { navigateRoute } = useNavigateRoute();
  const { data: userInfo } = useFullUserInfo();
  const { getBalanceOrEmptyByType } = useUserWallet();

  const dataInfo = [
    {
      label: "我的粉丝",
      value: userInfo?.fansNumber ?? "-",
      uri: "/fans",
    },
    {
      label: "我的关注",
      value: userInfo?.followNumber ?? "-",
      uri: "/follow",
    },
    {
      label: `我的${userWalletType.currency.label}`,
      value: getBalanceOrEmptyByType(userWalletType.currency.value) ?? "-",
      uri: "/wallet",
    },
    {
      label: `我的${userWalletType.bean.label}`,
      value: getBalanceOrEmptyByType(userWalletType.bean.value) ?? "-",
      uri: "/wallet",
    },
  ];

  const handleVip = () => {
    navigateRoute("/vip");
  };

  const handleUserHome = () => {
    navigateRoute("/user-home");
  };

  return (
    <div className="relative">
      <div className="relative z-10 pt-[40px]">
        <div
          className="flex gap-[15px] px-[15px] items-center justify-between"
          onClick={handleUserHome}
        >
          <UserInfo
            user={userInfo!}
            transparent={false}
            size={66}
            className="!text-[17px] !text-white"
            onClick={handleUserHome}
          />
          <ArrowIcon size={17} color="#fff" />
        </div>
        <div className="pt-[25px] px-[15px] flex justify-between">
          {dataInfo.map((item, index) => (
            <Link
              key={index}
              to={item.uri}
              className="flex flex-col items-center"
            >
              <span className="text-[18px] text-white">{item.value}</span>
              <span className="text-[13px] text-white/75">{item.label}</span>
            </Link>
          ))}
        </div>
        <div className="relative h-[109px]" onClick={handleVip}>
          <Image src={vipBanner} width="100%" height="100%" fit="cover" />
          <Button className="absolute top-[38px] right-[33px] vip-banner-btn text-[13px] text-white rounded-[5px] h-[30px]">
            {userInfo?.vip ? "立即续费" : "立即开通"}
          </Button>
        </div>
      </div>
      <div className="absolute z-0 top-0 left-0 w-full h-full my-user-info-bg blur-[25px]">
        <Image
          width="100%"
          height="100%"
          src={userInfo?.avatarUrl}
          fit="cover"
          placeholder={<DefaultLoadImage />}
          fallback={<DefaultLoadImage />}
        />
      </div>
    </div>
  );
};
