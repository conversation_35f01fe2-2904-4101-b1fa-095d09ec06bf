import { ScrollLoadData } from "@/components/scroll-load-data";
import { getLayoutPageList } from "@/lib/api/layout";
import { PageParams } from "@/type";
import { BaseUrlConfig } from "@/type/layout-config";
import { useEffect, useState } from "react";
import { PullToRefresh } from "@/components/pull-to-refresh";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { ScrollArea } from "@/ui/scroll-area";
import { VideoItem } from "./video-item";
import { VideoResult } from "@/type/video-result";
import { parseObject } from "@/utils";

interface Props {
  index: number;
  isActive: boolean;
  filterValue?: Record<string, any>;
  data: BaseUrlConfig;
}

export const SubTabContent = (props: Props) => {
  const { index, isActive, data, filterValue = {} } = props;

  const [isInit, setIsInit] = useState(false);

  const requestData = { ...data.params, ...parseObject(filterValue) };
  // index 为了处理当params为 {}时，刷新时会刷新 所有数据，因为 只有url
  const queryKey = [data.url, index, requestData];
  const queryFn = (params: PageParams) => {
    return getLayoutPageList<VideoResult[]>({
      url: data.url,
      params,
      data: requestData,
    });
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  useEffect(() => {
    if (isActive) {
      setIsInit(true);
    }
  }, [isActive]);

  if (!isInit) {
    return null;
  }

  return (
    <ScrollArea className={isActive ? "h-full" : "hidden"}>
      <PullToRefresh onRefresh={handleRefresh}>
        <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="p-[15px] grid grid-cols-2 gap-[15px] md:grid-cols-3 lg:grid-cols-4">
              {data.map((item) => (
                <VideoItem key={item.id} data={item} />
              ))}
            </div>
          )}
        </ScrollLoadData>
      </PullToRefresh>
    </ScrollArea>
  );
};
