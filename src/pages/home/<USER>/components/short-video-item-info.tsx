import { Avatar } from "@/components/avatar";
import { CommonIcon } from "@/components/common-icon";
import { MonetizationOnIcon, PlusIcon } from "@/components/svg-icon";
import { confirmDialog } from "@/components/work/confirm-dialog";
import { ShareModal } from "@/components/work/share-modal";
import { ShortVideoCommentModal } from "@/components/work/short-video-comment-modal";
import {
  UserInfoModal,
  UserInfoModalRef,
} from "@/components/work/user-info-modal";
import { useDownloadQueueManager } from "@/hooks/use-download-queue-manager";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useModal } from "@/hooks/use-modal";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { userWalletQueryKey } from "@/hooks/use-user-wallet";
import { follow, unfollow } from "@/lib/api/user";
import {
  shortVideoBuy,
  shortVideoCancelCollection,
  shortVideoCollection,
  shortVideoDel,
  shortVideoDownload,
  shortVideoLike,
} from "@/lib/api/video";
import { queryClient } from "@/provider/query-client";
import { ShortVideoResult } from "@/type/short-video-result";
import {
  ReportTargetType,
  ShareTargetType,
  userWalletType,
} from "@/utils/enums";
import { formatDuration } from "@/utils/format-duration";
import { useMutation } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import classNames from "classnames";
import { produce } from "immer";
import { useRef } from "react";
import { useEffect, useState } from "react";

interface Props {
  isPlaying: boolean;
  data: ShortVideoResult;
  onReplaceData: (data: ShortVideoResult) => void;
  reload: () => void;
}

// todo: 纯净模式

export const ShortVideoItemInfo = (props: Props) => {
  const { isPlaying, data: shortVideoData, onReplaceData, reload } = props;

  const [data, setData] = useState(shortVideoData);

  const { data: userInfo } = useFullUserInfo();
  const { addToQueue } = useDownloadQueueManager();

  useEffect(() => {
    setData(shortVideoData);
  }, [shortVideoData]);

  const userInfoModalRef = useRef<UserInfoModalRef>(null);

  const openUserInfoModal = (id: string) => {
    userInfoModalRef.current?.open(id);
  };

  const {
    open: shareOpen,
    openModal: openShareModal,
    closeModal: closeShareModal,
  } = useModal();

  const {
    open: commentOpen,
    openModal: openCommentModal,
    closeModal: closeCommentModal,
  } = useModal();

  const followMutation = useMutation({
    mutationFn: follow,
  });
  const unfollowMutation = useMutation({
    mutationFn: unfollow,
  });

  const handleFollowChange = async () => {
    Toast.show({
      icon: "loading",
      content: "加载中...",
      duration: 0,
    });

    const mutation = data.user?.hasFollow ? unfollowMutation : followMutation;
    const { ok } = await mutation.mutateAsync(data.user?.id!);

    if (ok) {
      setData(
        produce((draft) => {
          draft.user!.hasFollow = !draft.user!.hasFollow;
        })
      );
    }
  };

  const likeMutation = useMutation({
    mutationFn: shortVideoLike,
  });

  const handleLikeChange = async () => {
    const { ok } = await likeMutation.mutateAsync({
      id: shortVideoData.id!,
      like: !shortVideoData.hasLike,
    });

    if (ok) {
      setData(
        produce((draft) => {
          draft.hasLike = !draft.hasLike;
          draft.likeNumber = draft.likeNumber! + (draft.hasLike ? 1 : -1);
        })
      );
    }
  };

  const collectionMutation = useMutation({
    mutationFn: shortVideoCollection,
  });

  const cancelCollectionMutation = useMutation({
    mutationFn: shortVideoCancelCollection,
  });

  const handleCollectionChange = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const mutation = data.hasCollect
      ? cancelCollectionMutation
      : collectionMutation;
    const { ok } = await mutation.mutateAsync(shortVideoData.id!);

    if (ok) {
      setData(
        produce((draft) => {
          draft.hasCollect = !draft.hasCollect;
          draft.collectNumber =
            draft.collectNumber! + (draft.hasCollect ? 1 : -1);
        })
      );
    }
  };

  const delMutation = useMutation({
    mutationFn: shortVideoDel,
  });

  const handleDel = async () => {
    const result = await confirmDialog.show(
      "是否删除此短视频，删除后不可恢复",
      "删除视频"
    );

    closeShareModal();

    if (result) {
      Toast.show({
        icon: "loading",
        content: "请稍后...",
        duration: 0,
      });

      const { ok } = await delMutation.mutateAsync(data.id!);

      if (ok) {
        reload();
      }
    }
  };

  const buyMutation = useMutation({
    mutationFn: shortVideoBuy,
  });

  const handleDownload = async () => {
    closeShareModal();

    if (data.hasBuyDownload) {
      handleDownloadVideo();
      return;
    }

    const result = await confirmDialog.show(
      `短视频下载价格：${data.downloadPrice ?? 0}，购买后可无限次下载`,
      "短视频下载购买"
    );

    if (result) {
      // 购买
      Toast.show({
        icon: "loading",
        content: "请稍后...",
        duration: 0,
      });

      const { ok } = await buyMutation.mutateAsync({
        id: data.id!,
        type: 1,
      });

      if (ok) {
        setData(
          produce((draft) => {
            draft.hasBuyDownload = true;
          })
        );
        Toast.show({
          content: "购买成功",
        });

        // 更新用户钱包
        queryClient.refetchQueries({
          queryKey: userWalletQueryKey,
        });

        handleDownloadVideo();
      }
    }
  };

  const downloadMutation = useMutation({
    mutationFn: shortVideoDownload,
  });

  const handleDownloadVideo = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok, data: downloadData } = await downloadMutation.mutateAsync(
      data.id!
    );

    if (ok && downloadData) {
      Toast.show({
        content: "开始下载",
      });

      addToQueue({
        title: "短视频下载",
        url: downloadData.url,
        params: {},
      });
    }
  };

  const customItem = [
    userInfo?.id !== data.user?.id
      ? {
          icon: "share/report",
          label: "举报",
          onClick: () => {
            navigateRoute("/report", {
              id: data?.id,
              type: ReportTargetType.ShortVideo,
            });
          },
        }
      : undefined,
    {
      icon: "share/download",
      label: "下载",
      onClick: handleDownload,
    },
    userInfo?.id === data.user?.id
      ? {
          icon: "share/del",
          label: "删除",
          onClick: handleDel,
        }
      : undefined,
  ].filter((item) => item !== undefined);

  const handleBuy = async () => {
    const result = await confirmDialog.show(
      `是否购买此短视频，价格:${data.playPrice ?? 0}${
        userWalletType.currency.label
      }`,
      "购买视频"
    );

    if (result) {
      Toast.show({
        icon: "loading",
        content: "请稍后...",
        duration: 0,
      });

      const { ok, data: buyData } = await buyMutation.mutateAsync({
        id: data.id!,
        type: 0,
      });

      onReplaceData(data);

      if (ok && buyData) {
        Toast.show({
          content: "购买成功",
        });

        // 更新用户钱包
        queryClient.refetchQueries({
          queryKey: userWalletQueryKey,
        });

        onReplaceData(buyData);
      }
    }
  };

  const { navigateRoute } = useNavigateRoute();

  const handleTagClick = (id: string, name: string) => {
    navigateRoute("/short-video-tag", {
      id,
      name,
    });
  };

  return (
    <div className="absolute bottom-0 left-0 right-0 top-0 pointer-events-none">
      {/** 右侧操作按钮 */}
      <div className="absolute right-[15px] bottom-[100px] flex flex-col gap-y-3 items-center text-white text-[13px]">
        <div className="relative pointer-events-auto mb-[9px]">
          <Avatar
            src={data.user?.avatarUrl!}
            size={42}
            className="border border-solid border-white"
            onClick={() => openUserInfoModal(data.user?.id!)}
          />
          {!data.user?.hasFollow ? (
            <span
              className="absolute bottom-[-9px] left-1/2 -translate-x-1/2 w-[18px] h-[18px] inline-flex items-center justify-center bg-[#FF005C] rounded-full"
              onClick={handleFollowChange}
            >
              <PlusIcon size={15} color="#fff" />
            </span>
          ) : null}
        </div>
        <div
          className="flex flex-col items-center gap-[2px] pointer-events-auto"
          onClick={handleLikeChange}
        >
          <CommonIcon
            name={
              data.hasLike ? "short-video/like-selected" : "short-video/like"
            }
            w={32}
            h={29}
          />
          <span>{data.likeNumber}</span>
        </div>
        <div
          className="flex flex-col items-center gap-[2px] pointer-events-auto"
          onClick={() => openCommentModal()}
        >
          <CommonIcon name="short-video/comment" w={30} h={30} />
          <span>{data.commentNumber}</span>
        </div>
        <div
          className="flex flex-col items-center gap-[2px] pointer-events-auto"
          onClick={handleCollectionChange}
        >
          <CommonIcon
            name={
              data.hasCollect
                ? "short-video/collection-selected"
                : "short-video/collection"
            }
            w={31}
            h={29}
          />
          <span>{data.collectNumber}</span>
        </div>
        <div
          className="flex flex-col items-center gap-[2px] pointer-events-auto"
          onClick={() => openShareModal()}
        >
          <CommonIcon name="short-video/share" w={30} h={30} />
          <span>{data.shareNumber}</span>
        </div>
      </div>
      {/* 底部用户信息 */}
      <div
        className="absolute bottom-[20px] left-[15px] right-[75px] flex flex-col gap-[5px] pointer-events-auto"
        onClick={() => openUserInfoModal(data.user?.id!)}
      >
        {data.tags?.length ? (
          <div
            className={classNames(
              "flex flex-wrap gap-[5px] transition-opacity duration-500",
              !isPlaying ? "opacity-100" : "opacity-0"
            )}
          >
            {data.tags.map((item) => (
              <div
                className="text-white text-xs px-[6px] py-[2px] rounded-[5px] bg-[#000000]/50"
                onClick={(e) => {
                  e.stopPropagation();
                  handleTagClick(item.id!, item.name!);
                }}
              >
                #{item.name}#
              </div>
            ))}
          </div>
        ) : null}
        {data.address || (data.playNumber ?? 0) > 0 || data.featured ? (
          <div
            className={classNames(
              "flex items-start gap-[5px] transition-opacity duration-500",
              !isPlaying ? "opacity-100" : "opacity-0"
            )}
          >
            {data.address ? (
              <div className="px-[6px] py-1 rounded-[5px] bg-white/30 gap-[5px] flex items-center">
                <CommonIcon name="location" w={12} h={12} />
                <span className="text-xs text-white">{data.address}</span>
              </div>
            ) : null}
            {(data.playNumber ?? 0) > 0 ? (
              <div className="px-[6px] py-1 rounded-[5px] short-video-price-bg gap-[5px] flex items-center">
                <span className="text-xs text-white">
                  ￥{data.playPrice ?? 0}
                  {userWalletType.currency.label} |{" "}
                  {formatDuration(data.duration!)}
                </span>
              </div>
            ) : null}
            {data.featured ? (
              <div className="px-[6px] py-1 rounded-[5px] short-video-featured-bg gap-[5px] flex items-center">
                <span className="text-xs text-white">精品</span>
              </div>
            ) : null}
          </div>
        ) : null}
        <div className="flex flex-col">
          <span
            className={classNames(
              "text-base text-white transition-opacity duration-500",
              !isPlaying ? "opacity-100" : "opacity-0"
            )}
          >
            @{data.user?.nickname}
          </span>
          <span
            className={classNames(
              "text-[15px] text-white line-clamp-3 transition-opacity duration-500",
              !isPlaying ? "opacity-100" : "opacity-0"
            )}
          >
            {data.content}
          </span>
          <span
            className={classNames(
              "text-sm transition-opacity duration-500",
              data.hasBuyPlayer ? "text-white" : "text-[#FF005C]",
              !isPlaying && !data.hasBuyPlayer ? "opacity-100" : "opacity-0"
            )}
          >
            {data.tips}
          </span>
        </div>
      </div>
      {/* 立即购买按钮 */}
      {!data.hasBuyPlayer ? (
        <div className="absolute bottom-[150px] left-0 right-0 flex justify-center">
          <div
            className="py-[2px] px-[10px] rounded-[20px] bg-white/20 flex gap-[5px] pointer-events-auto"
            onClick={handleBuy}
          >
            <MonetizationOnIcon size={16} className="text-white" />
            <span className="text-white">立即购买</span>
          </div>
        </div>
      ) : null}
      <ShareModal
        type={ShareTargetType.ShortVideo}
        rowSize={5}
        open={shareOpen}
        customItem={customItem}
        onClose={closeShareModal}
      />
      <UserInfoModal ref={userInfoModalRef} />
      <ShortVideoCommentModal
        data={data}
        open={commentOpen}
        onClose={closeCommentModal}
      />
    </div>
  );
};
