import { CommonIcon } from "@/components/common-icon";
import { DefaultLoadImage } from "@/components/default-load-image";
import { UserLevelTag } from "@/components/work/user-level-tag";
import { LiveRoomSimpleResult } from "@/type/live_room_simple_result";
import { Image } from "antd-mobile";
interface Props {
  data: LiveRoomSimpleResult;
  onClick: () => void;
}

export const TabItem = ({ data, onClick }: Props) => {
  const { user, cover, chargeTips, memberText, address, note } = data;

  return (
    <div className="relative" onClick={onClick}>
      <Image
        src={cover ?? ""}
        width="100%"
        height="auto"
        fit="cover"
        className="rounded-[5px] min-h-[120px] max-h-[240px]"
        lazy
        placeholder={<DefaultLoadImage className="h-[240px]" />}
        fallback={<DefaultLoadImage className="h-[240px]" />}
      />
      <div className="absolute top-0 left-0 w-full h-full bg-white/30 p-[9px] flex flex-col justify-between rounded-[5px]">
        <div className="flex flex-col gap-[5px]">
          <div className="flex justify-between">
            {chargeTips ? (
              <span className="live-charge-tips rounded-[2px] leading-[1] text-white text-xs px-[5px] py-[2px] flex items-center">
                {chargeTips}
              </span>
            ) : (
              <span />
            )}
            <span className="rounded-[8px] bg-white/30 text-xs text-[#6712FF] px-[5px] py-[2px]">
              {memberText}
            </span>
          </div>
          {address ? (
            <div className="flex gap-[5px]">
              <CommonIcon name="location" w={13} h={13} />
              <span className="text-[13x] leading-[1] text-white line-clamp-1">
                {address}
              </span>
            </div>
          ) : null}
        </div>
        <div className="flex flex-col gap-[5px]">
          <div className="flex items-center">
            <span className="mr-[10px] text-[15px] leading-[1] text-white line-clamp-1">
              {user?.nickname}
            </span>
            <UserLevelTag user={user!} />
          </div>
          <div className="text-white text-[13px] leading-[1] line-clamp-2">
            {note}
          </div>
        </div>
      </div>
    </div>
  );
};
