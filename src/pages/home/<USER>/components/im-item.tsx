import { Avatar } from "@/components/avatar";
import { IConversationPreviewUIProps } from "@tencentcloud/chat-uikit-react";
import { Badge } from "antd-mobile";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { getTimeStringForChat } from "@/utils/format-time";
import { lastMessageBuild } from "@/utils/last-message-build";

export const ImItem = (props: IConversationPreviewUIProps) => {
  const { conversation } = props;

  const userProfile = conversation?.userProfile;

  const lastMessage = conversation?.lastMessage;

  const unreadCount =
    conversation.unreadCount > 0 ? conversation.unreadCount : undefined;

  console.log("imItem", props);

  const { navigateRoute } = useNavigateRoute();

  const handleClick = () => {
    navigateRoute(`/chat?cid=${conversation.conversationID}`);
  };

  return (
    <div
      className="large-badge flex gap-2 items-center py-3"
      onClick={handleClick}
    >
      <Badge content={unreadCount} style={{ "--top": "4px", "--right": "6px" }}>
        <Avatar src={userProfile.avatar} size={50} />
      </Badge>
      <div className="w-full">
        <div className="flex justify-between items-center">
          <span className="text-base flex-1 line-clamp-1">
            {userProfile.nick}
          </span>
          <span className="text-xs text-hint-color">
            {getTimeStringForChat(Number(lastMessage?.lastTime))}
          </span>
        </div>
        <div className="text-sm text-hint-color line-clamp-1">
          {lastMessageBuild(conversation.lastMessage as any)}
        </div>
      </div>
    </div>
  );
};
