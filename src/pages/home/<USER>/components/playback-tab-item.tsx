import { CommonIcon } from "@/components/common-icon";
import { DefaultLoadImage } from "@/components/default-load-image";
import { PlayIcon } from "@/components/svg-icon";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { PlaybackVideoResult } from "@/type/playback-video-result";
import { AspectRatio } from "@/ui/aspect-ratio";
import { formatDuration } from "@/utils/format-duration";
import { formatTimeLine } from "@/utils/format-time-line";
import { Image } from "antd-mobile";
import dayjs from "dayjs";

interface Props {
  data: PlaybackVideoResult;
}

export const PlaybackTabItem = ({ data }: Props) => {
  const {
    id,
    cover,
    title,
    playPrice = 0,
    releaseDate,
    playNumber,
    duration = 0,
  } = data;

  const { navigateRoute } = useNavigateRoute();

  const handleClick = () => {
    navigateRoute("/playback-video-detail", {
      id,
    });
  };

  return (
    <AspectRatio ratio={1}>
      <div className="flex flex-col w-full h-full" onClick={handleClick}>
        <div className="flex-1 h-0 relative">
          <Image
            src={cover ?? ""}
            width="100%"
            height="100%"
            fit="cover"
            className="rounded-t-[5px]"
            lazy
            placeholder={<DefaultLoadImage />}
            fallback={<DefaultLoadImage />}
          />
          <div className="absolute top-0 left-0 w-full h-full px-[5px] flex items-end bg-black/30">
            <div className="flex justify-between w-full">
              <div className="flex items-center gap-1 text-white">
                <PlayIcon size={11} />
                <span
                  className="text-[22px]"
                  style={{
                    zoom: 0.5,
                  }}
                >
                  {playNumber}
                </span>
              </div>
              <span
                className="text-[22px]"
                style={{
                  zoom: 0.5,
                }}
              >
                {formatDuration(duration)}
              </span>
            </div>
          </div>
        </div>
        <div className="flex-none rounded-b-[5px] py-2 px-[5px] bg-app-bar-background">
          <span className="line-clamp-1">{title}</span>
          <div className="flex justify-between">
            <span className="flex gap-1 items-center">
              <CommonIcon name="currency" w={16} h={10} />
              <span
                className="text-hint-color text-[22px]"
                style={{
                  zoom: 0.5,
                }}
              >
                {playPrice}
              </span>
            </span>
            <span
              className="text-hint-color text-[22px]"
              style={{
                zoom: 0.5,
              }}
            >
              {formatTimeLine(dayjs(releaseDate).valueOf())}
            </span>
          </div>
        </div>
      </div>
    </AspectRatio>
  );
};
