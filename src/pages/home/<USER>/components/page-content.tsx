import { ReactNode, useEffect, useState } from "react";
import classNames from "classnames";
import { PullToRefresh } from "@/components/pull-to-refresh";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { ScrollArea } from "@/ui/scroll-area";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { PageParams } from "@/type";
import { CommunityTabType } from "@/type/layout-config";
import { getLayoutPageList } from "@/lib/api/layout";
import { parseObject } from "@/utils";

interface Props<T> {
  index: number;
  type: string;
  isActive: boolean;
  filterValue: Record<string, any>;
  data: CommunityTabType;
  children: (data: T[]) => ReactNode;
  extraContent?: (config: CommunityTabType) => ReactNode;
}

export const PageContent = <T,>(props: Props<T>) => {
  const { index, type, isActive, filterValue, data, extraContent, children } =
    props;

  const [isInit, setIsInit] = useState(false);

  useEffect(() => {
    if (isActive) {
      setIsInit(true);
    }
  }, [isActive]);

  const requestData = { ...data.params, ...parseObject(filterValue) };

  const queryKey = [type, data.url, index, requestData];
  const queryFn = (params: PageParams) => {
    return getLayoutPageList<T[]>({
      url: data.url,
      params,
      data: requestData,
    });
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  if (!isInit) {
    return null;
  }

  return (
    <ScrollArea className={classNames("h-full", isActive ? "" : "hidden")}>
      <PullToRefresh onRefresh={handleRefresh}>
        {extraContent ? extraContent(data) : null}
        <div className="w-full h-full">
          <ScrollLoadData size={10} queryKey={queryKey} queryFn={queryFn}>
            {children}
          </ScrollLoadData>
        </div>
      </PullToRefresh>
    </ScrollArea>
  );
};
