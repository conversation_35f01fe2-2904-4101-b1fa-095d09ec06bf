import { ShortVideoResult } from "@/type/short-video-result";
import { useEffect, useRef, useState } from "react";
import videojs from "video.js";
import Player from "video.js/dist/types/player";
import "./short-video-player.less";
import { ShortVideoItemInfo } from "./short-video-item-info";

interface Props {
  isActive: boolean;
  data: ShortVideoResult;
  index: number;
  onReplaceData: (index: number, data: ShortVideoResult) => void;
  reload: () => void;
}

export const ShortVideoPlayer = (props: Props) => {
  const { isActive, data, index, onReplaceData, reload } = props;

  const [isPlaying, setIsPlaying] = useState(false);

  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<Player>();

  useEffect(() => {
    if (isActive && videoRef.current && !playerRef.current) {
      playerRef.current = videojs(videoRef.current, {
        preload: "auto",
        controls: true,
        loop: true,
        enableSmoothSeeking: true,
        language: "zh-CN",
        // 静音可自动播放，但需要手动调音量
        muted: true,
      });

      playerRef.current?.src(data.video);

      playerRef.current.one("canplay", () => {
        if (isActive) {
          playerRef.current?.play();
        }
      });
    }

    const handleTap = () => {
      if (playerRef.current?.paused()) {
        playerRef.current?.play();
      } else {
        playerRef.current?.pause();
      }
    };

    // 获取视频元素
    const videoElement = playerRef.current
      ?.el()
      .querySelector(".vjs-tech") as HTMLVideoElement | null;

    if (videoElement) {
      videoElement.addEventListener("touchend", handleTap);
    }

    // 清理函数
    return () => {
      if (videoElement) {
        videoElement.removeEventListener("touchend", handleTap);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isActive, data.video]);

  useEffect(() => {
    const player = playerRef.current;
    if (player && player.readyState() >= 2) {
      if (isActive) {
        player.play();
      } else {
        player.pause();
      }
    }
  }, [isActive]);

  useEffect(() => {
    const player = playerRef.current;

    const handlePlayPause = () => {
      const isPlaying = !player?.paused();

      setIsPlaying(isPlaying);
    };

    player?.on("play", handlePlayPause);
    player?.on("pause", handlePlayPause);

    return () => {
      player?.off("play", handlePlayPause);
      player?.off("pause", handlePlayPause);
    };
  }, []);

  const handleReplaceData = (data: ShortVideoResult) => {
    onReplaceData(index, data);
  };

  return (
    <div className="short-video-player w-full h-full relative z-10">
      <video
        ref={videoRef}
        className="video-js vjs-default-skin w-full h-full"
      />
      <ShortVideoItemInfo
        isPlaying={isPlaying}
        data={data}
        onReplaceData={handleReplaceData}
        reload={reload}
      />
    </div>
  );
};
