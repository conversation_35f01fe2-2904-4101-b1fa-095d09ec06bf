import { CommonIcon } from "@/components/common-icon";
import { ArrowIcon } from "@/components/svg-icon";
import { TopicTag } from "@/components/topic-tag";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { DynamicTopic } from "@/type/dynamic-topic";

interface Props {
  data: DynamicTopic[];
}

export const Topic = (props: Props) => {
  const { data } = props;

  const { navigateRoute } = useNavigateRoute();

  const handleClick = () => {
    navigateRoute("/topic");
  };

  return (
    <div
      className="h-[35px] my-[10px] px-[10px] rounded-[5px] bg-white dark:bg-white/10 flex items-center gap-1 justify-between"
      onClick={handleClick}
    >
      <div className="flex-none flex items-center gap-2 mr-[5px]">
        <CommonIcon name="topic" w={25} h={23} />
        <span className="font-medium text-[13px]">热门话题</span>
      </div>
      <div className="gap-2 flex text-sm">
        {data.map((item) => (
          <TopicTag key={item.id} data={item} />
        ))}
        <ArrowIcon color="#fff" size={12} />
      </div>
    </div>
  );
};
