import { ThemeIcon } from "@/components/theme-icon";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { useUserMenus } from "@/hooks/use-user-menus";
import { useTheme } from "@/provider/useTheme";
import { getRoute } from "@/router/route-map";
import { UserMenu } from "@/type/user-menu";
import { AspectRatio } from "@/ui/aspect-ratio";
import { Badge } from "antd-mobile";

const formatIcon = (icon: UserMenu["icon"], theme: string) => {
  const url = theme === "light" ? icon.light : icon.dark;

  // 通用格式化为 menus/xxx，支持字母、数字、下划线和连字符
  return url.replace(/.*\/(menus\/[\w-]+)\.png$/, "$1");
};

export const MyMenus = () => {
  const { data: menus = [] } = useUserMenus();

  const theme = useTheme();
  const { navigateRoute } = useNavigateRoute();

  const handleClick = (uri?: string) => {
    if (uri) {
      navigateRoute(getRoute(uri));
    }
  };

  return (
    <div className="grid grid-cols-4 bg-white dark:bg-[#111722] rounded-[5px]">
      {menus.map((menu) => (
        <AspectRatio key={menu.uri} ratio={1}>
          <div
            className="w-full h-full flex flex-col items-center justify-center gap-[9px]"
            onClick={() => handleClick(menu.uri)}
          >
            <Badge content={menu.badge} style={{ "--top": "8px" }}>
              <ThemeIcon name={formatIcon(menu.icon, theme)} w={28} h={28} />
            </Badge>
            <span>{menu.name}</span>
          </div>
        </AspectRatio>
      ))}
    </div>
  );
};
