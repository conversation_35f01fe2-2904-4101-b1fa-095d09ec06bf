import { ScrollLoadData } from "@/components/scroll-load-data";
import { getLayoutPageList } from "@/lib/api/layout";
import { PageParams } from "@/type";
import { BaseUrlConfig } from "@/type/layout-config";
import { useEffect, useState } from "react";
import { TabItem } from "./tab-item";
import { PullToRefresh } from "@/components/pull-to-refresh";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { ScrollArea } from "@/ui/scroll-area";
import { LiveRoomSimpleResult } from "@/type/live_room_simple_result";
import { LivePlayback } from "./live-playback";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

interface Props {
  index: number;
  isActive: boolean;
  data: BaseUrlConfig;
}

export const TabContent = (props: Props) => {
  const { index, isActive, data } = props;

  const [isInit, setIsInit] = useState(false);

  // index 为了处理当params为 {}时，刷新时会刷新 所有数据，因为 只有url
  const queryKey = [data.url, index, data.params];
  const queryFn = (params: PageParams) => {
    return getLayoutPageList<LiveRoomSimpleResult[]>({
      url: data.url,
      params,
      data: { ...data.params },
    });
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  const { navigateRoute } = useNavigateRoute();

  const handleClickItem = (data: LiveRoomSimpleResult, index: number) => {
    navigateRoute("/live-list", {
      defaultIndex: index,
      queryKey: JSON.stringify(queryKey),
    });
  };

  useEffect(() => {
    if (isActive) {
      setIsInit(true);
    }
  }, [isActive]);

  if (!isInit) {
    return null;
  }

  if (data.url === "livePlayback") {
    return <LivePlayback className={isActive ? "h-full" : "hidden"} />;
  }

  return (
    <ScrollArea className={isActive ? "h-full" : "hidden"}>
      <PullToRefresh onRefresh={handleRefresh}>
        <ScrollLoadData size={10} queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="p-[15px] w-full grid grid-cols-2 gap-[15px]">
              <div className="flex flex-col gap-[15px]">
                {data.map((item, index) =>
                  index % 2 === 0 ? (
                    <TabItem
                      key={item.id}
                      data={item}
                      onClick={() => handleClickItem(item, index)}
                    />
                  ) : null
                )}
              </div>
              <div className="flex flex-col gap-[15px]">
                {data.map((item, index) =>
                  index % 2 === 1 ? (
                    <TabItem
                      key={item.id}
                      data={item}
                      onClick={() => handleClickItem(item, index)}
                    />
                  ) : null
                )}
              </div>
            </div>
          )}
        </ScrollLoadData>
      </PullToRefresh>
    </ScrollArea>
  );
};
