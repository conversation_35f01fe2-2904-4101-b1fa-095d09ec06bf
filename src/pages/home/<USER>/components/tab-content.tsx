import { BaseUrlConfig, Filter } from "@/type/layout-config";
import { useEffect, useState } from "react";
import { <PERSON>roll<PERSON><PERSON>, ScrollBar } from "@/ui/scroll-area";
import { ShortVideo } from "./short-video";
import { SubTabContent } from "./sub-tab-content";
import classNames from "classnames";
import { FilterTabs } from "@/components/work/filter-tabs";
import { MoreIcon } from "@/components/svg-icon";
import { FilterPopup } from "@/components/work/filter-popup";

interface Props {
  index: number;
  isActive: boolean;
  data: BaseUrlConfig & {
    children?: BaseUrlConfig[];
  };
  filter?: Filter[];
}

export const TabContent = (props: Props) => {
  const { index, isActive, data, filter } = props;

  const [isInit, setIsInit] = useState(false);
  const [value, setValue] = useState(0);
  const [visible, setVisible] = useState(false);
  const [filterValue, setFilterValue] = useState<Record<string, any>>({});

  const subTabs = data.children?.map((item, index) => ({
    ...item,
    key: index,
  }));

  useEffect(() => {
    if (isActive) {
      setIsInit(true);
    }
  }, [isActive]);

  if (!isInit) {
    return null;
  }

  if (data.url === "shortVideo") {
    return <ShortVideo className={isActive ? "h-full" : "hidden"} />;
  }

  return (
    <div
      className={classNames(
        "h-full flex flex-col",
        isActive ? "h-full" : "hidden"
      )}
    >
      <div className="mx-[15px] flex">
        <div className="flex-1 w-0">
          {subTabs?.length ? (
            <ScrollArea>
              <div>
                <FilterTabs value={value} tabs={subTabs} onChange={setValue} />
              </div>
              <ScrollBar orientation="horizontal" />
            </ScrollArea>
          ) : null}
        </div>
        <div className="flex-none">
          <span
            className="w-[25px] h-[25px] rounded-full bg-app-bar-background flex justify-center items-center"
            onClick={() => setVisible(true)}
          >
            <MoreIcon size={20} className="text-hint-color" />
          </span>
          <FilterPopup
            title="视频过滤"
            visible={visible}
            value={filterValue}
            onChange={setFilterValue}
            data={filter ?? []}
            onClose={() => setVisible(false)}
          />
        </div>
      </div>
      {subTabs?.length ? (
        // 存在二级菜单
        <div className="flex-1 h-0">
          {subTabs.map((subItem, i) => (
            <SubTabContent
              key={subItem.key}
              index={i}
              isActive={i === value}
              data={subItem}
              filterValue={filterValue}
            />
          ))}
        </div>
      ) : (
        // 不存在二级菜单
        <div className="flex-1 h-0">
          <SubTabContent
            index={index}
            isActive={isActive}
            data={data}
            filterValue={filterValue}
          />
        </div>
      )}
    </div>
  );
};
