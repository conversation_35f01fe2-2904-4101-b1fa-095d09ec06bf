import { CommonIcon } from "@/components/common-icon";
import { UserAvatar } from "@/components/user-avatar";
import { ChatRoomSimpleResult } from "@/type/chat-room-simple-result";
import { userWalletType } from "@/utils/enums";

interface Props {
  data: ChatRoomSimpleResult;
}

export const ChatRoomItem = (props: Props) => {
  const { data } = props;

  return (
    <div className="chat-room-bg py-2 px-[10px] rounded-[15px] flex flex-col gap-2">
      <div className="flex items-center justify-between h-[20px]">
        <div className="flex items-center">
          {data.price ? (
            <>
              <CommonIcon name="gold" w={19} h={19} />
              <div className="chat-room-gold-bg text-xs h-4 rounded-[2px] pl-2 pr-1 relative left-[-4px]">
                {data.price}
                {userWalletType.currency.label}
              </div>
            </>
          ) : null}
        </div>
        <div className="flex gap-[2px] items-center">
          {data.address ? (
            <>
              <CommonIcon name="location" w={12} h={12} />
              <span className="text-[13px] text-[#BABCBE]">{data.address}</span>
            </>
          ) : null}
        </div>
      </div>
      <div className="flex justify-center">
        <UserAvatar src={data.cover ?? ""} size={72} />
      </div>
      <div className="flex flex-col items-center">
        <div className="font-medium text-[15px] text-[#333] dark:text-foreground line-clamp-1">
          {data.name}
        </div>
        <div className="text-xs text-[#999] dark:text-[#babcbe]">
          {data.tags?.join(" · ")}
        </div>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex">
          {data.memberAvatars?.map((avatar, i) => (
            <UserAvatar key={i} src={avatar} size={15} />
          ))}
        </div>
        <div className="flex items-center gap-1">
          <CommonIcon name="chat_room_member" w={12} h={12} />
          <span className="text-[13px] text-[#9E89C7]">
            {data.memberNumber}人
          </span>
        </div>
      </div>
    </div>
  );
};
