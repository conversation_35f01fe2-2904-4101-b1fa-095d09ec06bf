import { ChatRoomItem } from "./chat-room-item";
import { ChatRoomSimpleResult } from "@/type/chat-room-simple-result";

interface Props {
  data: ChatRoomSimpleResult[];
}

export const ChatRoomContent = (props: Props) => {
  const { data } = props;

  return (
    <div className="py-3 grid grid-cols-2 gap-[15px] md:grid-cols-3 lg:grid-cols-4">
      {data.map((item) => (
        <ChatRoomItem key={item.id} data={item} />
      ))}
    </div>
  );
};
