import { useEffect, useState } from "react";
import { PlusIcon } from "@/components/svg-icon";
import { LayoutConfigChatRoomHome } from "@/type/layout-config";
import { ChatRoomContent } from "./components/chat-room-content";
import { PageWrapper } from "../components/page-wrapper";
import { ChatRoomSimpleResult } from "@/type/chat-room-simple-result";
import { useQuery } from "@tanstack/react-query";
import { layoutConfigOptions } from "@/utils/query-options";
import { LayoutConfigKey } from "@/utils/layout-config-key";

export default function ChatRoomPage() {
  const { data: config } = useQuery(
    layoutConfigOptions(LayoutConfigKey.ChatRoomHome)
  );

  const data = config?.data as LayoutConfigChatRoomHome;

  const [value, setValue] = useState(0);

  useEffect(() => {
    if (data?.tabs.defaultIndex) {
      setValue(data.tabs.defaultIndex);
    }
  }, [data?.tabs.defaultIndex]);

  const tabList =
    data?.tabs.items.map((item, index) => ({
      ...item,
      key: index,
    })) ?? [];

  return (
    <PageWrapper<ChatRoomSimpleResult>
      filter={{
        filter: data?.filter,
        value,
        onChange: setValue,
        tabs: tabList,
        moreAction: (
          <div className="chat-room-create-bg text-[#fff] dark:text-[#cee0ff] flex items-center h-[25px] px-2 rounded-full text-xs">
            <PlusIcon />
            创建
          </div>
        ),
      }}
      content={{
        type: "chat-room",
        data: tabList,
        children: (data) => <ChatRoomContent data={data} />,
      }}
    />
  );
}
