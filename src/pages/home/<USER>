import { getRoute } from "@/router/route-map";
import { LayoutConfigHome } from "@/type/layout-config";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { layoutConfigOptions } from "@/utils/query-options";
import { useQuery } from "@tanstack/react-query";
import { Navigate } from "react-router-dom";

/**
 * 主要用于中间层跳转
 * 不会存在其他 ui
 */
export default function Home() {
  const { data } = useQuery(
    layoutConfigOptions<LayoutConfigHome>(LayoutConfigKey.Home)
  );

  const { defaultIndex = 0, items = [] } =
    data?.data?.bottomNavigationBar || {};

  const route = getRoute(items[defaultIndex]?.uri);

  if (route) {
    return <Navigate to={route} replace />;
  }

  return null;
}
