import { useEffect, useState } from "react";
import { LayoutConfigUserPlazaHome } from "@/type/layout-config";
import { UserPlazaContent } from "./components/user-plaza-content";
import { PageWrapper } from "../components/page-wrapper";
import { User } from "@/type/user";
import { ExtraContent } from "./components/extra-content";
import { useQuery } from "@tanstack/react-query";
import { layoutConfigOptions } from "@/utils/query-options";
import { LayoutConfigKey } from "@/utils/layout-config-key";

export default function UserPlazaPage() {
  const { data: config } = useQuery(
    layoutConfigOptions(LayoutConfigKey.UserPlazaHome)
  );

  const data = config?.data as LayoutConfigUserPlazaHome;

  const [value, setValue] = useState(0);

  useEffect(() => {
    if (data?.tabs.defaultIndex) {
      setValue(data.tabs.defaultIndex);
    }
  }, [data?.tabs.defaultIndex]);

  const tabList =
    data?.tabs.items.map((item, index) => ({
      ...item,
      key: index,
    })) ?? [];

  return (
    <PageWrapper<User>
      filter={{
        filter: data?.filter,
        value,
        onChange: setValue,
        tabs: tabList,
      }}
      content={{
        type: "user-plaza",
        data: tabList,
        children: (data) => <UserPlazaContent data={data} />,
        extraContent: (config) => <ExtraContent config={config} />,
      }}
    />
  );
}
