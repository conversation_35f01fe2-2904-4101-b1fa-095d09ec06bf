import { UserPlazaItem } from "./user-plaza-item";
import { User } from "@/type/user";

interface Props {
  data: User[];
}

export const UserPlazaContent = (props: Props) => {
  const { data } = props;

  return (
    <div className="py-3 grid grid-cols-2 gap-[10px] md:grid-cols-3 lg:grid-cols-4">
      {data.map((item) => (
        <UserPlazaItem key={item.id} data={item} />
      ))}
    </div>
  );
};
