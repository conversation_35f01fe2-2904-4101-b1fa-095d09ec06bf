import { CommonIcon } from "@/components/common-icon";
import { DefaultLoadImage } from "@/components/default-load-image";
import {
  UserInfoModal,
  UserInfoModalRef,
} from "@/components/work/user-info-modal";
import { UserLevelTag } from "@/components/work/user-level-tag";
import { UserTagList } from "@/components/work/user-tag-list";
import { User } from "@/type/user";
import { AspectRatio } from "@/ui/aspect-ratio";
import { ScrollArea, ScrollBar } from "@/ui/scroll-area";
import { UserOnlineState } from "@/utils/enums";
import { Image } from "antd-mobile";
import { useRef } from "react";

interface Props {
  data: User;
}

export const UserPlazaItem = (props: Props) => {
  const { data } = props;

  const userInfoModalRef = useRef<UserInfoModalRef>(null);

  const handleClick = () => {
    userInfoModalRef.current?.open(data.id!);
  };

  return (
    <AspectRatio ratio={1}>
      <div
        className="rounded-[8px] w-full h-full p-2 flex flex-col justify-between relative z-10"
        onClick={handleClick}
      >
        <div className="absolute top-0 left-0 w-full h-full z-[-1]">
          <Image
            src={data.avatarUrl}
            fit="cover"
            className="w-full h-full rounded-[8px]"
            placeholder={<DefaultLoadImage />}
            fallback={<DefaultLoadImage />}
          />
        </div>
        <div className="flex items-center justify-between">
          <div>
            {data.creditScore && data.creditScore > 0 ? (
              <div className="user-plaza-item-recommend text-xs text-white px-1 rounded-[2px] h-[17px]">
                推荐
              </div>
            ) : null}
          </div>
          <div className="flex gap-[2px] items-center">
            {data.realAddress ? (
              <>
                <CommonIcon name="location" w={12} h={12} />
                <span className="text-[13px] text-white">
                  {data.realAddress}
                </span>
              </>
            ) : null}
          </div>
        </div>
        <div>
          <div className="flex items-center">
            {data.onlineState === UserOnlineState.online ? (
              <span className="flex-none w-[10px] h-[10px] mr-[2px] rounded-full bg-[#46DE3F] border-solid border-[2px] border-[rgba(156,156,156,0.5)]" />
            ) : null}
            <div className="flex gap-[5px] items-center">
              <div className="line-clamp-1 text-[15px]">{data.nickname}</div>
              <UserLevelTag user={data} />
            </div>
          </div>
          <ScrollArea>
            <UserTagList user={data} />
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </div>
        <UserInfoModal ref={userInfoModalRef} />
      </div>
    </AspectRatio>
  );
};
