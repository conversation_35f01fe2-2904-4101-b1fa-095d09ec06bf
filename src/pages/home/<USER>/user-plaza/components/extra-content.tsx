import { useDefaultResource } from "@/hooks/use-default-avatar";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { CommunityTabType } from "@/type/layout-config";
import { Image } from "antd-mobile";

interface Props {
  config: CommunityTabType;
}
export const ExtraContent = (props: Props) => {
  const { config } = props;

  const { recommendUserBanner } = useDefaultResource();

  const bannerData = recommendUserBanner?.[0];

  const { navigateRoute } = useNavigateRoute();

  const handleClick = () => {
    navigateRoute("/user-margin");
  };

  return (
    <>
      {config.custom?.showBanner && bannerData ? (
        <div className="my-[10px]" onClick={handleClick}>
          <Image
            src={bannerData.resourceUrl}
            className="h-[125px] w-auto rounded-[5px]"
            fit="cover"
          />
        </div>
      ) : null}
    </>
  );
};
