import { DefaultLoadImage } from "@/components/default-load-image";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { UserTransactionResult } from "@/type/user-transaction-result";
import { UserTransactionState, UserTransactionType } from "@/utils/enums";
import { Image } from "antd-mobile";

interface Props {
  data: UserTransactionResult;
}

export const TradeItem = (props: Props) => {
  const { data } = props;

  const { navigateRoute } = useNavigateRoute();

  const handleClick = () => {
    navigateRoute("/user-transaction-detailed", {
      id: data.id,
    });
  };

  return (
    <div className="w-full h-full flex flex-col" onClick={handleClick}>
      <div className="w-full flex-1 relative">
        <Image
          src={data.cover}
          fit="cover"
          width="100%"
          height="100%"
          placeholder={<DefaultLoadImage />}
          fallback={<DefaultLoadImage />}
          className="rounded-t-[10px]"
          lazy
        />
        <div className="absolute left-[5px] top-[5px] flex gap-[5px] items-center px-[5px] py-[2px] bg-white/80 rounded-[5px]">
          <span className="flex-none w-[6px] h-[6px] rounded-full bg-[#46DE3F] border-solid border-[2px] border-[#46DE3F]/50" />
          <span className="text-[22px] text-black" style={{ zoom: 0.5 }}>
            {UserTransactionState[data.state!]}
          </span>
        </div>
        {data.deposit ? (
          <div className="absolute right-[5px] top-[5px] flex rounded-[5px] px-[5px] py-[2px] bg-[#4CAF50]/80">
            <span className="text-[22px] text-white" style={{ zoom: 0.5 }}>
              保证金: {data.deposit}
            </span>
          </div>
        ) : null}
      </div>
      <div className="flex-none flex flex-col gap-[5px] bg-app-bar-background rounded-b-[10px]">
        <div className="mt-3 mb-2 mx-[10px]">
          <div className="line-clamp-2">
            <span className="flex-none bg-[#ff3460] text-white text-xs py-[2px] px-1 rounded-[4px] mr-1">
              {UserTransactionType[data.type!]}
            </span>
            <div className="font-medium inline">{data.title}</div>
          </div>
          <div className="flex items-center justify-between">
            {data.type === UserTransactionType.出售 ? (
              <span className="text-[#FF005C] font-bold">¥ {data.price}</span>
            ) : null}
            {data.type === UserTransactionType.出售 ? (
              <span
                className="text-[22px] text-hint-color"
                style={{ zoom: 0.5 }}
              >
                库存: {data.stock}
              </span>
            ) : null}
            {data.type === UserTransactionType.求购 ? (
              <span className="text-[#FF005C] font-bold">
                预算: ¥ {data.price}
              </span>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
};
