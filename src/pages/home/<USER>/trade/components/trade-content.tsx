import { UserTransactionResult } from "@/type/user-transaction-result";
import { TradeItem } from "./trade-item";
import { AspectRatio } from "@/ui/aspect-ratio";

interface Props {
  data: UserTransactionResult[];
}

export const TradeContent = (props: Props) => {
  const { data } = props;

  return (
    <div className="py-3 grid grid-cols-2 gap-[10px] md:grid-cols-3 lg:grid-cols-4">
      {data.map((item) => (
        <AspectRatio key={item.id} ratio={0.6}>
          <TradeItem data={item} />
        </AspectRatio>
      ))}
    </div>
  );
};
