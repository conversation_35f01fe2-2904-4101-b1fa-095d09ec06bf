import { useEffect, useState } from "react";
import { LayoutConfigUserTransactionHome } from "@/type/layout-config";
import { TradeContent } from "./components/trade-content";
import { PageWrapper } from "../components/page-wrapper";
import { UserTransactionResult } from "@/type/user-transaction-result";
import { useQuery } from "@tanstack/react-query";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { layoutConfigOptions } from "@/utils/query-options";
import { TradeCreateModal } from "@/components/work/trade-create-modal";
import { CommonIcon } from "@/components/common-icon";

export default function TradePage() {
  const { data: config } = useQuery(
    layoutConfigOptions(LayoutConfigKey.UserTransactionHome)
  );

  const data = config?.data as LayoutConfigUserTransactionHome;

  const [value, setValue] = useState(0);

  useEffect(() => {
    if (data?.tabs.defaultIndex) {
      setValue(data.tabs.defaultIndex);
    }
  }, [data?.tabs.defaultIndex]);

  const tabList =
    data?.tabs.items.map((item, index) => ({
      ...item,
      key: index,
    })) ?? [];

  return (
    <>
      <PageWrapper<UserTransactionResult>
        filter={{
          filter: data?.filter,
          value,
          onChange: setValue,
          tabs: tabList,
        }}
        content={{
          type: "trade",
          data: tabList,
          children: (data) => <TradeContent data={data} />,
        }}
      />
      <TradeCreateModal
        trigger={
          <CommonIcon
            name="add"
            className="fixed bottom-[105px] right-[15px]"
            w={42}
            h={42}
          />
        }
      />
    </>
  );
}
