import { CommonIcon } from "@/components/common-icon";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ArrowIcon } from "@/components/svg-icon";
import { UserInfo } from "@/components/work/user-info";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { usePay } from "@/hooks/use-pay";
import { getUserOrderCreate } from "@/lib/api/user";
import { getRoute } from "@/router/route-map";
import { UserOrderDetailedResult } from "@/type/user-order-detailed-result";
import { Button } from "@/ui/button";
import { Textarea } from "@/ui/textarea";
import { PayBusinessType } from "@/utils/enums";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { layoutConfigOptions } from "@/utils/query-options";
import { useQuery } from "@tanstack/react-query";
import { Image, Toast } from "antd-mobile";
import { useState } from "react";
import { useSearchParams } from "react-router-dom";

export default function CreateOrder() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id") || "";

  const { data: config } = useQuery(
    layoutConfigOptions<{
      showAddress: boolean;
      showMerchantInfo: boolean;
      showTargetInfo: boolean;
      items: Array<{
        key: string;
        label: string;
        copy: boolean;
      }>;
    }>(LayoutConfigKey.UserOrderCreate)
  );

  const { data: orderData } = useQuery({
    queryKey: ["user-order-create", id],
    queryFn: () => getUserOrderCreate(id),
    enabled: !!id,
  });

  const { navigateRoute } = useNavigateRoute();

  const handleTargetUriClick = () => {
    if (orderData?.data?.targetUri) {
      navigateRoute(getRoute(orderData.data.targetUri));
    }
  };

  const [note, setNote] = useState("");

  const handleCopy = (value: string) => {
    navigator.clipboard.writeText(value);
    Toast.show("内容已复制到粘贴板!");
  };

  const { showPay } = usePay({
    type: PayBusinessType.UserOrder,
    params: {
      orderId: id,
      note,
    },
  });

  const handlePay = async () => {
    await showPay();
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="订单详情" />
      <PageMain
        extra={
          <div className="px-[15px] py-[10px] border-t border-divider border-solid flex items-center justify-between">
            <div>
              <span className="text-base text-hint-color">合计：</span>
              <span className="text-[21px] font-bold text-[#ff005c]">
                ¥ {orderData?.data?.shouldPayAmount}
              </span>
              <span className="text-[13px] text-hint-color">
                {" "}
                共{orderData?.data?.number}件
              </span>
            </div>
            <Button
              variant="primary"
              className="w-[121px] h-[44px]"
              onClick={handlePay}
            >
              立即支付
            </Button>
          </div>
        }
      >
        {config?.data?.showAddress ? (
          <>
            <div
              className="flex gap-[15px] items-center p-[15px]"
              onClick={() => navigateRoute("/user-address", { orderId: id })}
            >
              <CommonIcon name="location" w={22} h={22} />
              <div className="flex flex-col flex-1 w-0">
                {!orderData?.data?.fullAddress ? (
                  "点击选择收货地址~"
                ) : (
                  <>
                    <span className="text-[13px] text-hint-color">
                      {orderData?.data?.receiveProvince}
                      {orderData?.data?.receiveCity}
                      {orderData?.data?.receiveRegion}
                    </span>
                    <span className="text-[17px]">
                      {orderData?.data?.receiveAddress}
                    </span>
                    <span className="text-[13px] text-hint-color">
                      {orderData?.data?.receiveName}
                      {orderData?.data?.receivePhone}
                    </span>
                  </>
                )}
              </div>
              <ArrowIcon />
            </div>
            <div className="bg-scaffold-background h-[15px]" />
          </>
        ) : null}
        <div className="p-[15px] flex flex-col">
          {config?.data?.showMerchantInfo && orderData?.data?.merchant ? (
            <div className="pb-[15px]">
              <UserInfo user={orderData.data.merchant} size={47} />
            </div>
          ) : null}
          {config?.data?.showTargetInfo ? (
            <div
              className="flex gap-[15px] items-center"
              onClick={handleTargetUriClick}
            >
              <div className="w-[69px] h-[69px]">
                <Image
                  width="100%"
                  height="100%"
                  src={orderData?.data?.cover}
                  fit="cover"
                  className="rounded-[5px]"
                />
              </div>
              <div className="flex-1 w-0 flex flex-col">
                <span className="line-clamp-1">
                  <span className="bg-[#FF005C] px-[2px] rounded-[5px] mr-[5px]">
                    {orderData?.data?.targetLabel}
                  </span>
                  {orderData?.data?.title}
                </span>
                {orderData?.data?.content ? (
                  <span className="text-[13px] line-clamp-2">
                    {orderData?.data?.content}
                  </span>
                ) : null}
                <span className="text-[#FF005C] text-sm font-bold">
                  ¥ {orderData?.data?.totalPrice}
                </span>
              </div>
            </div>
          ) : null}
        </div>
        <div className="bg-scaffold-background h-[15px]" />
        <div className="p-[15px]">
          <Textarea
            className="px-[15px] py-[10px] resize-none"
            placeholder="您可以在此处输入您要对卖家的留言"
            rows={3}
            value={note}
            onChange={(e) => setNote(e.target.value)}
          />
        </div>
        <div className="bg-scaffold-background h-[15px]" />
        <div className="flex flex-col bg-divider gap-[1px]">
          {config?.data?.items.map((item) => (
            <div
              key={item.key}
              className="bg-app-bar-background flex items-center justify-between h-[75px] px-[15px]"
              onClick={() => {
                if (item.copy) {
                  handleCopy(
                    orderData?.data?.[
                      item.key as keyof UserOrderDetailedResult
                    ] as string
                  );
                }
              }}
            >
              <span>{item.label}</span>
              <div>
                <span className="text-[13px] text-hint-color">
                  {
                    orderData?.data?.[
                      item.key as keyof UserOrderDetailedResult
                    ] as string
                  }
                </span>
                {item.copy ? (
                  <span className="text-[#448AFF]">复制</span>
                ) : null}
              </div>
            </div>
          ))}
        </div>
      </PageMain>
    </PageWrapper>
  );
}
