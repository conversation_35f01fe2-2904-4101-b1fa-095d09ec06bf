import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { WalletHomeLayout } from "@/type/wallet-home-layout";
import { useAsyncValue } from "react-router-dom";
import {
  darkWalletBillBackground,
  lightWalletBillBackground,
  walletBackground,
} from "@/components/image-list";
import { userWalletType } from "@/utils/enums";
import { useUserWallet } from "@/hooks/use-user-wallet";
import { Button } from "@/ui/button";
import { AspectRatio } from "@/ui/aspect-ratio";
import { useTheme } from "@/provider/useTheme";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { PageParams } from "@/type";
import { userWalletBill } from "@/lib/api/wallet";
import dayjs from "dayjs";
import { getUserWallet } from "@/utils/get-user-wallet";
import { Toast } from "antd-mobile";
import { getRoute } from "@/router/route-map";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { SettingIcon } from "@/components/svg-icon";

export default function WalletPage() {
  const { data } = useAsyncValue() as { data: WalletHomeLayout };

  const { exchange = true, recharge = true, withdraw = true, opts = [] } = data;

  const { getBalanceOrEmptyByType } = useUserWallet();
  const theme = useTheme();
  const { navigateRoute } = useNavigateRoute();

  const billBackground =
    theme === "light" ? lightWalletBillBackground : darkWalletBillBackground;

  const queryKey = ["user-wallet-bill"];
  const queryFn = (params: PageParams) => {
    return userWalletBill(params);
  };

  const clipboardText = (text?: string) => {
    navigator.clipboard.writeText(text ?? "未知").then(() => {
      Toast.show({
        content: "复订单号已复制到粘贴板!",
      });
    });
  };

  const navigateToUri = (uri?: string) => {
    if (uri) {
      navigateRoute(getRoute(uri!));
    }
  };

  const handleWithdraw = () => {
    navigateRoute("/withdrawal");
  };

  const handleRecharge = () => {
    navigateRoute("/recharge");
  };

  const handleExchange = () => {
    navigateRoute("/exchange");
  };

  const handleSetting = () => {
    navigateRoute("/charge-setting");
  };

  return (
    <PageWrapper className="wallet-home-bg">
      <NavigationBar
        title="账户"
        canBack
        className="bg-transparent !text-white"
        action={
          <span onClick={handleSetting}>
            <SettingIcon size={20} />
          </span>
        }
      />
      <PageMain>
        <div className="p-[15px] w-full max-w-[820px] m-auto">
          <AspectRatio ratio={684 / 434}>
            <div
              className="p-[15px] w-full h-full bg-cover bg-no-repeat flex flex-col"
              style={{ backgroundImage: `url(${walletBackground})` }}
            >
              <div className="flex gap-[2px] skew-x-[-20deg]">
                <span className="text-[17px] text-[#4722B3] font-bold">
                  我的
                </span>
                <span className="text-[17px] text-white font-bold px-[2px] bg-[#946BFF] rounded">
                  钱包
                </span>
              </div>
              <div className="flex-1" />
              <div className="flex justify-between items-center">
                <div className="flex flex-col">
                  <span className="text-[15px] text-[#300000]">
                    {userWalletType.currency.label}(比例10:1 仅用于消费)
                  </span>
                  <span className="text-[29px] text-[#6712FF]">
                    {getBalanceOrEmptyByType(userWalletType.currency.value) ??
                      "-"}
                  </span>
                </div>
                <div className="flex gap-[10px]">
                  {exchange ? (
                    <Button
                      variant="secondary"
                      className="text-[13px] h-[30px] w-[58px] border-[#6712FF] text-[#6712FF] bg-[#6712FF]/10"
                      onClick={handleExchange}
                    >
                      兑换
                    </Button>
                  ) : null}
                  {recharge ? (
                    <Button
                      variant="primary"
                      className="text-[13px] h-[30px] w-[58px] wallet-recharge-btn"
                      onClick={handleRecharge}
                    >
                      充值
                    </Button>
                  ) : null}
                </div>
              </div>
              <div className="mt-[10px] flex justify-between items-center">
                <div className="flex flex-col">
                  <span className="text-sm text-black">
                    {userWalletType.bonus.label}
                  </span>
                  <span className="text-[24px] text-[#6712FF]">
                    {getBalanceOrEmptyByType(userWalletType.bonus.value) ?? "-"}
                  </span>
                </div>
                <div className="flex flex-col">
                  <span className="text-sm text-black">
                    {userWalletType.bean.label}
                  </span>
                  <span className="text-[24px] text-[#6712FF]">
                    {getBalanceOrEmptyByType(userWalletType.bean.value) ?? "-"}
                  </span>
                </div>
                <div>
                  {withdraw ? (
                    <Button
                      variant="secondary"
                      className="text-[13px] h-[30px] w-[58px] border-[#6712FF] text-[#6712FF] bg-[#6712FF]/10"
                      onClick={handleWithdraw}
                    >
                      提现
                    </Button>
                  ) : null}
                </div>
              </div>
            </div>
          </AspectRatio>
        </div>
        <div className="flex px-[15px]">
          {opts.map((opt) => (
            <div key={opt.name} className="flex-1 p-[5px]">
              {opt.type === "main" ? (
                <Button
                  variant="primary"
                  size="lg"
                  className="w-full wallet-main-opt-btn"
                  onClick={() => navigateToUri(opt.uri)}
                >
                  {opt.name}
                </Button>
              ) : (
                <Button
                  variant="secondary"
                  size="lg"
                  className="w-full border-[#6712FF] text-[#6712FF] bg-[#6712FF]/10"
                  onClick={() => navigateToUri(opt.uri)}
                >
                  {opt.name}
                </Button>
              )}
            </div>
          ))}
        </div>
        <div className="p-[15px]">
          <div
            className="p-[15px] bg-app-bar-background bg-contain bg-no-repeat rounded-[10px]"
            style={{ backgroundImage: `url(${billBackground})` }}
          >
            <div className="flex font-bold text-base">
              <span className="text-foreground">账户</span>
              <span className="text-[#D38E2F]">明细</span>
            </div>
            <div className="pt-[15px]">
              <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
                {(data) => (
                  <div className="flex flex-col gap-[15px]">
                    {data.map((item) => (
                      <div
                        key={item.id}
                        className="flex justify-between"
                        onClick={() => clipboardText(item.orderNumber)}
                      >
                        <div className="flex flex-col">
                          <span className="text-[15px] line-clamp-1">
                            {item.note}
                          </span>
                          <span className="text-[13px] text-hint-color">
                            {dayjs(item.cdate).format("YYYY-MM-DD HH:mm:ss")}
                          </span>
                        </div>
                        <div className="flex flex-col items-end">
                          <span className="text-[13px] text-hint-color">
                            {item.orderNumber}
                          </span>
                          <span
                            className="text-[15px]"
                            style={{
                              color:
                                (item.balance ?? 0) <= 0
                                  ? "#FF005C"
                                  : "#6712FF",
                            }}
                          >
                            {(item.balance ?? 0) <= 0 ? "-" : "+"}
                            {Math.abs(item.balance ?? 0)}
                            {getUserWallet(item.type)?.label}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollLoadData>
            </div>
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
