import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { MountsList, mountsListQueryKey } from "./components/mounts-list";

export default function MountsListPage() {
  const { handleRefresh } = usePageListRefresh(mountsListQueryKey);

  return (
    <PageWrapper>
      <NavigationBar canBack title="我的坐骑" />
      <PageMain
        className="p-[15px] bg-scaffold-background"
        onRefresh={handleRefresh}
      >
        <MountsList />
      </PageMain>
    </PageWrapper>
  );
}
