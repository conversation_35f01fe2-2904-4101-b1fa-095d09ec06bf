import { DefaultLoadImage } from "@/components/default-load-image";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { CloseIcon } from "@/components/svg-icon";
import { getMountsList, wearMounts } from "@/lib/api/user";
import { PageParams } from "@/type";
import { UserMountsResult } from "@/type/user-mounts-result";
import { formatTimeLine } from "@/utils/format-time-line";
import { useMutation } from "@tanstack/react-query";
import { Image, Toast } from "antd-mobile";
import dayjs from "dayjs";
import { useState } from "react";

// eslint-disable-next-line react-refresh/only-export-components
export const mountsListQueryKey = ["user-mounts-list"];

export const MountsList = () => {
  const [selectedIndex, setSelectedIndex] = useState<number | undefined>();

  const queryFn = (params: PageParams) => getMountsList(params);

  const wearMountsMutation = useMutation({
    mutationFn: wearMounts,
  });

  const handleItemClick = (index: number, item?: UserMountsResult) => {
    if (
      item &&
      !(
        item.have ||
        (item.expirationTime && dayjs(item.expirationTime).isBefore(dayjs()))
      )
    ) {
      Toast.show({
        content: "当前还未拥有该装饰，无法选择",
      });
      return;
    }

    if (selectedIndex === index) {
      return;
    }

    setSelectedIndex(index);
    wearMountsMutation.mutate(item?.id);
  };

  return (
    <ScrollLoadData
      queryKey={mountsListQueryKey}
      queryFn={queryFn}
      showTail={false}
    >
      {(data) => {
        const wear = data.some((item) => item.wear);
        const unWearSelected =
          selectedIndex === -1 || (selectedIndex === undefined && !wear);

        return (
          <div className="grid grid-cols-3 gap-[10px]">
            <div
              key={-1}
              className="flex flex-col justify-center items-center border-[2px] border-solid border-transparent rounded-[15px]"
              style={unWearSelected ? { borderColor: "#B573FF" } : {}}
              onClick={() => handleItemClick(-1)}
            >
              <div className="w-[75px] flex justify-center">
                <CloseIcon size={50} className="text-hint-color" />
              </div>
              <span className="text-[13px] py-[5px]">未穿戴</span>
            </div>
            {data.map((item, index) => {
              const have =
                item.have && dayjs(item.expirationTime).isAfter(dayjs());
              const selected =
                selectedIndex === index ||
                (selectedIndex === undefined && item.wear);

              return (
                <div
                  key={item.id}
                  className="flex flex-col justify-center items-center border-[2px] border-solid border-transparent rounded-[15px]"
                  style={selected ? { borderColor: "#B573FF" } : {}}
                  onClick={() => handleItemClick(index, item)}
                >
                  <div className="w-[75px]">
                    <Image
                      src={item.image}
                      width="100%"
                      height="100%"
                      fit="contain"
                      placeholder={<DefaultLoadImage />}
                      fallback={<DefaultLoadImage />}
                      onLoad={() => {}}
                      lazy
                      style={
                        have ? {} : { filter: "saturate(0%) brightness(80%)" }
                      }
                    />
                  </div>
                  <span className="text-[13px] pt-[5px]">{item.name}</span>
                  {item.expirationTime &&
                  dayjs(item.expirationTime).isAfter(dayjs()) ? (
                    <span
                      className="text-[20px] text-[#ff5252]"
                      style={{ zoom: 0.5 }}
                    >
                      {formatTimeLine(dayjs(item.expirationTime).valueOf())}
                      到期
                    </span>
                  ) : null}
                  <span className="pt-[5px]" />
                </div>
              );
            })}
          </div>
        );
      }}
    </ScrollLoadData>
  );
};
