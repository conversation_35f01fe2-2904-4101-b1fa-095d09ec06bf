import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { NavigationSearch } from "@/components/work/navigation-search";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { searchRanking } from "@/lib/api/user";
import { Button } from "@/ui/button";
import { useQuery } from "@tanstack/react-query";
import { Form } from "antd-mobile";
import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";

const colors = [
  ["rgba(255, 153, 18, 0.12)", "rgba(255, 153, 18, 0)"],
  ["rgba(18, 116, 255, 0.12)", "rgba(18, 116, 255, 0)"],
  ["rgba(156, 66, 38, 0.12)", "rgba(156, 66, 38, 0)"],
];

export default function UserSearchHome() {
  const [searchParams] = useSearchParams();
  const defaultIndex = Number(searchParams.get("defaultIndex")) || 0;

  const [form] = Form.useForm();

  const value = Form.useWatch("name", form);

  const [historyList, setHistoryList] = useState<string[]>([]);

  const { data: userInfo } = useFullUserInfo();
  const { data: hotList } = useQuery({
    queryKey: ["search-ranking"],
    queryFn: () => searchRanking(),
  });
  const { navigateRoute } = useNavigateRoute();

  useEffect(() => {
    setHistoryList(
      JSON.parse(
        localStorage.getItem(`${userInfo?.id}:cache_search_history`) || "[]"
      )
    );
  }, [userInfo?.id]);

  const handleSearch = () => {
    const newHistoryList = [value, ...historyList];

    localStorage.setItem(
      `${userInfo?.id}:cache_search_history`,
      JSON.stringify(newHistoryList)
    );

    handleNavigateToDetailed(value);
  };

  const handleNavigateToDetailed = (keyword: string) => {
    navigateRoute(
      "/user-search-detailed",
      {
        defaultIndex,
        keyword,
      },
      { replace: true }
    );
  };

  return (
    <PageWrapper className="bg-scaffold-background">
      <NavigationBar
        canBack
        content={
          <div className="flex-1 flex items-center gap-2">
            <NavigationSearch
              form={form}
              placeholder="请输入要搜索的内容"
              name="name"
              onFinish={handleSearch}
            />
            <Button
              variant="primary"
              className="text-[13px] py-0 px-[10px] h-[32px]"
              disabled={!value?.trim()}
              onClick={handleSearch}
            >
              搜索
            </Button>
          </div>
        }
      />
      <PageMain>
        <div className="p-[15px] flex flex-col gap-[15px]">
          {historyList.length ? (
            <div className="flex flex-col gap-[10px]">
              <span className="text-sm">历史记录</span>
              <div className="flex flex-wrap gap-[10px]">
                {historyList.map((item, i) => (
                  <span
                    key={i}
                    className="px-[10px] py-[5px] bg-[#E7E7E7] dark:bg-[#283140] rounded-[15px] text-[15px] text-[#999] dark:text-[#7F848D]"
                    onClick={() => handleNavigateToDetailed(item)}
                  >
                    {item}
                  </span>
                ))}
              </div>
            </div>
          ) : null}
          {hotList?.data?.length ? (
            <div className="flex flex-col gap-[10px]">
              <span className="text-sm">热门搜索</span>
              <div className="flex flex-col gap-[10px]">
                {hotList.data.map((item, i) => {
                  let color = colors[i];
                  if (!color) {
                    color = [
                      "hsla(var(--foreground)/0.12)",
                      "hsla(var(--foreground)/0)",
                    ];
                  }

                  return (
                    <div
                      key={i}
                      className="h-[35px] px-[13px] rounded-[5px] flex items-center"
                      style={{
                        background: `linear-gradient(to right, ${color[0]} 0%, ${color[1]} 100%)`,
                      }}
                      onClick={() => handleNavigateToDetailed(item.content!)}
                    >
                      <div className="flex flex-1">
                        <span className="w-[25px] text-xs font-bold">
                          {i + 1}
                        </span>
                        <span className="flex-1 text-sm line-clamp-1">
                          {item.content}
                        </span>
                      </div>
                      <span className="text-xs">{item.tips}</span>
                    </div>
                  );
                })}
              </div>
            </div>
          ) : null}
        </div>
      </PageMain>
    </PageWrapper>
  );
}
