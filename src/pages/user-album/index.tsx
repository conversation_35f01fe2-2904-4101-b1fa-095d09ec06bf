import { BaseMediaUpload } from "@/components/base-media-upload";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { CloseIcon } from "@/components/svg-icon";
import { AlbumResourceBox } from "@/components/work/album-resource-box";
import { confirmDialog } from "@/components/work/confirm-dialog";
import { UploadFile } from "@/components/work/upload-file";
import { useConfigList } from "@/hooks/use-config-list";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { useUploadQueueManager } from "@/hooks/use-upload-queue-manager";
import { userWalletQueryKey } from "@/hooks/use-user-wallet";
import {
  buyUserAlbum,
  getUserAlbum,
  removeUserAlbum,
  userAlbumAdd,
  userAlbum<PERSON>dd<PERSON><PERSON><PERSON>,
  userAlbumSetPrice,
} from "@/lib/api/user";
import { queryClient } from "@/provider/query-client";
import { Button } from "@/ui/button";
import { getRandomParams } from "@/utils/common";
import { userWalletType } from "@/utils/enums";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import { useRef, useState } from "react";
import { useSearchParams } from "react-router-dom";
import {
  PriceSettingModal,
  PriceSettingModalRef,
} from "./components/price-setting-modal";

export default function UserAlbum() {
  const [searchParams] = useSearchParams();
  const userId = searchParams.get("id");

  const [publicFiles, setPublicFiles] = useState<File[]>([]);
  const [paidFiles, setPaidFiles] = useState<File[]>([]);

  const priceSettingModalRef = useRef<PriceSettingModalRef>(null);

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["userAlbum", userId],
    queryFn: () => getUserAlbum({ userId: userId || "" }),
  });

  const { data: userInfo } = useFullUserInfo();

  const { getSystemConfig } = useConfigList();

  const isSelf = userInfo?.id === userId;

  const max = getSystemConfig("USER_ALBUM_MAX_PRICE");
  const min = getSystemConfig("USER_ALBUM_MIN_PRICE");

  const { navigateRoute, navigateBack } = useNavigateRoute();

  const handlePublicPreview = (index: number) => {
    const previewList = data?.data?.publicResource?.map((item) => {
      return {
        thumbnailImage: item.resource?.images?.Thumbnail?.url!,
        url:
          item.resource?.duration == null
            ? item.resource?.images?.BigPicture?.url
            : item.resource?.url,
        video: !!item.resource?.duration,
        preview: false,
      };
    });

    navigateRoute(
      "/resource-preview",
      {},
      {
        state: {
          data: previewList,
          defaultIndex: index,
        },
      }
    );
  };

  const handlePaidPreview = (index: number) => {
    const previewList = data?.data?.paidResource?.map((item) => {
      return {
        thumbnailImage: item.resource?.images?.Thumbnail?.url!,
        url:
          item.resource?.duration == null
            ? item.resource?.images?.BigPicture?.url
            : item.resource?.url,
        video: !!item.resource?.duration,
        preview: !data?.data?.buy,
      };
    });

    navigateRoute(
      "/resource-preview",
      {},
      {
        state: {
          data: previewList,
          defaultIndex: index,
        },
      }
    );
  };

  const { mutateAsync: buy } = useMutation({
    mutationFn: buyUserAlbum,
  });

  const handleBuy = async () => {
    const result = await confirmDialog.show(
      "（购买后可无限次查看）",
      `购买该内容需要支付${data?.data?.price ?? 0}`
    );

    if (result) {
      Toast.show({
        icon: "loading",
        content: "请稍后...",
        duration: 0,
      });

      const { ok } = await buy({ userId: userId || "" });

      if (ok) {
        Toast.show({
          content: "购买成功",
        });

        queryClient.refetchQueries({
          queryKey: userWalletQueryKey,
        });

        refetch();
      }
    }
  };

  const { mutateAsync: remove } = useMutation({
    mutationFn: removeUserAlbum,
  });

  const handleDelete = async (id: string) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await remove({ id });

    if (ok) {
      refetch();
    }
  };

  const { mutateAsync } = useMutation({
    mutationFn: userAlbumAddCheck,
  });

  const { addToQueue } = useUploadQueueManager();

  const handleSubmit = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await mutateAsync({
      publicResources: publicFiles.length
        ? publicFiles.map(() => getRandomParams())
        : undefined,
      paidResources: paidFiles.length
        ? paidFiles.map(() => getRandomParams())
        : undefined,
    });

    if (ok) {
      addToQueue(
        {
          title: "上传颜照库",
          url: userAlbumAdd,
          params: {
            publicResources: publicFiles.length ? publicFiles : undefined,
            paidResources: paidFiles.length ? paidFiles : undefined,
          },
        },
        () => {
          navigateBack();
        }
      );
    }
  };

  const { mutateAsync: setPrice } = useMutation({
    mutationFn: userAlbumSetPrice,
  });

  const handlePriceChange = async (value: number) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await setPrice({ price: value });

    if (ok) {
      refetch();
    }
  };

  return (
    <PageWrapper className="relative">
      <NavigationBar canBack title="颜照库" />
      <PageMain isLoading={isLoading} isEmpty={!data?.data}>
        <div className="p-[15px] flex flex-col gap-[15px]">
          {isSelf ? (
            <div>
              当前价格:{data?.data?.price ?? 0}
              {userWalletType.currency.label}，总购买次数:
              {data?.data?.buyNumber}
              ，总收益：{data?.data?.income ?? 0}
              {userWalletType.bean.label}
            </div>
          ) : null}
          <div className="flex flex-col gap-1">
            <div>公开内容</div>
            <div className="grid grid-cols-3 gap-[15px]">
              {data?.data?.publicResource?.map((item, index) => (
                <div key={item.id} className="relative">
                  <AlbumResourceBox
                    data={item}
                    buy
                    size={100}
                    onPreview={() => handlePublicPreview(index)}
                  />
                  {isSelf ? (
                    <div
                      className="absolute top-[-5px] right-[-5px] z-10"
                      onClick={() => handleDelete(item.id!)}
                    >
                      <span>
                        <CloseIcon className="text-white bg-[#FF005C] rounded-full" />
                      </span>
                    </div>
                  ) : null}
                </div>
              ))}
              {publicFiles.map((file, index) => (
                <div key={index} className="w-[100px] h-[100px]">
                  <UploadFile
                    file={file}
                    onDelete={() => {
                      setPublicFiles(publicFiles.filter((_, i) => i !== index));
                    }}
                  />
                </div>
              ))}
              {isSelf ? (
                <div className="w-[100px] h-[100px]">
                  <BaseMediaUpload
                    className="w-full h-full"
                    maxCount={9}
                    onSelected={(files: File[]) => {
                      setPublicFiles([...publicFiles, ...files]);
                    }}
                  />
                </div>
              ) : null}
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <div>收费内容</div>
            <div className="grid grid-cols-3 gap-[15px]">
              {data?.data?.paidResource?.map((item, index) => (
                <div key={item.id} className="relative">
                  <AlbumResourceBox
                    data={item}
                    buy={data?.data?.buy ?? false}
                    size={100}
                    onPreview={() => handlePaidPreview(index)}
                  />
                  {isSelf ? (
                    <div
                      className="absolute top-[-5px] right-[-5px] z-10"
                      onClick={() => handleDelete(item.id!)}
                    >
                      <span>
                        <CloseIcon className="text-white bg-[#FF005C] rounded-full" />
                      </span>
                    </div>
                  ) : null}
                </div>
              ))}
              {paidFiles.map((file, index) => (
                <div key={index} className="w-[100px] h-[100px]">
                  <UploadFile
                    file={file}
                    onDelete={() => {
                      setPaidFiles(paidFiles.filter((_, i) => i !== index));
                    }}
                  />
                </div>
              ))}
              {isSelf ? (
                <div className="w-[100px] h-[100px]">
                  <BaseMediaUpload
                    className="w-full h-full"
                    maxCount={9}
                    onSelected={(files: File[]) => {
                      setPaidFiles([...paidFiles, ...files]);
                    }}
                  />
                </div>
              ) : null}
            </div>
          </div>
        </div>
      </PageMain>
      {isSelf ? (
        <div className="px-[15px] absolute bottom-[90px] left-0 w-full">
          <Button
            variant="primary"
            className="w-full h-[30px] text-xs"
            onClick={() => priceSettingModalRef.current?.open()}
          >
            更改价格(当前{data?.data?.price || 0}
            {userWalletType.currency.label})
          </Button>
        </div>
      ) : null}
      {isSelf && (publicFiles.length || paidFiles.length) ? (
        <div className="px-[15px] absolute bottom-[15px] left-0 w-full">
          <Button variant="primary" className="w-full" onClick={handleSubmit}>
            保存更改({publicFiles.length + paidFiles.length}张资源)
          </Button>
        </div>
      ) : null}
      {!isSelf && !data?.data?.buy ? (
        <div className="px-[15px] absolute bottom-[50px] left-0 w-full">
          <Button variant="primary" className="w-full" onClick={handleBuy}>
            立即购买({data?.data?.price ?? 0}
            {userWalletType.currency.label})
          </Button>
        </div>
      ) : null}
      <PriceSettingModal
        ref={priceSettingModalRef}
        min={Number(min)}
        max={Number(max)}
        value={Number(data?.data?.price ?? 0)}
        onChange={handlePriceChange}
      />
    </PageWrapper>
  );
}
