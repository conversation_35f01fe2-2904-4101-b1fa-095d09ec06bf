import { ArrowIcon } from "@/components/svg-icon";
import { videoClassifyList } from "@/lib/api/video";
import { VideoClassifyResult } from "@/type/video-classify-result";
import { useQuery } from "@tanstack/react-query";
import { Popup } from "antd-mobile";
import { useState } from "react";

const convertClassifyList = (
  list: VideoClassifyResult[]
): {
  value: string;
  label: string;
  children?: { value: string; label: string }[];
}[] => {
  return list.map((item) => ({
    value: item.id!,
    label: item.name!,
    children: item.children ? convertClassifyList(item.children) : undefined,
  }));
};

interface Props {
  value?: {
    value: string;
    label: string;
  };
  onChange?: (value: { value: string; label: string }) => void;
}

export const CascadeSelect = ({ value, onChange }: Props) => {
  const [open, setOpen] = useState(false);
  const [currentOptions, setCurrentOptions] = useState<
    {
      value: string;
      label: string;
      children?: { value: string; label: string }[];
    }[]
  >([]);

  const { data: classifyList } = useQuery({
    queryKey: ["videoClassifyList"],
    queryFn: videoClassifyList,
    staleTime: Infinity,
  });

  const options = convertClassifyList(classifyList?.data ?? []);

  const onOpen = () => {
    setOpen(true);
    setCurrentOptions(options);
  };

  const onClose = () => {
    setOpen(false);
  };

  const handleChange = (item: { value: string; label: string }) => {
    onChange?.(item);
    onClose();
  };

  return (
    <>
      <div onClick={onOpen}>{value?.label ?? "请选择视频分类"}</div>
      <Popup
        destroyOnClose
        visible={open}
        bodyClassName="rounded-t-[10px]"
        onMaskClick={onClose}
        onClose={onClose}
      >
        <div className="h-[300px] flex flex-col gap-1 bg-app-bar-background p-[15px]">
          {currentOptions.map((item) => (
            <div key={item.value} className="flex items-center gap-[10px]">
              <span
                className="flex-1 text-base"
                onClick={() => handleChange(item)}
              >
                {item.label}
              </span>
              {item.children?.length ? (
                <span
                  className="w-4 h-4 flex justify-center items-center"
                  onClick={() => setCurrentOptions(item.children ?? [])}
                >
                  <ArrowIcon />
                </span>
              ) : null}
            </div>
          ))}
        </div>
      </Popup>
    </>
  );
};
