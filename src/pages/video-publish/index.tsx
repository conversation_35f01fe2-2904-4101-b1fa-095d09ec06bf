import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { TagSelect } from "@/components/work/tag-select";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/ui/form";
import { Textarea } from "@/ui/textarea";
import { Switch, Toast } from "antd-mobile";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { SingleMediaUpload } from "@/components/work/single-media-upload";
import { Button } from "@/ui/button";
import { useConfigList } from "@/hooks/use-config-list";
import { tipsDialog } from "@/components/work/tips-dialog";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  videoPublishCheck,
  videoPublishUrl,
  videoTagList,
  videoUploaderHas,
} from "@/lib/api/video";
import { useUploadQueueManager } from "@/hooks/use-upload-queue-manager";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { getRandomParams, getVideoDuration } from "@/utils/common";
import { Input } from "@/ui/input";
import { CascadeSelect } from "./components/cascade-select";

export default function VideoPublish() {
  const form = useForm();

  const customCover = form.watch("customCover");
  const resource = form.watch("resource");

  const { getSystemConfig } = useConfigList();

  const tips = getSystemConfig("VIDEO_PUBLISH_TIPS");

  useEffect(() => {
    if (resource) {
      getVideoDuration(resource).then((duration) => {
        form.setValue("duration", duration);
      });
    }
  }, [form, resource]);

  useEffect(() => {
    if (tips) {
      tipsDialog.success({
        title: "提示信息",
        content: <div className="whitespace-pre-wrap text-left">{tips}</div>,
        confirmText: "确定",
      });
    }
  }, [tips]);

  const { data: uploaderHas } = useQuery({
    queryKey: ["video-uploader-has"],
    queryFn: videoUploaderHas,
  });

  const { data: tagList } = useQuery({
    queryKey: ["video-tag-list"],
    queryFn: videoTagList,
  });

  const { mutateAsync } = useMutation({
    mutationFn: videoPublishCheck,
  });

  const { addToQueue } = useUploadQueueManager();
  const { navigateBack, navigateRoute } = useNavigateRoute();

  const handleApply = () => {
    navigateRoute("/video-uploader-apply");
  };

  const handleSubmit = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const data = form.getValues();

    const { cover, resource, customCover, classify, ...rest } = data;

    const { ok } = await mutateAsync({
      cover: customCover && cover ? getRandomParams() : undefined,
      resource: resource ? getRandomParams() : undefined,
      videoClassifyId: classify?.value,
      ...rest,
    });

    if (ok) {
      addToQueue(
        {
          title: "视频发布",
          url: videoPublishUrl,
          params: {
            ...rest,
            cover: customCover ? cover : undefined,
            resource,
            videoClassifyId: classify?.value,
          },
        },
        () => {
          navigateBack();
        }
      );
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="视频发布" />
      <PageMain>
        {!uploaderHas?.data ? (
          <div className="px-[15px] py-[10px] bg-[#9D64FF]/20 flex items-center justify-between text-[#9D64FF] text-sm">
            <span>您还不是UP主哦~申请为UP主可上传视频~</span>
            <Button
              variant="primary"
              className="text-sm px-2"
              onClick={handleApply}
            >
              立即申请
            </Button>
          </div>
        ) : null}
        <div className="p-[15px]">
          <Form {...form}>
            <div className="flex flex-col gap-[10px]">
              <FormField
                control={form.control}
                name="original"
                render={({ field }) => {
                  return (
                    <FormItem className="flex items-center justify-between">
                      <FormLabel>是否原创</FormLabel>
                      <FormControl>
                        <Switch
                          className="!m-0"
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="customCover"
                render={({ field }) => {
                  return (
                    <FormItem className="flex items-center justify-between">
                      <FormLabel>自定义封面</FormLabel>
                      <FormControl>
                        <Switch
                          className="!m-0"
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="prohibitComment"
                render={({ field }) => {
                  return (
                    <FormItem className="flex items-center justify-between">
                      <FormLabel>是否禁止评论</FormLabel>
                      <FormControl>
                        <Switch
                          className="!m-0"
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              {customCover ? (
                <FormField
                  control={form.control}
                  name="cover"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel>封面</FormLabel>
                        <FormControl>
                          <SingleMediaUpload
                            accept="image/*"
                            {...field}
                            className="w-full h-[150px]"
                            placeholder="点击选择封面"
                          />
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
              ) : null}
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>视频标题</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="请输入标题" />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>视频描述</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          rows={3}
                          className="rounded-[10px] px-[15px] py-[10px] resize-none"
                          placeholder="请输入视频描述"
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="classify"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>视频分类</FormLabel>
                      <FormControl>
                        <CascadeSelect {...field} />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="resource"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>视频资源</FormLabel>
                      <FormControl>
                        <SingleMediaUpload
                          accept="video/*"
                          {...field}
                          className="w-full h-[139px]"
                          placeholder="请选择视频"
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>视频标签</FormLabel>
                      <FormControl>
                        <TagSelect
                          {...field}
                          append={false}
                          options={
                            tagList?.data?.map((item) => ({
                              id: item.id!,
                              label: item.name!,
                            })) ?? []
                          }
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
            </div>
          </Form>
          <div className="my-[20px]">
            <Button variant="primary" className="w-full" onClick={handleSubmit}>
              立即上传
            </Button>
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
