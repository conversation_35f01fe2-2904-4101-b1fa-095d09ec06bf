import {PageWrapper} from "@/components/page-wrapper";
import {NavigationBar} from "@/components/navigation-bar.tsx";
import {PageMain} from "@/components/page-main.tsx";
import {Input} from "@/ui/input.tsx";
import {Button} from "@/ui/button";
import classNames from "classnames";
import {userSearchOne} from "@/lib/api/user";
import {PayBusinessType, userWalletType} from "@/utils/enums";
import {Avatar, AvatarImage} from "@/ui/avatar";
import {AspectRatio} from "@/ui/aspect-ratio";
import {useAsyncValue, useSearchParams} from "react-router-dom";
import {RechargeCostResult} from "@/type/recharge-cost-result.ts";
import {useEffect, useState} from "react";
import {
    rechargeCostVipBackground,
    rechargeCostVipSelectedBackground,
} from "@/components/image-list";
import {Image} from "antd-mobile";
import {useConfigList} from "@/hooks/use-config-list.ts";
import {tipsDialog} from "@/components/work/tips-dialog.tsx";
import {User} from "@/type/user.ts";
import {useQuery} from "@tanstack/react-query";
import {usePay} from "@/hooks/use-pay.ts";

/**
 * 网页支付页面

 */
export default function WebPayPage() {

    const [searchParams] = useSearchParams();
    const uid = searchParams.get("uid") || undefined;

    const {data} = useAsyncValue() as { data: RechargeCostResult[] };

    const [selectedIndex, setSelectedIndex] = useState<number>(0);

    const {getSystemConfig} = useConfigList();

    /**
     * 关键字信息
     */
    const [keyword, setKeyword] = useState<string | undefined | null>(uid);

    /**
     * 用户信息
     */
    const [user, setUser] = useState<User | null>(null);


    /**
     * 如果有用户信息则自动加载
     */
    useEffect(() => {
        if(keyword) onConfirmAccountClick();
    },[]);

    const {showPay} = usePay({
        type: PayBusinessType.Recharge,
        userId: user?.id,
        params: {
            costId: data[selectedIndex].id,
        },
    });

    /**
     * 查询
     */
    const searchUserRequest = useQuery({
        queryKey: ["searchUserOne", keyword],
        queryFn: () => userSearchOne({keyword: keyword!}),
        enabled: false,
    });

    /**
     * 确认账号按钮点击事件
     */
    const onConfirmAccountClick = async () => {
        if (keyword == null || keyword.trim() == '') {
            tipsDialog.error({title: "发生错误", content: "请输入账号信息", confirmText: "知道了"});
            return;
        }

        // 调用接口获取用户信息
        var res = await searchUserRequest.refetch()
        if (res.data?.data) {
            setUser(res.data!.data!);
        }
    };

    /**
     * 切换账号按钮点击事件
     */
    const onChangeAccountClick = () => setUser(null);

    /**
     * 充值按钮点击事件
     */
    const onRechargeClick = () => {
        if (user == null) {
            tipsDialog.error({title: "发生错误", content: "未找到用户信息", confirmText: "知道了"});
            return;
        }

        // 显示支付弹窗
        showPay();
    }

    return (
        <PageWrapper>
            <NavigationBar title="在线充值"/>
            <PageMain className="bg-[#F6F6F6] dark:bg-[#000510]" isEmpty={false}>
                <div style={{padding: 10}}>
                    {/*账号输入框*/}
                    {
                        user != null ? null : (
                            <div className="bg-app-bar-background" style={{padding: "10px 15px", borderRadius: 5, display: "flex"}}>
                                <div style={{flex: 1}}><Input value={keyword ?? ""} onChange={(e) => setKeyword(e.target.value)} placeholder="请输入充值的账号/手机号" className={"border border-input"} style={{borderRadius: 4, borderBottomRightRadius: 0, borderTopRightRadius: 0}}/></div>
                                <Button color="primary" style={{borderBottomLeftRadius: 0, borderTopLeftRadius: 0}} onClick={onConfirmAccountClick}>确认账号</Button>
                            </div>
                        )
                    }

                    {/*用户信息*/}
                    {
                        user == null ? null : (
                            <div className="bg-app-bar-background" style={{padding: "10px 15px", borderRadius: 5, display: "flex"}}>
                                <Avatar>
                                    <AvatarImage src={user?.avatarUrl} alt="@shadcn"/>
                                </Avatar>
                                <div style={{flex: 1, padding: "0 20px"}}>
                                    <div>{user?.nickname}</div>
                                    <div>ID: {user?.userid}</div>
                                </div>
                                <div style={{display: "flex", justifyContent: "center", alignItems: "center"}} onClick={onChangeAccountClick}>切换账号</div>
                            </div>
                        )
                    }

                    {/*充值选项列表*/}
                    <div style={{marginTop: 10, borderRadius: 5}}>
                        <span className="text-[13px] text-hint-color">
                            请选择充值套餐
                        </span>
                        <div className="grid grid-cols-2 gap-[15px]" style={{marginTop: 10, borderRadius: 5}}>
                            {data.map((item, index) => (
                                <AspectRatio key={item.id} ratio={1.5}>
                                    <div
                                        className={classNames(
                                            "w-full h-full relative flex justify-center items-center rounded-[5px] bg-app-bar-background border border-solid",
                                            selectedIndex === index
                                                ? "border-[#9D64FF] bg-[#9D64FF]/10"
                                                : "border-app-bar-background bg-app-bar-background"
                                        )}
                                        onClick={() => setSelectedIndex(index)}
                                    >
                                        <div className="flex flex-col items-center justify-center">
                                            <span
                                                className={classNames(
                                                    "text-[17px]",
                                                    selectedIndex === index ? "text-[#9D64FF]" : ""
                                                )}
                                            >
                                              ￥{item.presentPrice}
                                            </span>
                                            <span className="text-sm text-hint-color">
                                              {item.number}
                                                {
                                                    Object.values(userWalletType).find((type) => type.value === item.walletType)?.label
                                                }
                                            </span>
                                            {
                                                item.giveNumber && item.giveNumber > 0 ?
                                                    (<span className="text-sm text-hint-color">普通用户送{item.giveNumber}{Object.values(userWalletType).find((type) => type.value === item.walletType)?.label}</span>)
                                                    : null
                                            }
                                        </div>
                                        {item.vipGiveNumber && item.vipGiveNumber > 0 ? (
                                            <div className="absolute top-0 right-0 h-[23px] z-10">
                                                <Image
                                                    src={selectedIndex === index ? rechargeCostVipSelectedBackground : rechargeCostVipBackground}
                                                    width="100%"
                                                    height="100%"
                                                    placeholder={null}
                                                    fallback={null}
                                                    className="absolute z-[-1] top-0 right-0"
                                                />
                                                <span className="text-white text-[13px] pl-[10px] pr-[5px]">
                                                    VIP再送{item.vipGiveNumber}
                                                    {
                                                        Object.values(userWalletType).find(
                                                            (type) => type.value === item.walletType
                                                        )?.label
                                                    }
                                                </span>
                                            </div>
                                        ) : null}
                                    </div>
                                </AspectRatio>
                            ))}
                        </div>
                        <div className="py-[25px]">
                            <Button variant="primary" size="lg" className="w-full" onClick={onRechargeClick}>充值</Button>
                        </div>
                        <div className="flex flex-col gap-[10px]">
                            <span className="text-sm text-hint-color">说明</span>
                            <span className="text-xs text-hint-color whitespace-pre-wrap">{getSystemConfig("WEB_RECHARGE_TIPS")}</span>
                        </div>
                    </div>
                </div>

                <div style={{padding: 10, background: "cornflowerblue", position: "fixed", bottom: 40, right: 10}}>
                    <a href="https://kf.yayoukj.com/chat/index/newChat/m/65c9d1104f0cb" style={{color: "white"}}>
                        <div className="contact-us">
                            <i className="el-icon-message"></i><br/>
                            在<br/>线<br/>客<br/>服
                        </div>
                    </a>
                </div>
            </PageMain>
        </PageWrapper>
    );
}