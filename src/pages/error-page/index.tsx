import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { But<PERSON> } from "@/ui/button";
import { useRouteError } from "react-router-dom";

export default function ErrorPage() {
  const error = useRouteError();
  console.error(error);

  const { navigateRoute } = useNavigateRoute();

  return (
    <div className="flex flex-col gap-4 items-center justify-center h-full">
      <span className="text-xl">当前页面不存在</span>
      <Button variant="primary" onClick={() => navigateRoute("/")}>
        返回首页
      </Button>
    </div>
  );
}
