import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { shortVideoDetail } from "@/lib/api/video";
import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "react-router-dom";
import { ShortVideoPlayer } from "../home/<USER>/components/short-video-player";

export default function ShortVideoDetail() {
  const [searchParams] = useSearchParams();

  const id = searchParams.get("id");

  const { data: shortVideo, isLoading } = useQuery({
    queryKey: ["shortVideo", id],
    queryFn: () => shortVideoDetail(id!),
  });

  return (
    <PageWrapper className="relative">
      <NavigationBar
        canBack
        className="bg-transparent absolute top-0 left-0 w-full z-20"
      />
      <PageMain isLoading={isLoading} needScroll={false}>
        <ShortVideoPlayer
          isActive
          index={0}
          data={shortVideo?.data!}
          reload={() => {}}
          onReplaceData={() => {}}
        />
      </PageMain>
    </PageWrapper>
  );
}
