import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { rePunchInRecord } from "@/lib/api/user";
import { PageParams } from "@/type";
import dayjs from "dayjs";

export default function RePunchInRecordPage() {
  const queryKey = ["re-punch-in-record"];
  const queryFn = (params: PageParams) => rePunchInRecord(params);

  const { handleRefresh } = usePageListRefresh(queryKey);

  return (
    <PageWrapper className="bg-scaffold-background">
      <NavigationBar canBack title="补签记录" />
      <PageMain onRefresh={handleRefresh}>
        <ScrollLoadData queryFn={queryFn} queryKey={queryKey}>
          {(data) => {
            return (
              <div className="flex flex-col gap-[15px] pt-[15px]">
                {data.map((item, index) => {
                  return (
                    <div
                      key={index}
                      className="bg-app-bar-background px-[15px] py-[10px]"
                    >
                      <div className="text-[13px]">
                        补签 - {dayjs(item.optDate).format("YYYY年MM月DD")}
                      </div>
                      <span className="text-[12px]">{item.cdate}</span>
                    </div>
                  );
                })}
              </div>
            );
          }}
        </ScrollLoadData>
      </PageMain>
    </PageWrapper>
  );
}
