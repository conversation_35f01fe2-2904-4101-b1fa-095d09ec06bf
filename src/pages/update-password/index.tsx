import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { useCountDownTimer } from "@/hooks/use-count-down-timer";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { smsSendCode } from "@/lib/api/message";
import { updatePassword } from "@/lib/api/user";
import { Button } from "@/ui/button";
import { Form, FormControl, FormField, FormItem } from "@/ui/form";
import { Input } from "@/ui/input";
import { extractLeadingNumbers } from "@/utils";
import { phoneRegex } from "@/utils/constant";
import { useMutation } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import { useForm } from "react-hook-form";

export default function UpdatePassword() {
  const form = useForm();
  const { isRunning, seconds, handleStart } = useCountDownTimer(60);
  const { navigateBack } = useNavigateRoute();

  const sendCodeMutation = useMutation({ mutationFn: smsSendCode });
  const updatePasswordMutation = useMutation({ mutationFn: updatePassword });
  const checkPhone = async () => {
    const { phone } = form.getValues();

    if (!phone) {
      Toast.show({ content: "请输入手机号" });
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(phone)) {
      Toast.show({ content: "请输入正确的手机号" });
      return;
    }
    // 发送验证码
    Toast.show({
      icon: "loading",
      content: "加载中…",
      duration: 0,
    });

    const { ok, msg } = await sendCodeMutation.mutateAsync(phone);

    if (ok) {
      handleStart();
    } else {
      Toast.show({ content: msg });
    }
  };

  const handleSumbit = async () => {
    const { phone, code, password } = form.getValues();

    if (!phone) {
      Toast.show({ content: "请输入手机号" });
      return;
    }

    if (!phoneRegex.test(phone)) {
      Toast.show({ content: "请输入正确的手机号" });
      return;
    }

    if (!code) {
      Toast.show({ content: "请输入验证码" });
      return;
    }

    if (!password) {
      Toast.show({ content: "请输入密码" });
      return;
    }

    Toast.show({
      icon: "loading",
      content: "操作中...",
      duration: 0,
    });

    const { ok } = await updatePasswordMutation.mutateAsync({
      phone,
      code,
      password,
    });

    if (ok) {
      Toast.show({ content: "密码修改成功，请重新登录!" });
      navigateBack();
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="修改密码" />
      <PageMain>
        <Form {...form}>
          <div className="flex flex-col gap-5 p-[15px]">
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className="flex items-center relative z-0">
                      <span className="absolute ml-[5px] z-[-1]">+86</span>
                      <Input
                        placeholder="请输入手机号"
                        className="pl-[45px] pr-[70px]"
                        maxLength={11}
                        {...field}
                        autoComplete="off"
                        value={extractLeadingNumbers(field.value)}
                      />
                      {!isRunning ? (
                        <Button
                          variant="primary"
                          size="sm"
                          className="absolute right-0"
                          onClick={checkPhone}
                        >
                          发送
                        </Button>
                      ) : (
                        <Button
                          size="sm"
                          variant="secondary"
                          className="absolute right-0"
                        >
                          {seconds}
                        </Button>
                      )}
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder="请输入验证码"
                        maxLength={6}
                        {...field}
                        autoComplete="off"
                        value={extractLeadingNumbers(field.value)}
                      />
                    </FormControl>
                  </FormItem>
                );
              }}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder="请输入登录密码"
                        {...field}
                        autoComplete="off"
                      />
                    </FormControl>
                  </FormItem>
                );
              }}
            />
          </div>
        </Form>
        <div className="p-[15px] pt-5">
          <Button
            variant="primary"
            size="lg"
            className="w-full"
            onClick={handleSumbit}
          >
            修改密码
          </Button>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
