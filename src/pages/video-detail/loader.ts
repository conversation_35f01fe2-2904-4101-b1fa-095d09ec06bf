import { videoDetail } from "@/lib/api/video";
import { queryClient } from "@/provider/query-client";
import { parseUrl } from "@/utils/parse-url";
import { defer, LoaderFunction, LoaderFunctionArgs } from "react-router-dom";

export const loader: LoaderFunction = ({ request }: LoaderFunctionArgs) => {
  const { searchParams } = parseUrl(request.url);
  const id = searchParams.get("id");

  return defer({
    data: queryClient
      .fetchQuery({
        queryKey: ["video-detail", id],
        queryFn: () => videoDetail(id!),
      })
      .catch((err) => err),
  });
};
