import { Fragment, ReactNode, useEffect, useRef, useState } from "react";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { CommentInput, FinishType } from "@/components/work/comment-input";
import { GiftResult } from "@/type/gift-result";
import Player from "video.js/dist/types/player";
import { useAsyncValue, useRevalidator } from "react-router-dom";
import videojs from "video.js";
import { VideoResult } from "@/type/video-result";
import { formatDuration } from "@/utils/format-duration";
import {
  ReportTargetType,
  ShareTargetType,
  userWalletType,
  VipPermission,
} from "@/utils/enums";
import { confirmDialog } from "@/components/work/confirm-dialog";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  buyVideo,
  collectionVideo,
  downloadVideo,
  likeVideo,
  videoComment,
  videoCommentPublishCheck,
  videoCommentPublishUrl,
  videoGiftGive,
  videoRelated,
  vipPlayer,
} from "@/lib/api/video";
import { Divider, Toast } from "antd-mobile";
import { useUserVip } from "@/hooks/user-vip";
import { Button } from "@/ui/button";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { UserInfo } from "@/components/work/user-info";
import { formatTimeLine } from "@/utils/format-time-line";
import dayjs from "dayjs";
import { CommonIcon } from "@/components/common-icon";
import { ShareModal } from "@/components/work/share-modal";
import { useModal } from "@/hooks/use-modal";
import { useDownloadQueueManager } from "@/hooks/use-download-queue-manager";
import { cancelCollection } from "@/lib/api/user";
import { produce } from "immer";
import "./index.less";
import { GiftContent } from "./components/gift-content";
import { AspectRatio } from "@/ui/aspect-ratio";
import { VideoItem } from "../home/<USER>/components/video-item";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { CommentItem } from "@/components/work/comment-item";
import { PageParams } from "@/type";
import { CommonCommentResult } from "@/type/common-comment-result";
import { getRandomParams } from "@/utils/common";
import { queryClient } from "@/provider/query-client";
import { useUploadQueueManager } from "@/hooks/use-upload-queue-manager";
import { userWalletQueryKey } from "@/hooks/use-user-wallet";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { SelectPopup } from "@/components/select-popup";
import { MoreIcon } from "@/components/svg-icon";

export default function VideoDetailPage() {
  const { data = {} } = useAsyncValue() as { data: VideoResult };
  const { revalidate } = useRevalidator();

  const { hasPermission } = useUserVip();
  const { navigateRoute } = useNavigateRoute();

  const { data: relatedData } = useQuery({
    queryKey: ["video-related", data.id],
    queryFn: () => videoRelated(data.id!),
  });

  const [quote, setQuote] = useState<
    { id: string; content: ReactNode } | undefined
  >();

  const commentQueryKey = ["video-comment", data.id];
  const commentQueryFn = (params: PageParams) => videoComment(data.id!, params);

  const [videoInfo, setVideoInfo] = useState(data);

  useEffect(() => {
    setVideoInfo(data);
  }, [data]);

  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<Player>();

  const { data: userInfo } = useFullUserInfo();

  const isSelf = userInfo?.id === data.user?.id;

  const {
    open: openShare,
    openModal: openShareModal,
    closeModal: closeShareModal,
  } = useModal();

  useEffect(() => {
    if (videoInfo.resource && videoRef.current) {
      if (playerRef.current) {
        playerRef.current.reset();
        playerRef.current.src(videoInfo.resource);
      } else {
        playerRef.current = videojs(videoRef.current, {
          preload: "auto",
          controls: true,
          autoplay: true,
          loop: true,
          enableSmoothSeeking: true,
          language: "zh-CN",
        });
      }

      // 添加双击事件处理函数
      const handleDoubleClick = (event: MouseEvent | TouchEvent) => {
        // 检查双击是否发生在控制栏之外
        const controlBar = playerRef.current
          ?.el()
          .querySelector(".vjs-control-bar");
        if (controlBar && !controlBar.contains(event.target as Node)) {
          if (playerRef.current?.paused()) {
            playerRef.current?.play();
          } else {
            playerRef.current?.pause();
          }
        }
      };

      let lastTap = 0;
      const handleTap = (event: TouchEvent) => {
        const currentTime = new Date().getTime();
        const tapLength = currentTime - lastTap;
        if (tapLength < 500 && tapLength > 0) {
          handleDoubleClick(event);
          event.preventDefault();
        }
        lastTap = currentTime;
      };

      // 获取视频元素
      const videoElement = playerRef.current
        ?.el()
        .querySelector(".vjs-tech") as HTMLVideoElement | null;

      if (videoElement) {
        videoElement.addEventListener("touchend", handleTap);
      }

      // 清理函数
      return () => {
        if (videoElement) {
          videoElement.removeEventListener("touchend", handleTap);
        }
      };
    }
  }, [videoInfo.resource]);

  useEffect(
    () => () => {
      playerRef.current?.dispose();
    },
    []
  );

  const vipPlayerMutation = useMutation({
    mutationFn: vipPlayer,
  });

  const handleVipPlayer = async () => {
    const result = await confirmDialog.show(
      `是否使用VIP特权观看此视频，今日剩余次数:${videoInfo.vipPlayerTodayRemainingNumber}`,
      "VIP特权播放"
    );

    if (result) {
      Toast.show({
        icon: "loading",
        content: "请稍后...",
        duration: 0,
      });

      const { ok } = await vipPlayerMutation.mutateAsync(videoInfo.id!);

      if (ok) {
        Toast.show({
          icon: "loading",
          content: "加载中...",
          duration: 0,
        });

        revalidate();
      }
    }
  };

  const buyVideoMutation = useMutation({
    mutationFn: buyVideo,
  });

  const handleBuyPlayer = async () => {
    const result = await confirmDialog.show(
      `视频播放价格：${videoInfo.playPrice ?? 0}，购买后可无限次观看`,
      "视频购买"
    );

    if (result) {
      Toast.show({
        icon: "loading",
        content: "请稍后...",
        duration: 0,
      });

      const { ok } = await buyVideoMutation.mutateAsync({
        id: videoInfo.id!,
        type: 0,
      });

      if (ok) {
        Toast.show({
          icon: "loading",
          content: "加载中...",
          duration: 0,
        });

        revalidate();
      }
    }
  };

  const handleGoVip = () => {
    navigateRoute("/vip");
  };

  const likeVideoMutation = useMutation({
    mutationFn: likeVideo,
  });

  const handleLikeVideo = async () => {
    const { ok } = await likeVideoMutation.mutateAsync({
      id: videoInfo.id!,
      like: !videoInfo.hasLike,
    });

    if (ok) {
      setVideoInfo(
        produce((draft) => {
          draft.hasLike = !draft.hasLike;
          if (draft.hasLike) {
            draft.likeNumber = draft.likeNumber! + 1;
          } else {
            draft.likeNumber = draft.likeNumber! - 1;
          }
        })
      );
    }
  };

  const downloadVideoMutation = useMutation({
    mutationFn: downloadVideo,
  });

  const { addToQueue } = useDownloadQueueManager();

  const download = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok, data } = await downloadVideoMutation.mutateAsync(videoInfo.id!);

    if (ok && data) {
      setVideoInfo(
        produce((draft) => {
          draft.downloadNumber = draft.downloadNumber! + 1;
        })
      );

      Toast.show({
        content: "开始下载",
      });

      addToQueue({
        title: videoInfo.title!,
        url: data.url!,
        params: {},
      });
    }
  };

  const handleDownloadVideo = async () => {
    if (videoInfo.hasBuyDownload) {
      // 直接下载
      download();
    } else {
      // 提示购买
      const result = await confirmDialog.show(
        `视频下载价格：${videoInfo.downloadPrice ?? 0}，购买后可无限次下载`,
        "视频下载购买"
      );

      if (result) {
        // 购买
        const { ok } = await buyVideoMutation.mutateAsync({
          id: videoInfo.id!,
          type: 1,
        });

        if (ok) {
          download();
        }
      }
    }
  };

  const handleShareSuccess = () => {
    setVideoInfo(
      produce((draft) => {
        draft.shareNumber = draft.shareNumber! + 1;
      })
    );
  };

  const collectionVideoMutation = useMutation({
    mutationFn: collectionVideo,
  });

  const cancelCollectionMutations = useMutation({
    mutationFn: cancelCollection,
  });

  const handleCollectionVideo = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    if (videoInfo.hasCollection) {
      const { ok } = await cancelCollectionMutations.mutateAsync({
        targetId: videoInfo.id!,
      });

      if (ok) {
        setVideoInfo(
          produce((draft) => {
            draft.hasCollection = false;
          })
        );
      }
    } else {
      const { ok } = await collectionVideoMutation.mutateAsync(videoInfo.id!);

      if (ok) {
        setVideoInfo(
          produce((draft) => {
            draft.hasCollection = true;
          })
        );
      }
    }
  };

  const handleSetQuote = (quote: CommonCommentResult) => {
    setQuote({
      id: quote.id!,
      content: `回复：${quote?.user?.nickname} “${quote.voice ? "[语音]" : ""}${
        (quote.resources ?? []).some((r) => r.duration) ? "[视频]" : ""
      }${(quote.resources ?? []).some((r) => !r.duration) ? "[图片]" : ""}${
        quote.content
      }”`,
    });
  };

  const videoGiftGiveMutation = useMutation({
    mutationFn: videoGiftGive,
  });

  const handleGiveGift = async (gift: GiftResult, note?: string) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await videoGiftGiveMutation.mutateAsync({
      videoId: videoInfo.id!,
      giftId: gift.id!,
      note,
    });

    if (ok) {
      Toast.show({
        content: "礼物赠送成功",
      });

      revalidate();
      queryClient.refetchQueries({
        queryKey: userWalletQueryKey,
      });
    }
  };

  const videoCommentPublishCheckMutations = useMutation({
    mutationFn: videoCommentPublishCheck,
  });

  const { addToQueue: addToUploadQueue } = useUploadQueueManager();

  const handleRefresh = async () => {
    // 重置评论列表
    queryClient.resetQueries({ queryKey: commentQueryKey });
  };

  const handleComment = async (data: FinishType) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await videoCommentPublishCheckMutations.mutateAsync({
      targetId: videoInfo.id!,
      content: data.text,
      ats: data.ats,
      voice: data.voice ? getRandomParams() : undefined,
      resources: data.resource
        ? data.resource.map(() => getRandomParams())
        : undefined,
      topId: quote?.id,
      parentId: quote?.id,
    });

    if (ok) {
      addToUploadQueue(
        {
          title: "视频评论",
          url: videoCommentPublishUrl,
          params: {
            targetId: videoInfo.id!,
            content: data.text,
            ats: data.ats,
            voice: data.voice,
            resources: data.resource,
            topId: quote?.id,
            parentId: quote?.id,
          },
        },
        () => {
          handleRefresh();
        }
      );
    }
  };

  const handleReportClick = () => {
    navigateRoute("/report", {
      id: data?.id!,
      type: ReportTargetType.Video,
    });
  };

  return (
    <PageWrapper className="bg-scaffold-background">
      <NavigationBar
        canBack
        title="视频详细"
        action={
          !isSelf ? (
            <SelectPopup
              options={[
                {
                  title: "举报",
                  value: "report",
                  onClick: handleReportClick,
                },
              ]}
            >
              <MoreIcon size={30} />
            </SelectPopup>
          ) : undefined
        }
      />
      <PageMain
        extra={
          <CommentInput
            key={videoInfo.id}
            quote={quote}
            onClearQuote={() => setQuote(undefined)}
            onGiveGift={handleGiveGift}
            onFinish={handleComment}
          />
        }
      >
        <div className="video-detail flex flex-col">
          <div className="relative h-[260px]">
            <div className="w-full h-full">
              <video
                ref={videoRef}
                className="video-js vjs-default-skin vjs-big-play-centered w-full h-full"
              >
                <source src={videoInfo.resource} type="application/x-mpegURL" />
              </video>
            </div>
            {!videoInfo.hasBuyPlayer ? (
              <div className="absolute top-[30px] right-[5px]">
                {(videoInfo.vipPlayerTodayRemainingNumber ?? 0) > 0 ? (
                  <span
                    className="inline-flex px-[5px] bg-black/70 rounded-l-[5px]"
                    onClick={handleVipPlayer}
                  >
                    <span
                      className="text-white text-[22px]"
                      style={{ zoom: 0.5 }}
                    >
                      VIP点击观看 {formatDuration(videoInfo.duration!)}
                      完整版(今日剩余${videoInfo.vipPlayerTodayRemainingNumber}
                      次)
                    </span>
                  </span>
                ) : null}
                {(videoInfo.vipPlayerTodayRemainingNumber ?? 0) <= 0 ? (
                  <span
                    className="inline-flex px-[5px] bg-black/70 rounded-l-[5px]"
                    onClick={handleBuyPlayer}
                  >
                    <span
                      className="text-white text-[22px]"
                      style={{ zoom: 0.5 }}
                    >
                      {videoInfo.playPrice ?? 0}
                      {userWalletType.currency.label}购买{" "}
                      {formatDuration(videoInfo.duration!)} 完整版
                    </span>
                  </span>
                ) : null}
              </div>
            ) : null}
          </div>
          {!videoInfo.hasBuyPlayer &&
          !hasPermission(VipPermission.VideoFree) ? (
            <div className="bg-[#6712FF]/10 px-[15px] py-2 flex items-center justify-between">
              <span className="text-[13px] text-[#6712FF]">
                开通视频会员，可免费观看
              </span>
              <Button
                variant="primary"
                className="w-[80px] h-[30px] text-[13px]"
                onClick={handleGoVip}
              >
                前往了解
              </Button>
            </div>
          ) : null}
          <div className="h-[15px]" />
          <div className="p-[15px] bg-app-bar-background flex flex-col">
            <UserInfo user={videoInfo.user!} size={50} />
            <Divider className="border-divider" />
            <span className="text-[15px]">{videoInfo.title}</span>
            <span className="text-sm text-hint-color">
              {videoInfo.content ?? "暂无描述内容"}
            </span>
            <span className="h-[10px]" />
            <span className="text-xs text-hint-color">
              {videoInfo.playNumber}播放 ·{" "}
              {formatTimeLine(dayjs(videoInfo.releaseDate).valueOf())}
            </span>
            <span className="h-[10px]" />
            <div className="flex gap-1 items-center">
              <span className="text-xs">标签: </span>
              <div className="flex gap-[10px] flex-wrap">
                {videoInfo.tags?.map((tag) => (
                  <span
                    key={tag.id}
                    className="inline-flex px-[5px] py-[2px] bg-scaffold-background rounded-[15px]"
                  >
                    <span
                      className="text-[20px] text-[#999]"
                      style={{ zoom: 0.5 }}
                    >
                      {tag.name}
                    </span>
                  </span>
                ))}
              </div>
            </div>
            <span className="h-[15px]" />
            <div className="mx-[30px] h-[55px] bg-scaffold-background rounded-[28px] text-hint-color flex items-center">
              <div
                className="flex-1 flex flex-col gap-[5px] items-center"
                onClick={handleLikeVideo}
              >
                <span>
                  <CommonIcon
                    name={videoInfo.hasLike ? "like-selected" : "like"}
                    w={17}
                    h={17}
                  />
                </span>
                <span className="inline-flex">
                  <span className="text-[20px]" style={{ zoom: 0.5 }}>
                    点赞 {videoInfo.likeNumber}
                  </span>
                </span>
              </div>
              <div
                className="flex-1 flex flex-col gap-[5px] items-center"
                onClick={handleDownloadVideo}
              >
                <span>
                  <CommonIcon name="download" w={17} h={17} />
                </span>
                <span className="inline-flex">
                  <span className="text-[20px]" style={{ zoom: 0.5 }}>
                    下载 {videoInfo.downloadNumber}
                  </span>
                </span>
              </div>
              <div
                className="flex-1 flex flex-col gap-[5px] items-center"
                onClick={() => openShareModal()}
              >
                <span>
                  <CommonIcon name="forward" w={17} h={17} />
                </span>
                <span className="inline-flex">
                  <span className="text-[20px]" style={{ zoom: 0.5 }}>
                    分享 {videoInfo.shareNumber}
                  </span>
                </span>
              </div>
              <div
                className="flex-1 flex flex-col gap-[5px] items-center"
                onClick={handleCollectionVideo}
              >
                <span>
                  <CommonIcon
                    name={
                      videoInfo.hasCollection
                        ? "collection-selected"
                        : "collection"
                    }
                    w={17}
                    h={17}
                  />
                </span>
                <span className="inline-flex">
                  <span className="text-[20px]" style={{ zoom: 0.5 }}>
                    收藏
                  </span>
                </span>
              </div>
            </div>
            <span className="h-[10px]" />
            <span className="inline-flex justify-center">
              <span
                className="text-[20px] text-hint-color"
                style={{ zoom: 0.5 }}
              >
                建议使用联通或电信网下载，移动或许会被限流~
              </span>
            </span>
            {videoInfo.giftGiver && videoInfo.giftSummary ? (
              <div className="py-[15px]">
                <GiftContent data={videoInfo} />
              </div>
            ) : null}
          </div>
          {relatedData?.data?.length ? (
            <div className="p-[15px] flex flex-col gap-[10px]">
              <span className="text-sm">相关视频</span>
              <div className="grid grid-cols-2 gap-[15px]">
                {relatedData.data.map((item) => (
                  <AspectRatio key={item.id} ratio={1}>
                    <VideoItem data={item} />
                  </AspectRatio>
                ))}
              </div>
            </div>
          ) : null}
          <div className="p-[15px] flex flex-col gap-[10px]">
            <span className="text-sm">相关评论</span>
            <ScrollLoadData queryKey={commentQueryKey} queryFn={commentQueryFn}>
              {(comments) => (
                <div className="flex flex-col pt-5">
                  {comments.map((item, index) => (
                    <Fragment key={item.id}>
                      <CommentItem
                        type="video"
                        id={videoInfo.id!}
                        data={item}
                        onSetQuote={handleSetQuote}
                      />
                      {index !== comments.length - 1 ? (
                        <Divider className="border-divider" />
                      ) : null}
                    </Fragment>
                  ))}
                </div>
              )}
            </ScrollLoadData>
          </div>
        </div>
        <ShareModal
          type={ShareTargetType.Video}
          id={videoInfo.id!}
          open={openShare}
          onClose={closeShareModal}
          onSuccess={handleShareSuccess}
        />
      </PageMain>
    </PageWrapper>
  );
}
