import { queryClient } from "@/provider/query-client";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { layoutConfigOptions } from "@/utils/query-options";
import { defer, LoaderFunction } from "react-router-dom";

export const loader: LoaderFunction = async () => {
  return defer({
    data: queryClient
      .fetchQuery(layoutConfigOptions(LayoutConfigKey.Chat))
      .catch((err) => err),
  });
};
