import { Chat } from "@tencentcloud/chat-uikit-react";
import { useAsyncValue, useSearchParams } from "react-router-dom";
import {
  IConversationModel,
  StoreName,
  TUIConversationService,
  TUIStore,
} from "@tencentcloud/chat-uikit-engine";
import { useEffect, useMemo, useState } from "react";
import { LayoutConfigChat } from "@/type/layout-config";
import { PageWrapper } from "@/components/page-wrapper";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { MessageList } from "@/components/work/message-list/message-list";
import { ChatMessage } from "@/components/work/chat-message/chat-message";
import { useQuery } from "@tanstack/react-query";
import { getUserChatProfile } from "@/lib/api/user";
import { UserOnlineState, userWalletType } from "@/utils/enums";
import { formatTimeLine } from "@/utils/format-time-line";
import dayjs from "dayjs";
import { NoticeBar } from "antd-mobile";
import { useUserWallet } from "@/hooks/use-user-wallet";
import { MessageInput } from "@/components/work/message-input/message-input";
import { SupervisorAccountIcon } from "@/components/svg-icon";
import { useNavigateRoute } from "@/hooks/use-navigate-route";

export default function ChatPage() {
  const { data: layoutConfig } = useAsyncValue() as { data: LayoutConfigChat };

  const [conversationProfile, setConversationProfile] = useState<
    IConversationModel | undefined
  >(undefined);

  const [showChargeTips, setShowChargeTips] = useState(true);

  const { getBalanceByType } = useUserWallet();

  // 是否是单聊
  const isC2C = !!conversationProfile?.userProfile?.userID;

  // 是否是群聊
  // const isGroup = !!conversationProfile?.groupProfile?.groupID;

  // 是否是系统IM用户
  const isSystemImUser = layoutConfig.systemImUserList?.includes(
    conversationProfile?.userProfile?.userID
  );

  const { data: userChatProfile, isLoading: userChatProfileLoading } = useQuery(
    {
      queryKey: ["user-chat-profile"],
      queryFn: () =>
        getUserChatProfile({
          userId: conversationProfile?.userProfile?.userID,
        }),
      enabled: isC2C && !isSystemImUser,
    }
  );

  const user = userChatProfile?.data?.user;

  const onlineStatusText = useMemo(() => {
    if (user) {
      if (user.onlineState === UserOnlineState.online) {
        return "在线";
      }

      if (user.lastOnlineTime) {
        return `${formatTimeLine(dayjs(user.lastOnlineTime).valueOf())}在线`;
      }
    }

    return null;
  }, [user]);

  const [searchParams] = useSearchParams();
  const cid = searchParams.get("cid");
  const uid = searchParams.get("uid");
  const gid = searchParams.get("gid");

  const conversationID = useMemo(() => {
    if (cid) {
      return cid;
    }

    if (gid) {
      return `GROUP${gid}`;
    }

    if (uid) {
      return `C2C${uid}`;
    }

    return null;
  }, [cid, gid, uid]);

  useEffect(() => {
    const onMessageListUpdated = (messageList: any) => {
      console.warn("messageList", messageList);
    };
    TUIStore.watch(StoreName.CHAT, {
      messageList: onMessageListUpdated,
      newMessageList: (newMessageList) => {
        console.warn("newMessageList", newMessageList);
      },
    });
  }, []);

  useEffect(() => {
    if (conversationID) {
      TUIConversationService.getConversationProfile(conversationID)
        .then((res) => {
          setConversationProfile(res.data.conversation);
          TUIConversationService.switchConversation(conversationID);
        })
        .catch((err) => {
          console.error(err);
        });
    }
  }, [conversationID]);

  const renderCustomInput = useMemo(() => {
    if (isSystemImUser) {
      return <div />;
    }

    if (userChatProfileLoading) {
      return (
        <div className="p-[15px] text-hint-color text-center bg-app-bar-background">
          加载中...
        </div>
      );
    }

    if (isC2C && userChatProfile?.data?.disabled) {
      return (
        <div className="p-[15px] text-hint-color text-center bg-app-bar-background">
          {userChatProfile?.data?.disabledTips ?? "你无法与该用户聊天"}
        </div>
      );
    }

    return null;
  }, [
    isC2C,
    isSystemImUser,
    userChatProfile?.data?.disabled,
    userChatProfile?.data?.disabledTips,
    userChatProfileLoading,
  ]);

  const { navigateRoute } = useNavigateRoute();

  const handleMoreClick = () => {
    navigateRoute("/user-home", {
      id: conversationProfile?.userProfile?.userID,
    });
  };

  return (
    <PageWrapper className="bg-scaffold-background">
      <NavigationBar
        content={
          <div className="flex flex-col items-center">
            <span className="text-lg">
              {conversationProfile?.userProfile.nick}
            </span>
            {onlineStatusText ? (
              <span className="text-hint-color inline-flex">
                <span className="text-lg" style={{ zoom: 0.5 }}>
                  {onlineStatusText}
                </span>
              </span>
            ) : null}
          </div>
        }
        canBack
        action={
          <span onClick={handleMoreClick}>
            <SupervisorAccountIcon size={20} />
          </span>
        }
      />
      <PageMain needScroll={false}>
        <div className="flex flex-col h-full">
          <div className="flex-none">
            {userChatProfile?.data?.chargeTips && showChargeTips ? (
              <NoticeBar
                closeable
                icon={null}
                content={`${userChatProfile?.data?.chargeTips}${
                  userChatProfile?.data?.chatCharge
                    ? `[剩余:${getBalanceByType(
                        userWalletType.todayChatQuota.value
                      )}${
                        userWalletType.todayChatQuota.label
                      }、${getBalanceByType(
                        userWalletType.lifetimeChatQuota.value
                      )}${
                        userWalletType.lifetimeChatQuota.label
                      }、${getBalanceByType(userWalletType.bonus.value)}${
                        userWalletType.bonus.label
                      }、${getBalanceByType(userWalletType.currency.value)}${
                        userWalletType.currency.label
                      }]`
                    : ""
                }`}
                onClose={() => setShowChargeTips(false)}
                style={{
                  "--height": "25px",
                  "--text-color": "hsl(var(--foreground))",
                }}
              />
            ) : null}
          </div>
          <div className="flex-1 h-0">
            <Chat className="flex flex-col h-full" TUIMessage={ChatMessage}>
              <MessageList />

              <MessageInput
                list={layoutConfig.more.c2c}
                userChatProfile={userChatProfile?.data}
              />
            </Chat>
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
