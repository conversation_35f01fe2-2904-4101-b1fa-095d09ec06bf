import { CalendarSwiper } from "@/components/calendar-swiper";
import {
  signInBackground,
  signInBackgroundCalendar,
} from "@/components/image-list";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ArrowFillIcon } from "@/components/svg-icon";
import { confirmDialog } from "@/components/work/confirm-dialog";
import { useConfigList } from "@/hooks/use-config-list";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { useScrollOpacity } from "@/hooks/user-scroll-opacity";
import { getPunchInInfo, rePunchIn } from "@/lib/api/user";
import { queryClient } from "@/provider/query-client";
import { userWalletType } from "@/utils/enums";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Image, Toast } from "antd-mobile";
import dayjs from "dayjs";
import { useState } from "react";

export default function SignInPage() {
  const { opacity, scrollRef } = useScrollOpacity(200);
  const { getSystemConfig } = useConfigList();

  const [date, setDate] = useState(dayjs());

  const queryKey = ["punchInInfo", date.format("YYYY-MM")];
  const { data, isLoading } = useQuery({
    queryKey,
    queryFn: () => getPunchInInfo(`${date.format("YYYY-MM-DD")} 00:00:00`),
  });

  const punchInInfo = data?.data;

  const handleDateChange = (value: dayjs.Dayjs) => {
    setDate(value);
  };

  const handleNextMonth = () => {
    setDate(date.add(1, "month"));
  };

  const handlePrevMonth = () => {
    setDate(date.subtract(1, "month"));
  };

  const rePunchInMutation = useMutation({ mutationFn: rePunchIn });

  const handleRePunchInClick = async (day: dayjs.Dayjs) => {
    const bo = await confirmDialog.show(
      `当此补签费用为${getSystemConfig("RE_SIGNING_PRICE")}${
        userWalletType.currency.label
      }，是否继续？"`,
      "是否补签"
    );

    if (bo) {
      Toast.show({
        icon: "loading",
        content: "请稍后...",
        duration: 0,
      });

      const { ok } = await rePunchInMutation.mutateAsync(
        `${day.format("YYYY-MM-DD")} 00:00:00`
      );

      if (ok) {
        queryClient.refetchQueries({ queryKey });
      }
    }
  };

  const { navigateRoute } = useNavigateRoute();

  const handleRePunchInRecordClick = () => {
    navigateRoute("/re-punch-in-record");
  };

  return (
    <PageWrapper className="relative z-10 bg-[#FFF1E6]">
      <NavigationBar
        canBack
        title="签到"
        className="bg-transparent"
        style={
          opacity > 0.5
            ? {
                background: `hsla(var(--app-bar-background)/${opacity})`,
                color: `hsla(var(--foreground)/${opacity})`,
              }
            : { color: "#fff" }
        }
        action={
          <span
            style={opacity > 0.5 ? {} : { color: "#fff" }}
            onClick={handleRePunchInRecordClick}
          >
            补签记录
          </span>
        }
      />
      <PageMain scrollRef={scrollRef}>
        <div className="relative z-10 w-[348px] mx-auto mt-[98px]">
          <div className="px-[5px] pb-[15px] rounded-[10px]">
            <div>
              <div className="p-[15px]">
                <div className="flex justify-between items-center">
                  <span
                    className="w-[14px] h-[14px] rounded-[14px] bg-[#ff572736]"
                    onClick={handlePrevMonth}
                  >
                    <ArrowFillIcon
                      size={14}
                      color="#D18A74"
                      className="rotate-180"
                    />
                  </span>
                  <span className="text-[#942E11] text-[17px]">
                    {date.format("YYYY年MM月")}
                  </span>
                  <span
                    className="w-[14px] h-[14px] rounded-[14px] bg-[#ff572736]"
                    onClick={handleNextMonth}
                  >
                    <ArrowFillIcon size={14} color="#D18A74" />
                  </span>
                </div>
              </div>
              <CalendarSwiper
                isLoading={isLoading}
                date={date}
                punchInInfo={punchInInfo}
                onDateChange={handleDateChange}
                onRePunchInClick={handleRePunchInClick}
              />
            </div>
          </div>
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-[-1]">
            <Image
              src={signInBackgroundCalendar}
              width="100%"
              height="100%"
              placeholder={null}
              fallback={null}
              className="object-cover"
            />
          </div>
        </div>
        <div className="p-[15px]">
          <div className="flex flex-col text-black bg-white rounded-[10px] p-[15px] gap-[10px]">
            <span className="text-sm">签到说明</span>
            <span className="whitespace-pre-wrap text-[#666]">
              {getSystemConfig("SIGNING_NOTE")}
            </span>
          </div>
        </div>
      </PageMain>
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-[-1]">
        <Image
          src={signInBackground}
          width="100%"
          height="auto"
          placeholder={null}
          fallback={null}
          className="object-cover relative top-[-50px]"
        />
      </div>
    </PageWrapper>
  );
}
