import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { DeleteIcon } from "@/components/svg-icon";
import { confirmDialog } from "@/components/work/confirm-dialog";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { bankList, deleteBankCard } from "@/lib/api/wallet";
import { useBankState } from "@/store/bank-state";
import { UserBankCard } from "@/type/user-bank-card";
import { Button } from "@/ui/button";
import { useMutation } from "@tanstack/react-query";
import { Divider, Toast } from "antd-mobile";

export default function BankList() {
  const queryKey = ["bank-list"];
  const queryFn = () => bankList();

  const { navigateRoute, navigateBack } = useNavigateRoute();

  const { bankState, setBankState } = useBankState();

  const handleSelectBank = (bank: UserBankCard) => {
    setBankState(bank);
    navigateBack();
  };

  const handleAddBank = () => {
    navigateRoute("/bind-bank-card", {});
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  const deleteBankMutation = useMutation({
    mutationFn: deleteBankCard,
  });

  const handleDeleteBank = async (id: string) => {
    const result = await confirmDialog.show(
      "删除后无法撤回",
      "是否删除银行卡？"
    );

    if (result) {
      Toast.show({
        icon: "loading",
        content: "请稍后...",
        duration: 0,
      });

      const { ok } = await deleteBankMutation.mutateAsync(id);

      if (ok) {
        handleRefresh();
        if (bankState?.id === id) {
          setBankState(undefined);
        }
      }
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="银行卡列表" />
      <PageMain className="bg-scaffold-background" onRefresh={handleRefresh}>
        <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
          {(data) => {
            return (
              <div className="p-[15px] flex flex-col gap-[15px]">
                {data.map((item) => (
                  <div
                    key={item.id}
                    className="bg-app-bar-background rounded-[10px] p-[15px]"
                    onClick={() => handleSelectBank(item)}
                  >
                    <div
                      className="flex items-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteBank(item.id!);
                      }}
                    >
                      <span className="flex-1 text-[15px] font-bold">
                        {item.bankName}
                      </span>
                      <span className="text-[#FF3460] flex">
                        <DeleteIcon size={15} />
                        删除
                      </span>
                    </div>
                    <Divider className="border-divider my-[10px]" />
                    <div className="flex gap-[5px]">
                      <span>{item.userName}</span>
                      <span className="flex-1 text-end">
                        {item.bankCardNumber}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            );
          }}
        </ScrollLoadData>
      </PageMain>
      <div className="p-[15px] bg-scaffold-background">
        <Button
          variant="primary"
          size="lg"
          className="w-full"
          onClick={handleAddBank}
        >
          绑定新的银行卡
        </Button>
      </div>
    </PageWrapper>
  );
}
