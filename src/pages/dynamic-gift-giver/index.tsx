import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { UserInfo } from "@/components/work/user-info";
import { dynamicGiftGiver } from "@/lib/api/dynamic";
import { PageParams } from "@/type";
import { userWalletType } from "@/utils/enums";
import { useSearchParams } from "react-router-dom";

export default function DynamicGiftGiver() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("dynamicId");

  const queryKey = ["dynamic-gift-giver", id];
  const queryFn = (params: PageParams) =>
    dynamicGiftGiver({ id: id || "" }, params);

  return (
    <PageWrapper>
      <NavigationBar canBack title="赠礼人列表" />
      <PageMain>
        <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="flex flex-col p-[15px]">
              {data.map((item, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="flex-1">
                    <UserInfo user={item.user!} size={50} />
                  </div>
                  <span className="text-hint-color">
                    {item.number}个(≈{item.totalAmount}
                    {userWalletType.currency.label})
                  </span>
                </div>
              ))}
            </div>
          )}
        </ScrollLoadData>
      </PageMain>
    </PageWrapper>
  );
}
