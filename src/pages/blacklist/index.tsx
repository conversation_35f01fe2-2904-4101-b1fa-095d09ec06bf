import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { UserInfo } from "@/components/work/user-info";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { blacklistList, removeBlacklist } from "@/lib/api/user";
import { PageParams } from "@/type";
import { Button } from "@/ui/button";
import { useMutation } from "@tanstack/react-query";
import { Divider, Toast } from "antd-mobile";
import { Fragment } from "react/jsx-runtime";

export default function BlacklistPage() {
  const queryKey = ["blacklist-list"];
  const queryFn = (params: PageParams) => blacklistList(params);

  const { handleRefresh } = usePageListRefresh(queryKey);

  const removeBlackMutation = useMutation({
    mutationFn: removeBlacklist,
  });

  const handleRemoveBlack = async (id: string) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await removeBlackMutation.mutateAsync(id);

    if (ok) {
      handleRefresh();
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="黑名单列表" />
      <PageMain>
        <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="p-[15px] flex flex-col">
              {data.map((item, index) => (
                <Fragment key={item.id}>
                  <div className="flex items-center gap-[10px]">
                    <div className="flex-1">
                      <UserInfo user={item} size={50} />
                    </div>
                    <Button
                      variant="secondary"
                      className="text-sm w-[90px] h-[40px]"
                      onClick={() => handleRemoveBlack(item.id!)}
                    >
                      移除
                    </Button>
                  </div>
                  {index !== data.length - 1 ? (
                    <Divider className="border-divider" />
                  ) : null}
                </Fragment>
              ))}
            </div>
          )}
        </ScrollLoadData>
      </PageMain>
    </PageWrapper>
  );
}
