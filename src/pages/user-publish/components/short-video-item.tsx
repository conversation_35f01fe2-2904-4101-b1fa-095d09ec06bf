import { DefaultLoadImage } from "@/components/default-load-image";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { ShortVideoResult } from "@/type/short-video-result";
import { Image } from "antd-mobile";

interface Props {
  index: number;
  queryKey: any[];
  queryData: any;
  url: string;
  data: ShortVideoResult;
}

export const ShortVideoItem = (props: Props) => {
  const { data, index, queryData, url, queryKey } = props;

  const { navigateRoute } = useNavigateRoute();

  const handleClick = () => {
    navigateRoute(
      "/short-video-publish-list",
      {
        defaultIndex: index,
      },
      {
        state: {
          queryKey,
          url,
          data: queryData,
        },
      }
    );
  };

  return (
    <div className="relative w-full h-full" onClick={handleClick}>
      <Image
        src={data.image}
        width="100%"
        height="100%"
        fit="cover"
        placeholder={<DefaultLoadImage />}
        fallback={<DefaultLoadImage />}
        onLoad={() => {}}
      />
      <div className="absolute bottom-[5px] left-[5px] w-full flex flex-col">
        <span className="text-white text-[20px]" style={{ zoom: 0.5 }}>
          {data.likeNumber}人点赞
        </span>
        <span className="text-white text-[20px]" style={{ zoom: 0.5 }}>
          播放{data.playNumber}次
        </span>
      </div>
    </div>
  );
};
