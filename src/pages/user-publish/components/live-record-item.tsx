import { DefaultLoadImage } from "@/components/default-load-image";
import { LiveRecordResult } from "@/type/live-record-result";
import { Image } from "antd-mobile";
import dayjs from "dayjs";

interface Props {
  data: LiveRecordResult;
}

export const LiveRecordItem = (props: Props) => {
  const { data } = props;

  return (
    <div className="py-[5px] flex flex-col">
      <div className="flex text-base">
        {data.cdate ? (
          <span>{dayjs(data.cdate).format("YYYY年MM月DD日")}</span>
        ) : null}
        {data.address ? <span> · {data.address}</span> : null}
      </div>
      <div>
        {data.startTime ? (
          <span>{dayjs(data.startTime).format("HH:mm")}</span>
        ) : (
          <span>未知</span>
        )}
        -
        {data.endTime ? (
          <span>{dayjs(data.endTime).format("HH:mm")}</span>
        ) : (
          <span>未知</span>
        )}
        <span> {data.note}</span>
      </div>
      <div className="pt-[10px]">
        {data.cover ? (
          <Image
            src={data.cover}
            width="100%"
            height="100%"
            fit="cover"
            className="rounded-[5px]"
            placeholder={<DefaultLoadImage />}
            fallback={<DefaultLoadImage />}
            onLoad={() => {}}
          />
        ) : null}
      </div>
    </div>
  );
};
