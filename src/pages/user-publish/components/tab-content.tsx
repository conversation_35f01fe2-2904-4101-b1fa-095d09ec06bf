import { ScrollLoadData } from "@/components/scroll-load-data";
import { getLayoutPageList } from "@/lib/api/layout";
import { PageParams } from "@/type";
import { BaseUrlConfig } from "@/type/layout-config";
import { Fragment, useEffect, useState } from "react";
import { PullToRefresh } from "@/components/pull-to-refresh";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { ScrollArea } from "@/ui/scroll-area";
import { Divider } from "antd-mobile";
import { DynamicItem } from "@/components/work/dynamic-item";
import { DynamicResult } from "@/type/dynamic-result";
import { VideoItem } from "@/pages/home/<USER>/components/video-item";
import { VideoResult } from "@/type/video-result";
import { UserTransactionResult } from "@/type/user-transaction-result";
import { TradeItem } from "@/pages/home/<USER>/trade/components/trade-item";
import { PlaybackTabItem } from "@/pages/home/<USER>/components/playback-tab-item";
import { PlaybackVideoResult } from "@/type/playback-video-result";
import { LiveRecordResult } from "@/type/live-record-result";
import { LiveRecordItem } from "./live-record-item";
import { ShortVideoItem } from "./short-video-item";
import { ShortVideoResult } from "@/type/short-video-result";
import { AspectRatio } from "@/ui/aspect-ratio";

interface Props {
  isActive: boolean;
  index: number;
  id?: string;
  data: BaseUrlConfig & { custom: { widget: string } };
}

export const TabContent = (props: Props) => {
  const { isActive, index, data, id } = props;

  const [isInit, setIsInit] = useState(false);

  const queryKey = [data.url, index, { ...data.params, userId: id }];
  const queryFn = (params: PageParams) => {
    return getLayoutPageList<any[]>({
      url: data.url,
      params,
      data: { ...data.params, userId: id },
    });
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  useEffect(() => {
    if (isActive) {
      setIsInit(true);
    }
  }, [isActive]);

  const renderItem = (list: any[]) => {
    switch (data.custom.widget) {
      case "Dynamic":
        return (
          <div className="flex flex-col w-full pt-[15px]">
            {list.map((item, index) => (
              <Fragment key={item.id}>
                <DynamicItem data={item as DynamicResult} />
                {index !== list.length - 1 ? (
                  <Divider className="border-divider my-[10px]" />
                ) : null}
              </Fragment>
            ))}
          </div>
        );
      case "Video":
        return (
          <div className="grid grid-cols-2 gap-[10px]">
            {list.map((item) => (
              <AspectRatio key={item.id} ratio={1}>
                <VideoItem data={item as VideoResult} />
              </AspectRatio>
            ))}
          </div>
        );
      case "Transaction":
        return (
          <div className="flex flex-col w-full gap-[20px]">
            {list.map((item) => (
              <TradeItem key={item.id} data={item as UserTransactionResult} />
            ))}
          </div>
        );

      case "LiveRecord":
        return (
          <div className="flex flex-col w-full pt-[15px]">
            {list.map((item, index) => (
              <Fragment key={item.id}>
                <LiveRecordItem data={item as LiveRecordResult} />
                {index !== list.length - 1 ? (
                  <Divider className="border-divider my-[10px]" />
                ) : null}
              </Fragment>
            ))}
          </div>
        );
      case "PlaybackVideo":
        return (
          <div className="grid grid-cols-2 gap-[10px]">
            {list.map((item) => (
              <AspectRatio key={item.id} ratio={1}>
                <PlaybackTabItem data={item as PlaybackVideoResult} />
              </AspectRatio>
            ))}
          </div>
        );
      case "ShortVideo":
        return (
          <div className="grid grid-cols-3">
            {list.map((item, index) => (
              <AspectRatio key={item.id} ratio={0.7}>
                <ShortVideoItem
                  data={item as ShortVideoResult}
                  queryData={{ ...data.params, userId: id }}
                  url={data.url}
                  index={index}
                  queryKey={queryKey}
                />
              </AspectRatio>
            ))}
          </div>
        );
    }
  };

  if (!isInit) {
    return null;
  }

  return (
    <ScrollArea className={isActive ? "h-full" : "hidden"}>
      <PullToRefresh onRefresh={handleRefresh}>
        <ScrollLoadData size={10} queryKey={queryKey} queryFn={queryFn}>
          {(list) => renderItem(list)}
        </ScrollLoadData>
      </PullToRefresh>
    </ScrollArea>
  );
};
