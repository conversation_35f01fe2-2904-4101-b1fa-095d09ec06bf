import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { Tabs } from "@/components/work/tabs";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { LayoutConfigUserPublish } from "@/type/layout-config";
import { ScrollArea, ScrollBar } from "@/ui/scroll-area";
import { useState } from "react";
import { useAsyncValue, useSearchParams } from "react-router-dom";
import { TabContent } from "./components/tab-content";

export default function UserPublishPage() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id") || undefined;
  const index = searchParams.get("index") || undefined;

  const { data: layoutConfig } = useAsyncValue() as {
    data: LayoutConfigUserPublish;
  };
  const { data: userInfo } = useFullUserInfo();

  const defaultIndex = layoutConfig.tabs.defaultIndex ?? -1;
  const tabs = layoutConfig.tabs.items;

  const isSelf = !id || userInfo?.id === id;

  const [activeKey, setActiveKey] = useState(
    index ? Number(index) : defaultIndex
  );

  return (
    <PageWrapper>
      <NavigationBar
        canBack
        title={isSelf ? "我的发布" : "他/她的发布"}
        className="bg-transparent"
      />
      <PageMain needScroll={false}>
        <div className="flex flex-col h-full p-[15px]">
          <ScrollArea>
            <div>
              <Tabs tabs={tabs} value={activeKey} onChange={setActiveKey} />
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
          <div className="flex-1 h-0">
            {tabs.map((tab, index) => (
              <TabContent
                id={id}
                key={index}
                index={index}
                isActive={index === activeKey}
                data={tab}
              />
            ))}
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
