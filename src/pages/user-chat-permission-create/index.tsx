import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { useConfigList } from "@/hooks/use-config-list";
import { createUserChatPermission } from "@/lib/api/user";
import { Button } from "@/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/ui/form";
import { Input } from "@/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import { useForm } from "react-hook-form";
import { useNavigate, useSearchParams } from "react-router-dom";
import { z } from "zod";

export default function UserChatPermissionCreate() {
  const [searchParams] = useSearchParams();

  const userId = searchParams.get("userId");

  const { getSystemConfig } = useConfigList();

  const minDay = getSystemConfig("USER_CHAT_PERMISSION_MIN_DAY");
  const maxDay = getSystemConfig("USER_CHAT_PERMISSION_MAX_DAY");

  const minPrice = getSystemConfig("USER_CHAT_PERMISSION_MIN_PRICE");
  const maxPrice = getSystemConfig("USER_CHAT_PERMISSION_MAX_PRICE");

  const formSchema = z.object({
    dayNumber: z.coerce.number().gte(Number(minDay)).lte(Number(maxDay)),
    price: z.coerce.number().gte(Number(minPrice)).lte(Number(maxPrice)),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });

  const { mutateAsync } = useMutation({
    mutationFn: createUserChatPermission,
  });

  const navigate = useNavigate();

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    Toast.show({
      content: "请稍后...",
      icon: "loading",
      duration: 0,
    });

    const { ok } = await mutateAsync({
      userId: userId!,
      ...data,
    });

    if (ok) {
      navigate(-1);
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="互动权限邀请" />
      <PageMain className="bg-scaffold-background">
        <div className="py-[10px] px-[15px]">
          <Form {...form}>
            <div className="flex flex-col gap-5">
              <FormField
                control={form.control}
                name="dayNumber"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <div className="flex flex-col gap-[10px] p-[15px] bg-app-bar-background rounded-[5px]">
                        <FormLabel>开通天数</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={`范围:${minDay} - ${maxDay}`}
                            {...field}
                            className="border-none bg-hint-color/10 rounded-[5px]"
                            type="number"
                          />
                        </FormControl>
                      </div>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <div className="flex flex-col gap-[10px] p-[15px] bg-app-bar-background rounded-[5px]">
                        <FormLabel>价格</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={`范围:${minPrice} - ${maxPrice}`}
                            {...field}
                            className="border-none bg-hint-color/10 rounded-[5px]"
                            type="number"
                          />
                        </FormControl>
                      </div>
                    </FormItem>
                  );
                }}
              />
              <div className="flex flex-col gap-[10px] p-[15px] bg-app-bar-background rounded-[5px]">
                <span>邀请说明</span>
                <div>{getSystemConfig("USER_CHAT_PERMISSION_CREATE_NOTE")}</div>
              </div>
            </div>
          </Form>
        </div>
        <div className="pt-[50px] px-[15px]">
          <Button
            variant="primary"
            size="lg"
            className="w-full"
            onClick={form.handleSubmit(onSubmit)}
          >
            发起邀请
          </Button>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
