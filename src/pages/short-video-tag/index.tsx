import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { shortVideoTag } from "@/lib/api/video";
import { PageParams } from "@/type";
import { AspectRatio } from "@/ui/aspect-ratio";
import { Image } from "antd-mobile";
import { useSearchParams } from "react-router-dom";

export default function ShortVideoTag() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id");
  const name = searchParams.get("name");

  const queryKey = ["short-video-tag", id];
  const queryFn = (params: PageParams) =>
    shortVideoTag({ id: id || "" }, params);

  const { handleRefresh } = usePageListRefresh(queryKey);
  const { navigateRoute } = useNavigateRoute();

  const handleClick = (defaultIndex: number) => {
    navigateRoute("/short-video-list", {
      id: id,
      defaultIndex,
    });
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title={`#${name}#`} />
      <PageMain onRefresh={handleRefresh}>
        <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="grid grid-cols-3">
              {data.map((item, index) => (
                <AspectRatio key={item.id} ratio={0.7}>
                  <div
                    className="w-full h-full relative"
                    onClick={() => handleClick(index)}
                  >
                    <Image
                      width="100%"
                      height="100%"
                      src={item.image}
                      fit="cover"
                    />
                    <div className="absolute bottom-[5px] left-[5px] flex flex-col">
                      <span
                        className="text-white text-[20px]"
                        style={{ zoom: 0.5 }}
                      >
                        {item.likeNumber}人点赞
                      </span>
                      <span
                        className="text-white text-[20px]"
                        style={{ zoom: 0.5 }}
                      >
                        播放{item.playNumber}次
                      </span>
                    </div>
                  </div>
                </AspectRatio>
              ))}
            </div>
          )}
        </ScrollLoadData>
      </PageMain>
    </PageWrapper>
  );
}
