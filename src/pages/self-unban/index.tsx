import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { UserInfo } from "@/components/work/user-info";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { UserUnbanInfoResult } from "@/type/user-unban-info-result";
import { Button } from "@/ui/button";
import { Divider } from "antd-mobile";
import { useAsyncValue } from "react-router-dom";

export default function SelfUnbanPage() {
  const { data } = useAsyncValue() as { data: UserUnbanInfoResult };
  const { data: userInfo } = useFullUserInfo();
  const { content = [], note = "", optTitle, canApply = false } = data;

  return (
    <PageWrapper>
      <NavigationBar canBack title="自助解封" />
      <PageMain>
        <div className="p-[15px]">
          <div className="flex flex-col gap-[10px]">
            <span className="text-sm">违规用户</span>
            {userInfo ? <UserInfo user={userInfo} size={50} /> : null}
          </div>
          <div className="mt-[10px]">
            <span className="text-sm">封禁内容</span>
            <div>
              {content.map((item) => (
                <div key={item.id}>{item.content ?? "未知"}</div>
              ))}
            </div>
          </div>
          <Divider className="border-divider my-[30px]" />
          <div>
            <span className="text-sm">解封规则</span>
            <div className="whitespace-pre-wrap">{note}</div>
          </div>
        </div>
        <div className="mb-[50px] px-[15px]">
          <Button
            variant="primary"
            size="lg"
            className="w-full"
            disabled={!canApply}
          >
            {optTitle}
          </Button>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
