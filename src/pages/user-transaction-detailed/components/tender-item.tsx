import { CommonIcon } from "@/components/common-icon";
import { Ellipsis } from "@/components/ellipsis";
import { ResourceContent } from "@/components/resource-content";
import { RichContent } from "@/components/rich-content";
import { UserInfo } from "@/components/work/user-info";
import { VoicePlayer } from "@/components/work/voice-player";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { userTransactionTenderConfirm } from "@/lib/api/transaction";
import { UserTransactionResult } from "@/type/user-transaction-result";
import { UserTransactionTenderResult } from "@/type/user-transaction-tender-result";
import { Button } from "@/ui/button";
import {
  UserTransactionState,
  UserTransactionTenderState,
} from "@/utils/enums";
import { formatTimeLine } from "@/utils/format-time-line";
import { useMutation } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import dayjs from "dayjs";

interface Props {
  transaction: UserTransactionResult;
  data: UserTransactionTenderResult;
}

export const TenderItem = ({ transaction, data }: Props) => {
  const { data: userInfo } = useFullUserInfo();

  const isSelf = userInfo?.id === transaction.user?.id;

  const { mutateAsync: tenderConfirm } = useMutation({
    mutationFn: userTransactionTenderConfirm,
  });

  const { navigateRoute } = useNavigateRoute();

  const handleTenderConfirm = async () => {
    Toast.show({
      content: "请稍后...",
      icon: "loading",
      duration: 0,
    });

    const { ok, data: resData } = await tenderConfirm({ id: data.id! });

    if (ok) {
      navigateRoute("/create-order", {
        id: resData?.id,
      });
    }
  };

  return (
    <div className="flex flex-col relative">
      <div className="flex">
        <div className="flex-1 w-0">
          <UserInfo user={data.user!} size={47} />
        </div>
        <span className="flex-none text-[13px] text-hint-color">
          {formatTimeLine(dayjs(data.cdate!).valueOf())}
        </span>
      </div>
      <div className="h-2" />
      {/* 语音相关 */}
      {data?.voice?.url ? (
        <div className="w-[200px]">
          <VoicePlayer
            url={data.voice.url}
            duration={data.voice.duration ?? 0}
          />
        </div>
      ) : null}
      {/* 文字内容 */}
      <div>
        <Ellipsis
          rows={2}
          expandText={<div className="text-[#9D64FF]">全文</div>}
          collapseText={<div className="text-[#9D64FF]">收起</div>}
          content={
            <RichContent
              content={data?.content ?? ""}
              builds={[
                "emailTextBuild",
                "ipTextBuild",
                "phoneTextBuild",
                "urlTextBuild",
              ]}
            />
          }
        />
      </div>
      {/* 图片 ｜ 视频 */}
      {data?.resources?.length ? (
        <ResourceContent resources={data.resources} />
      ) : null}
      <div className="h-[5px]" />
      <div className="flex items-center">
        <div className="flex-1">
          <span className="text-[#FF005C] text-base">￥{data.price || 0}</span>
        </div>
        {transaction.state === UserTransactionState.等待交易 && isSelf ? (
          <Button
            variant="primary"
            className="w-[83px] h-[30px] text-[13px]"
            onClick={handleTenderConfirm}
          >
            中标
          </Button>
        ) : null}
      </div>
      {data.state === UserTransactionTenderState.已采纳 ? (
        <div className="absolute top-[25px] right-[15px]">
          <CommonIcon name="userTransactionBid" w={66} h={50} />
        </div>
      ) : null}
    </div>
  );
};
