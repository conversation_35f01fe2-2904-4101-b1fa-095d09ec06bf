import { CommonIcon } from "@/components/common-icon";
import { Ellipsis } from "@/components/ellipsis";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ResourceContent } from "@/components/resource-content";
import { RichContent } from "@/components/rich-content";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { CommentInput, FinishType } from "@/components/work/comment-input";
import { CommentItem } from "@/components/work/comment-item";
import { UserInfo } from "@/components/work/user-info";
import { VoicePlayer } from "@/components/work/voice-player";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { useUploadQueueManager } from "@/hooks/use-upload-queue-manager";
import {
  userTransactionClose,
  userTransactionCommentPage,
  userTransactionCommentPublishCheck,
  userTransactionCommentPublishUrl,
  userTransactionCreateOrder,
  userTransactionGet,
  userTransactionTakeOff,
  userTransactionTakeOn,
  userTransactionTenderLike,
  userTransactionTenderPage,
} from "@/lib/api/transaction";
import { PageParams } from "@/type";
import { CommonCommentResult } from "@/type/common-comment-result";
import { getRandomParams } from "@/utils/common";
import {
  PayBusinessType,
  ReportTargetType,
  ShareTargetType,
  UserTransactionState,
  UserTransactionType,
  userTransactionTypeConfig,
} from "@/utils/enums";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Divider, Toast } from "antd-mobile";
import { ReactNode } from "react";
import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { Fragment } from "react/jsx-runtime";
import { TenderItem } from "./components/tender-item";
import { ArrowIcon, FavoriteIcon, MoreIcon } from "@/components/svg-icon";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useEffect } from "react";
import { Button } from "@/ui/button";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { usePay } from "@/hooks/use-pay";
import { SelectPopup } from "@/components/select-popup";
import { ShareModal } from "@/components/work/share-modal";
import { useModal } from "@/hooks/use-modal";

export default function UserTransactionDetailed() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id");

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["userTransactionGet", id],
    queryFn: () => userTransactionGet({ id: id! }),
  });

  const commentQueryKey = ["user-transaction", "comment", id];
  const commentQueryFn = (params: PageParams) => {
    return userTransactionCommentPage({ targetId: id! }, params);
  };

  const tenderQueryKey = ["user-transaction", "tender", id];
  const tenderQueryFn = (params: PageParams) => {
    return userTransactionTenderPage({ targetId: id! }, params);
  };

  const detail = data?.data;

  const [hasLike, setHasLike] = useState(false);

  const {
    open: openShare,
    openModal: openShareModal,
    closeModal: closeShareModal,
  } = useModal();

  useEffect(() => {
    setHasLike(detail?.hasLike ?? false);
  }, [detail?.hasLike]);

  const { mutateAsync: tenderLike } = useMutation({
    mutationFn: userTransactionTenderLike,
  });

  const handleLike = () => {
    tenderLike({ id: detail?.id!, like: !hasLike });
    setHasLike(!hasLike);
  };

  const { data: userInfo } = useFullUserInfo();
  const isSelf = userInfo?.id === detail?.user?.id;

  const [quote, setQuote] = useState<
    { id: string; content: ReactNode } | undefined
  >();
  const [commentMode, setCommentMode] = useState(false);

  const handleSetQuote = (quote: CommonCommentResult) => {
    setQuote({
      id: quote.id!,
      content: `回复：${quote?.user?.nickname} “${quote.voice ? "[语音]" : ""}${
        (quote.resources ?? []).some((r) => r.duration) ? "[视频]" : ""
      }${(quote.resources ?? []).some((r) => !r.duration) ? "[图片]" : ""}${
        quote.content
      }”`,
    });
  };
  const { handleRefresh } = usePageListRefresh(commentQueryKey);

  const userTransactionCommentPublishCheckMutation = useMutation({
    mutationFn: userTransactionCommentPublishCheck,
  });

  const { addToQueue } = useUploadQueueManager();

  const handleComment = async (data: FinishType) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await userTransactionCommentPublishCheckMutation.mutateAsync(
      {
        targetId: id!,
        content: data.text,
        ats: data.ats,
        voice: data.voice ? getRandomParams() : undefined,
        resources: data.resource
          ? data.resource.map(() => getRandomParams())
          : undefined,
        topId: quote?.id,
        parentId: quote?.id,
      }
    );

    if (ok) {
      addToQueue(
        {
          title: "交易评论",
          url: userTransactionCommentPublishUrl,
          params: {
            targetId: id!,
            content: data.text,
            ats: data.ats,
            voice: data.voice,
            resources: data.resource,
            topId: quote?.id,
            parentId: quote?.id,
          },
        },
        () => {
          handleRefresh();
        }
      );
    }
  };

  const { navigateRoute } = useNavigateRoute();

  const handleChat = () => {
    navigateRoute("/chat", {
      uid: detail?.user?.id,
    });
  };

  const { mutateAsync: createOrder } = useMutation({
    mutationFn: userTransactionCreateOrder,
  });

  const handleBuy = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });
    const { ok, data: resData } = await createOrder({ id: detail?.id! });

    if (ok && resData?.id) {
      navigateRoute("/create-order", {
        id: resData.id,
      });
    }
  };

  const handleTenderClick = () => {
    navigateRoute("/user-transaction-participate", {
      id: id!,
    });
  };

  const { mutateAsync: takeOn } = useMutation({
    mutationFn: userTransactionTakeOn,
  });

  const handleTakeOn = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });
    const { ok } = await takeOn({ id: detail?.id! });

    if (ok) {
      refetch();
    }
  };

  const handleReportClick = () => {
    navigateRoute("/report", {
      id: detail?.id!,
      type: ReportTargetType.Transaction,
    });
  };

  const { mutateAsync: takeOff } = useMutation({
    mutationFn: userTransactionTakeOff,
  });

  const handleTakeOff = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });
    const { ok } = await takeOff({ id: detail?.id! });

    if (ok) {
      refetch();
    }
  };

  const { mutateAsync: close } = useMutation({
    mutationFn: userTransactionClose,
  });

  const handleClose = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });
    const { ok } = await close({ id: detail?.id! });

    if (ok) {
      refetch();
    }
  };

  const { showPay } = usePay({
    type: PayBusinessType.UserTransactionDeposit,
    params: {
      id: detail?.id!,
    },
  });

  const handlePayDepositClick = () => {
    showPay();
  };

  const renderOerateArea = (
    <div className="w-full">
      {commentMode && detail?.type === UserTransactionType.出售 ? (
        <CommentInput
          quote={quote}
          onClearQuote={() => setQuote(undefined)}
          showGift={false}
          onFinish={handleComment}
          action={
            <div
              className="w-[43px] h-[43px] rounded-full flex items-center justify-center bg-scaffold-background"
              onClick={() => setCommentMode(false)}
            >
              <ArrowIcon size={24} className="rotate-180" />
            </div>
          }
        />
      ) : null}
      {!commentMode &&
      detail?.type === UserTransactionType.出售 &&
      !isSelf &&
      detail.state === UserTransactionState.等待交易 ? (
        <div className="w-full flex items-center px-[15px] py-[7px]">
          <div className="flex items-center gap-[5px]" onClick={handleLike}>
            <FavoriteIcon size={20} color={hasLike ? "#FF5252" : "#babcbe"} />
            <span className="text-hint-color text-[15px]">想要</span>
          </div>
          <div className="flex-1" />
          <Button
            variant="secondary"
            className="h-[40px] w-[70px] text-[15px]"
            onClick={() => setCommentMode(true)}
          >
            评论
          </Button>
          <div className="w-[10px]" />
          <Button
            variant="secondary"
            className="h-[40px] w-[80px] text-[15px]"
            onClick={handleChat}
          >
            聊一聊
          </Button>
          <div className="w-[10px]" />
          <Button
            variant="primary"
            className="h-[40px] w-[90px] text-[15px]"
            onClick={handleBuy}
          >
            立即购买
          </Button>
        </div>
      ) : null}
      {!commentMode &&
      detail?.type === UserTransactionType.求购 &&
      !isSelf &&
      detail.state === UserTransactionState.等待交易 ? (
        <div className="w-full flex items-center px-[15px] py-[7px]">
          <Button
            variant="secondary"
            className="h-[40px] flex-1 text-[15px]"
            onClick={handleChat}
          >
            聊一聊
          </Button>
          <div className="w-[30px]" />
          <Button
            variant="primary"
            className="h-[40px] flex-1 text-[15px]"
            onClick={handleTenderClick}
          >
            投标
          </Button>
        </div>
      ) : null}
      {!commentMode && isSelf ? (
        <div className="w-full flex items-center px-[15px] py-[7px]">
          {detail?.type === UserTransactionType.出售 ? (
            <div className="flex-1 pr-[10px]">
              <Button
                variant="primary"
                className="h-[40px] w-[70px] text-[15px]"
                onClick={() => setCommentMode(true)}
              >
                评论
              </Button>
            </div>
          ) : null}
          {detail?.state === UserTransactionState.等待支付保证金 ? (
            <Button
              variant="primary"
              className="h-[40px] flex-1 text-[15px]"
              onClick={handlePayDepositClick}
            >
              支付保证金
            </Button>
          ) : null}
          {detail?.state === UserTransactionState.已下架 ? (
            <Button
              variant="primary"
              className="h-[40px] flex-1 text-[15px]"
              onClick={handleTakeOn}
            >
              上架
            </Button>
          ) : null}
          {detail?.state === UserTransactionState.等待交易 ? (
            <Button
              variant="secondary"
              className="h-[40px] flex-1 text-[15px]"
              onClick={handleTakeOff}
            >
              下架
            </Button>
          ) : null}
          {detail?.state === UserTransactionState.等待支付保证金 ||
          detail?.state === UserTransactionState.已下架 ? (
            <div className="pl-[30px] flex-1">
              <Button
                variant="secondary"
                className="h-[40px] w-full text-[15px]"
                onClick={handleClose}
              >
                关闭
              </Button>
            </div>
          ) : null}
        </div>
      ) : null}
      {detail?.state === UserTransactionState.已完成 ? (
        <div className="w-full flex justify-center">
          <span>交易已完成</span>
        </div>
      ) : null}
      {detail?.state === UserTransactionState.已关闭 ? (
        <div className="w-full flex justify-center">
          <span>交易已关闭</span>
        </div>
      ) : null}
      {detail?.state === UserTransactionState.交易中 ? (
        <div className="w-full flex justify-center">
          <span>交易中</span>
        </div>
      ) : null}
    </div>
  );

  return (
    <PageWrapper>
      <NavigationBar
        canBack
        title={`${
          detail?.type ? userTransactionTypeConfig[detail.type] : ""
        }详细`}
        action={
          <SelectPopup
            options={[
              {
                title: "分享",
                value: "share",
                onClick: () => {
                  openShareModal();
                },
              },
              ...(!isSelf
                ? [
                    {
                      title: "举报",
                      value: "report",
                      onClick: () => {
                        handleReportClick();
                      },
                    },
                  ]
                : []),
            ]}
          >
            <MoreIcon size={30} />
          </SelectPopup>
        }
      />
      <PageMain
        isLoading={isLoading}
        isEmpty={!detail}
        extra={renderOerateArea}
      >
        <div className="p-[15px] flex flex-col">
          <UserInfo user={detail?.user!} size={47} />
          <div className="h-[25px]" />
          <div className="flex gap-[5px] items-center">
            <span className="text-[#FF005C] text-[21px]">
              {detail?.type === UserTransactionType.求购 ? "预算" : ""} ¥{" "}
              {detail?.price || 0}
            </span>
            {detail?.deposit ? (
              <span className=" px-[10px] py-[2px] rounded-[10px] bg-green-600/80 text-white text-xs">
                保证金: {detail.deposit}
              </span>
            ) : null}
          </div>
          <div className="h-[10px]" />
          <div className="flex justify-between">
            <span className="text-xs text-hint-color">
              {detail?.type === UserTransactionType.出售
                ? `${detail?.likeNumber}人想要 · `
                : `${detail?.commentNumber}次投标 · `}
              {detail?.readNumber}浏览
            </span>
            <span className="flex gap-[5px] items-center">
              <CommonIcon name="location" w={12} h={12} />
              <span className="text-xs">{detail?.address ?? "未知"}</span>
            </span>
          </div>
          <div className="h-[10px]" />
          {/* 语音相关 */}
          {detail?.voice?.url ? (
            <div className="w-[200px]">
              <VoicePlayer
                url={detail.voice.url}
                duration={detail.voice.duration ?? 0}
              />
            </div>
          ) : null}
          {/* 文字内容 */}
          <div>
            <Ellipsis
              rows={2}
              expandText={<div className="text-[#9D64FF]">全文</div>}
              collapseText={<div className="text-[#9D64FF]">收起</div>}
              content={
                <RichContent content={detail?.content ?? ""} builds={[]} />
              }
            />
          </div>
          {/* 图片 ｜ 视频 */}
          {detail?.resources?.length ? (
            <ResourceContent resources={detail.resources} />
          ) : null}
          <div className="h-[20px]" />
          <div className="py-[21px] text-base">
            全部{detail?.type == UserTransactionType.求购 ? "投标" : "评论"}(
            {detail?.commentNumber})
          </div>
          {detail?.type === UserTransactionType.出售 ? (
            <ScrollLoadData queryKey={commentQueryKey} queryFn={commentQueryFn}>
              {(comments) => (
                <div className="flex flex-col pt-5">
                  {comments.map((item, index) => (
                    <Fragment key={item.id}>
                      <CommentItem
                        type="user-transaction"
                        id={detail?.id!}
                        data={item}
                        onSetQuote={handleSetQuote}
                      />
                      {index !== comments.length - 1 ? (
                        <Divider className="border-divider" />
                      ) : null}
                    </Fragment>
                  ))}
                </div>
              )}
            </ScrollLoadData>
          ) : null}
          {detail?.type === UserTransactionType.求购 ? (
            <ScrollLoadData queryKey={tenderQueryKey} queryFn={tenderQueryFn}>
              {(tenders) => (
                <div className="flex flex-col pt-5">
                  {tenders.map((item, index) => (
                    <Fragment key={item.id}>
                      <TenderItem transaction={detail} data={item} />
                      {index !== tenders.length - 1 ? (
                        <Divider className="border-divider" />
                      ) : null}
                    </Fragment>
                  ))}
                </div>
              )}
            </ScrollLoadData>
          ) : null}
        </div>
      </PageMain>
      <ShareModal
        type={ShareTargetType.Transaction}
        id={detail?.id!}
        open={openShare}
        onClose={closeShareModal}
      />
    </PageWrapper>
  );
}
