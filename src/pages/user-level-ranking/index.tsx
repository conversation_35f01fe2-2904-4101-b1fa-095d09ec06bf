import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { Image } from "antd-mobile";
import { userLevelRankingBackground } from "@/components/image-list";
import { Tabs } from "@/components/work/tabs";
import { useAsyncValue } from "react-router-dom";
import { LayoutConfigUserLevelRanking } from "@/type/layout-config";
import { useState } from "react";
import { TabContent } from "./components/tab-content";
import { TipsAndUpdatesIcon } from "@/components/svg-icon";
import { tipsDialog } from "@/components/work/tips-dialog";
import { useConfigList } from "@/hooks/use-config-list";

export default function UserLevelRanking() {
  const { data } = useAsyncValue() as { data: LayoutConfigUserLevelRanking };

  const tabs = data.tabs.items;

  const [activeTab, setActiveTab] = useState(data.tabs.defaultIndex);

  const { getSystemConfig } = useConfigList();

  const handleActionClick = () => {
    tipsDialog.success({
      title: "提示信息",
      content: (
        <div className="text-left whitespace-pre-wrap">
          {getSystemConfig("USER_LEVEL_RANKING_TIPS")}
        </div>
      ),
      confirmText: "确定",
    });
  };

  return (
    <PageWrapper className="relative z-10">
      <NavigationBar
        canBack
        className="bg-transparent"
        content={<Tabs tabs={tabs} value={activeTab} onChange={setActiveTab} />}
        action={
          <span onClick={handleActionClick}>
            <TipsAndUpdatesIcon size={24} />
          </span>
        }
      />
      <PageMain needScroll={false}>
        <div className="h-full p-[15px]">
          {tabs.map((tab, index) => (
            <TabContent
              key={index}
              isActive={activeTab === index}
              tabs={tab.tabs}
            />
          ))}
        </div>
      </PageMain>
      <div className="absolute top-0 left-0 w-full h-full z-[-1]">
        <Image
          src={userLevelRankingBackground}
          width="100%"
          height="100%"
          fit="cover"
        />
      </div>
    </PageWrapper>
  );
}
