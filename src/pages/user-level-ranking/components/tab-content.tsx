import { FilterTabs } from "@/components/work/filter-tabs";
import { BaseUrlConfig } from "@/type/layout-config";
import { useEffect, useState } from "react";
import { SubContent } from "./sub-content";
import classNames from "classnames";

interface Props {
  isActive: boolean;
  tabs: {
    defaultIndex: number;
    items: BaseUrlConfig[];
  };
}

export const TabContent = (props: Props) => {
  const { isActive, tabs } = props;

  const items = tabs.items.map((item, index) => ({
    ...item,
    key: index,
  }));

  const [isInit, setIsInit] = useState(false);

  const [value, setValue] = useState(tabs.defaultIndex ?? 0);

  useEffect(() => {
    if (isActive) {
      setIsInit(true);
    }
  }, [isActive]);

  if (!isInit) {
    return null;
  }

  return (
    <div
      className={classNames("flex flex-col", isActive ? "h-full" : "hidden")}
    >
      <div className="w-full pb-[15px]">
        <FilterTabs tabs={items} value={value} onChange={setValue} />
      </div>
      <div className="flex-1 h-0">
        {tabs.items.map((item, index) => (
          <SubContent
            key={index}
            isActive={value === index}
            index={index}
            config={item}
          />
        ))}
      </div>
    </div>
  );
};
