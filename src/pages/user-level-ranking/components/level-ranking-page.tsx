import { CommonIcon } from "@/components/common-icon";
import { UserInfo } from "@/components/work/user-info";
import {
  UserInfoModal,
  UserInfoModalRef,
} from "@/components/work/user-info-modal";
import { fetch } from "@/lib/request";
import { BaseUrlConfig } from "@/type/layout-config";
import { User } from "@/type/user";
import { ScrollArea } from "@/ui/scroll-area";
import { useQuery } from "@tanstack/react-query";
import { Image } from "antd-mobile";
import { useRef } from "react";

interface Props {
  index: number;
  config: BaseUrlConfig;
}

export const LevelRankingPage = (props: Props) => {
  const { index, config } = props;

  const userInfoModalRef = useRef<UserInfoModalRef>(null);

  const openUserInfoModal = (id: string) => {
    userInfoModalRef.current?.open(id);
  };

  const { data } = useQuery({
    queryKey: ["user-level-ranking", index, config.url, config.params],
    queryFn: () =>
      fetch.post<{ score: number; user: User }[]>(
        `/api${config.url}`,
        config.params
      ),
  });

  const [first, second, third, ...restData] = data?.data ?? [];

  const handleOpenModal = (id: string) => {
    openUserInfoModal(id);
  };

  return (
    <ScrollArea className="h-full">
      <div className="flex flex-col gap-5">
        <div className="w-full flex items-end">
          <div className="bg-white flex-1 h-[140px] rounded-t-[10px] shadow-lg flex flex-col gap-[10px] justify-center items-center">
            {second ? (
              <div
                className="flex flex-col gap-[10px] w-full h-full items-center justify-end"
                onClick={() => handleOpenModal(second?.user.id!)}
              >
                <div className="relative">
                  <div className="w-[56px] h-[56px] border-[2px] border-solid border-[#C9E4FF] rounded-full">
                    <Image
                      src={second?.user.avatarUrl!}
                      width="100%"
                      height="100%"
                      className="rounded-full"
                    />
                  </div>
                  <span className="absolute top-[-15px] left-[8px]">
                    <CommonIcon name="ranking2" w={43} h={32} />
                  </span>
                </div>
                <div className="flex flex-col mb-3">
                  <span className="text-center text-[#FF637D] line-clamp-1 text-[15px]">
                    {second?.user.nickname}
                  </span>
                  <span className="text-center text-[#666] text-xs">
                    {second?.score}
                  </span>
                </div>
              </div>
            ) : (
              <span className="text-[#666]">无人上榜</span>
            )}
          </div>
          <div className="bg-white flex-1 h-[155px] rounded-t-[10px] shadow-lg flex flex-col gap-[10px] justify-center items-center">
            {first ? (
              <div
                className="flex flex-col gap-[20px] items-center w-full h-full justify-end"
                onClick={() => handleOpenModal(first?.user.id!)}
              >
                <div className="relative">
                  <div className="w-[56px] h-[56px] border-[2px] border-solid border-[#FFE280] rounded-full">
                    <Image
                      src={first?.user.avatarUrl!}
                      width="100%"
                      height="100%"
                      className="rounded-full"
                    />
                  </div>
                  <span className="absolute top-[-15px] left-[8px]">
                    <CommonIcon name="ranking1" w={43} h={32} />
                  </span>
                </div>
                <div className="flex flex-col mb-3">
                  <span className="text-center text-[#FF637D] line-clamp-1 text-[15px]">
                    {first?.user.nickname}
                  </span>
                  <span className="text-center text-[#666] text-xs">
                    {first?.score}
                  </span>
                </div>
              </div>
            ) : (
              <span className="text-[#666]">无人上榜</span>
            )}
          </div>
          <div className="bg-white flex-1 h-[140px] rounded-t-[10px] shadow-lg flex flex-col gap-[10px] justify-center items-center">
            {third ? (
              <div
                className="flex flex-col gap-[10px] w-full h-full items-center justify-end"
                onClick={() => handleOpenModal(third?.user.id!)}
              >
                <div className="relative">
                  <div className="w-[56px] h-[56px] border-[2px] border-solid border-[#F7BF8E] rounded-full">
                    <Image
                      src={third?.user.avatarUrl!}
                      width="100%"
                      height="100%"
                      className="rounded-full"
                    />
                  </div>
                  <span className="absolute top-[-15px] left-[8px]">
                    <CommonIcon name="ranking3" w={43} h={32} />
                  </span>
                </div>
                <div className="flex flex-col mb-3">
                  <span className="text-center text-[#FF637D] line-clamp-1 text-[15px]">
                    {third?.user.nickname}
                  </span>
                  <span className="text-center text-[#666] text-xs">
                    {third?.score}
                  </span>
                </div>
              </div>
            ) : (
              <span className="text-[#666]">无人上榜</span>
            )}
          </div>
        </div>
        {restData.length ? (
          <div className="bg-white rounded-[10px] px-[15px]">
            {restData.map((item, index) => (
              <div className="flex items-center gap-[15px] text-black py-[20px]">
                <span className="text-black text-xl">{index + 4}</span>
                <div className="flex-1">
                  <UserInfo user={item.user} size={41} />
                </div>
                <span className="text-[#666] text-xs">{item.score}</span>
              </div>
            ))}
          </div>
        ) : null}
        <UserInfoModal ref={userInfoModalRef} />
      </div>
    </ScrollArea>
  );
};
