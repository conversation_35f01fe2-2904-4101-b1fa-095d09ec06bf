import { BaseUrlConfig } from "@/type/layout-config";
import { useEffect, useState } from "react";
import { LevelRankingPage } from "./level-ranking-page";

interface Props {
  isActive: boolean;
  index: number;
  config: BaseUrlConfig;
}

export const SubContent = (props: Props) => {
  const { isActive, index, config } = props;

  const [isInit, setIsInit] = useState(false);

  useEffect(() => {
    if (isActive) {
      setIsInit(true);
    }
  }, [isActive]);

  if (!isInit) {
    return null;
  }

  return (
    <div className={isActive ? "h-full" : "hidden"}>
      <LevelRankingPage index={index} config={config} />
    </div>
  );
};
