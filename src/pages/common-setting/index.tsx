import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ArrowIcon } from "@/components/svg-icon";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { type Theme, ThemeProviderContext } from "@/provider/theme-provider";
import { Divider, Popup } from "antd-mobile";
import { Fragment, useContext, useMemo, useState } from "react";

const themeList = [
  {
    label: "跟随系统",
    value: "system",
  },
  {
    label: "浅色模式",
    value: "light",
  },
  {
    label: "深色模式",
    value: "dark",
  },
];

export default function CommonSettingPage() {
  const [visible, setVisible] = useState(false);

  const { theme, setTheme } = useContext(ThemeProviderContext);
  const { navigateRoute } = useNavigateRoute();

  const themeLabel = useMemo(() => {
    return themeList.find((item) => item.value === theme)?.label;
  }, [theme]);

  const handleSelectTheme = (value: Theme) => {
    setTheme(value);
    setVisible(false);
  };

  const handleNavigateBeautySetting = () => {
    navigateRoute("/beauty-setting");
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="通用设置" />
      <PageMain>
        <div className="flex flex-col p-[15px]">
          <div
            className="flex justify-between py-[10px]"
            onClick={() => setVisible(true)}
          >
            <span className="text-[15px]">系统主题</span>
            <div className="flex items-center gap-[5px]">
              <span className="text-[13px]">{themeLabel}</span>
              <ArrowIcon />
            </div>
          </div>
          <div
            className="flex justify-between py-[10px]"
            onClick={handleNavigateBeautySetting}
          >
            <span className="text-[15px]">美颜设置</span>
            <div className="flex items-center gap-[5px]">
              <ArrowIcon />
            </div>
          </div>
        </div>
      </PageMain>
      <Popup
        visible={visible}
        bodyClassName="bg-transparent"
        onClose={() => setVisible(false)}
      >
        <div className="flex flex-col gap-[10px] mx-[15px]">
          <div className="flex flex-col rounded-[10px] bg-app-bar-background">
            {themeList.map((item, index) => (
              <Fragment key={item.value}>
                <div
                  className="py-[15px] text-center text-base text-[#9d64ff]"
                  onClick={() => handleSelectTheme(item.value as Theme)}
                >
                  {item.label}
                </div>
                {index !== themeList.length - 1 ? (
                  <Divider className="border-divider m-0" />
                ) : null}
              </Fragment>
            ))}
          </div>
          <div
            className="mb-[10px] py-[15px] text-center text-base text-[#9d64ff] bg-hint-color rounded-[10px]"
            onClick={() => setVisible(false)}
          >
            取消
          </div>
        </div>
      </Popup>
    </PageWrapper>
  );
}
