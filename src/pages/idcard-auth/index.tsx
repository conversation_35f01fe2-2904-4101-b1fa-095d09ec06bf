import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { UserIdcardCertifiedResult } from "@/type/user-idcard-certified-result";
import { useAsyncValue, useRevalidator } from "react-router-dom";
import { AuthContent } from "./components/auth-content";
import { queryClient } from "@/provider/query-client";
import { IdcardAuthQueryKey } from "./loader";
import { Image } from "antd-mobile";
import { idcardAuthSuccess } from "@/components/image-list";

export default function IdcardAuth() {
  const { data } = useAsyncValue() as { data?: UserIdcardCertifiedResult };
  const revalidator = useRevalidator();

  const onRefresh = () => {
    queryClient.invalidateQueries({ queryKey: IdcardAuthQueryKey });
    revalidator.revalidate();
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="实名认证" />
      <PageMain>
        <div className="p-[15px]">
          {data ? (
            <div className="pt-[100px]">
              <div className="flex flex-col gap-[15px] items-center">
                <div className="w-[200px]">
                  <Image
                    src={idcardAuthSuccess}
                    width="100%"
                    height="auto"
                    fit="cover"
                    placeholder={null}
                    fallback={null}
                  />
                </div>
                <div className="flex flex-col gap-[5px]">
                  <span>真实姓名：{data.name}</span>
                  <span>身份证号：{data.idcard}</span>
                  <span>手机号码：{data.phone}</span>
                </div>
              </div>
            </div>
          ) : (
            <AuthContent onRefresh={onRefresh} />
          )}
        </div>
      </PageMain>
    </PageWrapper>
  );
}
