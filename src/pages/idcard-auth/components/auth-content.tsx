import { useCountDownTimer } from "@/hooks/use-count-down-timer";
import { smsSendCode } from "@/lib/api/message";
import { certifiedIdcard } from "@/lib/api/user";
import { Button } from "@/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/ui/form";
import { Input } from "@/ui/input";
import { extractLeadingNumbers } from "@/utils";
import { idcardRegex, phoneRegex } from "@/utils/constant";
import { useMutation } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import { useForm } from "react-hook-form";

interface Props {
  onRefresh: () => void;
}

export const AuthContent = ({ onRefresh }: Props) => {
  const { isRunning, seconds, handleStart } = useCountDownTimer(60);
  const sendCodeMutation = useMutation({ mutationFn: smsSendCode });
  const certifiedIdcardMutation = useMutation({
    mutationFn: certifiedIdcard,
  });

  const checkPhone = async () => {
    const { phone } = form.getValues();

    if (!phone) {
      Toast.show({ content: "请输入手机号" });
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(phone)) {
      Toast.show({ content: "请输入正确的手机号" });
      return;
    }
    // 发送验证码
    Toast.show({
      icon: "loading",
      content: "加载中…",
      duration: 0,
    });

    const { ok, msg } = await sendCodeMutation.mutateAsync(phone);

    if (ok) {
      handleStart();
    } else {
      Toast.show({ content: msg });
    }
  };

  const form = useForm();

  const handleSubmit = async () => {
    const { name, idcard, phone, code } = form.getValues();

    if (!name || name.length < 2) {
      Toast.show({ content: "请输入真实姓名" });
      return;
    }

    if (!idcardRegex.test(idcard)) {
      Toast.show({ content: "请输入正确的身份证号码" });
      return;
    }

    if (!phoneRegex.test(phone)) {
      Toast.show({ content: "请输入正确的手机号" });
      return;
    }

    if (!code) {
      Toast.show({ content: "请输入验证码" });
      return;
    }

    Toast.show({
      icon: "loading",
      content: "加载中…",
      duration: 0,
    });

    const { ok } = await certifiedIdcardMutation.mutateAsync({
      name,
      idcard,
      phone,
      code,
    });

    if (ok) {
      onRefresh();
    }
  };

  return (
    <Form {...form}>
      <div className="flex flex-col gap-5">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => {
            return (
              <FormItem>
                <FormLabel>真实姓名</FormLabel>
                <FormControl>
                  <Input
                    placeholder="请输入真实姓名"
                    {...field}
                    autoComplete="off"
                  />
                </FormControl>
              </FormItem>
            );
          }}
        />
        <FormField
          control={form.control}
          name="idcard"
          render={({ field }) => {
            return (
              <FormItem>
                <FormLabel>身份证号码</FormLabel>
                <FormControl>
                  <Input
                    placeholder="请输入身份证号码"
                    {...field}
                    autoComplete="off"
                  />
                </FormControl>
              </FormItem>
            );
          }}
        />
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>手机号</FormLabel>
              <FormControl>
                <div className="flex items-center relative z-0">
                  <span className="absolute ml-[5px] z-[-1]">+86</span>
                  <Input
                    placeholder="请输入手机号码"
                    className="pl-[45px]"
                    maxLength={11}
                    {...field}
                    autoComplete="off"
                    value={extractLeadingNumbers(field.value)}
                  />
                  {!isRunning ? (
                    <Button
                      variant="primary"
                      size="sm"
                      className="absolute right-0"
                      onClick={checkPhone}
                    >
                      发送
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      variant="secondary"
                      className="absolute right-0"
                    >
                      {seconds}
                    </Button>
                  )}
                </div>
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="code"
          render={({ field }) => (
            <FormItem>
              <FormLabel>验证码</FormLabel>
              <FormControl>
                <Input
                  placeholder="请输入验证码"
                  maxLength={9}
                  {...field}
                  autoComplete="off"
                  value={extractLeadingNumbers(field.value)}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <Button
          type="submit"
          variant="primary"
          size="lg"
          onClick={handleSubmit}
        >
          提交认证
        </Button>
      </div>
    </Form>
  );
};
