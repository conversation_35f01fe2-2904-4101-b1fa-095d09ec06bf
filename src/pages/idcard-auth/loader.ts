import { getIdcardAuth } from "@/lib/api/user";
import { queryClient } from "@/provider/query-client";
import { defer, LoaderFunction } from "react-router-dom";

export const IdcardAuthQueryKey = ["id-card-auth"];

export const loader: LoaderFunction = async () => {
  return defer({
    data: queryClient
      .fetchQuery({
        queryKey: IdcardAuthQueryKey,
        queryFn: getIdcardAuth,
      })
      .catch((err) => err),
  });
};
