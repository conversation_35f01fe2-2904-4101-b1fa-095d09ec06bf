import { DefaultLoadImage } from "@/components/default-load-image";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { DynamicTopic } from "@/type/dynamic-topic";
import { AspectRatio } from "@/ui/aspect-ratio";
import { Image } from "antd-mobile";

interface Props {
  data: DynamicTopic;
}

export const TabItem = (props: Props) => {
  const { data } = props;

  const { navigateRoute } = useNavigateRoute();

  const handleClick = () => {
    navigateRoute("/topic-detail", {
      id: data.id,
      name: data.name,
    });
  };

  return (
    <div
      className="flex p-[14px] rounded-[5px] bg-app-bar-background"
      onClick={handleClick}
    >
      <div className="flex-1 w-0">
        <span className="line-clamp-1 text-base">#{data.name}</span>
        <span className="line-clamp-1 mb-[5px] text-hint-color text-[13px]">
          {data.note ?? "暂无话题描述~"}
        </span>
        <span className="text-xs text-[#9F89C7]">
          {data.dynamicCount ?? 0}动态
        </span>
      </div>
      {data.cover ? (
        <div className="w-[72px]">
          <AspectRatio ratio={1}>
            <Image
              src={data.cover}
              width="100%"
              height="100%"
              fit="cover"
              className="rounded-[10px]"
              lazy
              placeholder={<DefaultLoadImage />}
              fallback={<DefaultLoadImage />}
              onLoad={() => {}}
            />
          </AspectRatio>
        </div>
      ) : null}
    </div>
  );
};
