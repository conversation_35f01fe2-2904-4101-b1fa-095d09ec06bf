import { ScrollLoadData } from "@/components/scroll-load-data";
import { getLayoutPageList } from "@/lib/api/layout";
import { PageParams } from "@/type";
import { DynamicTopic } from "@/type/dynamic-topic";
import { BaseUrlConfig } from "@/type/layout-config";
import { useEffect, useState } from "react";
import { TabItem } from "./tab-item";
import { PullToRefresh } from "@/components/pull-to-refresh";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { ScrollArea } from "@/ui/scroll-area";

interface Props {
  isActive: boolean;
  data: BaseUrlConfig;
  searchParams?: Record<string, any>;
}

export const TabContent = (props: Props) => {
  const { isActive, data, searchParams } = props;

  const [isInit, setIsInit] = useState(false);

  const queryKey = [data.url, data.params, searchParams];
  const queryFn = (params: PageParams) => {
    return getLayoutPageList<DynamicTopic[]>({
      url: data.url,
      params,
      data: { ...data.params, ...searchParams },
    });
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  useEffect(() => {
    if (isActive) {
      setIsInit(true);
    }
  }, [isActive]);

  if (!isInit) {
    return null;
  }

  return (
    <ScrollArea className={isActive ? "h-full" : "hidden"}>
      <PullToRefresh onRefresh={handleRefresh}>
        <ScrollLoadData size={10} queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="flex flex-col w-full gap-5">
              {data.map((item) => (
                <TabItem key={item.id} data={item} />
              ))}
            </div>
          )}
        </ScrollLoadData>
      </PullToRefresh>
    </ScrollArea>
  );
};
