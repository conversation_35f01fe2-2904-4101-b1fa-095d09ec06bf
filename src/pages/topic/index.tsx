import { NavigationBar } from "@/components/navigation-bar";
import { PageWrapper } from "@/components/page-wrapper";
import { Tabs } from "@/components/work/tabs";
import { LayoutConfigTopic } from "@/type/layout-config";
import { ScrollA<PERSON>, ScrollBar } from "@/ui/scroll-area";
import { useState } from "react";
import { useAsyncValue } from "react-router-dom";
import { TabContent } from "./components/tab-content";
import { PageMain } from "@/components/page-main";
import { NavigationSearch } from "@/components/work/navigation-search";

export default function Topic() {
  const { data } = useAsyncValue() as { data: LayoutConfigTopic };

  const defaultIndex = data.tabs.defaultIndex ?? -1;
  const tabs = data.tabs.items;
  const search = data.search;

  const [activeKey, setActiveKey] = useState(defaultIndex);
  const [searchParams, setSearchParams] = useState({ name: "" });

  return (
    <PageWrapper>
      <NavigationBar
        canBack
        content={
          search ? (
            <NavigationSearch
              placeholder="请输入话题名称"
              name="name"
              onFinish={setSearchParams}
            />
          ) : null
        }
        title="话题列表"
      />
      <div className="flex flex-col flex-1 h-0 px-[15px] py-2 bg-scaffold-background">
        <ScrollArea>
          <Tabs tabs={tabs} value={activeKey} onChange={setActiveKey} />
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
        <PageMain needScroll={false}>
          {tabs.map((tab, index) => (
            <TabContent
              key={index}
              isActive={index === activeKey}
              searchParams={searchParams}
              data={tab}
            />
          ))}
        </PageMain>
      </div>
    </PageWrapper>
  );
}
