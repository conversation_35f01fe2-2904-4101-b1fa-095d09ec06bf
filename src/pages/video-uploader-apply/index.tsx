import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { useConfigList } from "@/hooks/use-config-list";
import { Button } from "@/ui/button";
import { Textarea } from "@/ui/textarea";
import { useState } from "react";

export default function VideoUploaderApply() {
  const { getSystemConfig } = useConfigList();

  const [applyNote, setApplyNote] = useState("");

  const videoUploaderApplyNote = getSystemConfig("VIDEO_UPLOADER_APPLY_NOTE");

  return (
    <PageWrapper>
      <NavigationBar canBack title="UP主申请" />
      <PageMain>
        <div className="flex flex-col gap-[20px]">
          <Textarea
            rows={5}
            placeholder="请输入申请理由"
            className="px-[15px] py-[10px] bg-scaffold-background resize-none border-none"
            value={applyNote}
            onChange={(e) => setApplyNote(e.target.value)}
          />
          <div className="px-[15px]">
            <Button variant="primary" className="w-full text-sm">
              提交申请
            </Button>
          </div>
          <div className="px-[15px] whitespace-pre-wrap">
            {videoUploaderApplyNote}
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
