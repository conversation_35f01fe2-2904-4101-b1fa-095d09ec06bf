import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import {
  JoinSpecialList,
  joinSpecialListQueryKey,
} from "./components/join-special-list";

export default function JoinSpecialListPage() {
  const { handleRefresh } = usePageListRefresh(joinSpecialListQueryKey);

  return (
    <PageWrapper>
      <NavigationBar canBack title="我的入场名片" />
      <PageMain
        className="p-[15px] bg-scaffold-background"
        onRefresh={handleRefresh}
      >
        <JoinSpecialList />
      </PageMain>
    </PageWrapper>
  );
}
