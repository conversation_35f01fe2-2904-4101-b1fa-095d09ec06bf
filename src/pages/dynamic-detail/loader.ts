import { defer, LoaderFunction, LoaderFunctionArgs } from "react-router-dom";
import { dynamicGet } from "@/lib/api/dynamic";
import { queryClient } from "@/provider/query-client";
import { parseUrl } from "@/utils/parse-url";

export const loader: LoaderFunction = ({ request }: LoaderFunctionArgs) => {
  const { searchParams } = parseUrl(request.url);

  const id = searchParams.get("id");

  const queryKey = ["dynamic", "detail", id];

  return defer({
    data: queryClient
      .fetchQuery({
        queryKey,
        queryFn: () => dynamicGet({ id: id! }),
      })
      .catch((err) => err),
  });
};
