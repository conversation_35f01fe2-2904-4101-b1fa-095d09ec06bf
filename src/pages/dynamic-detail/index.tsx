import { NavigationBar } from "@/components/navigation-bar";
import { DynamicItem } from "@/components/work/dynamic-item";
import { GiftContent } from "./components/gift-content";
import { CommentItem } from "@/components/work/comment-item";
import {
  dynamicCommentPage,
  dynamicCommentPublishCheck,
  dynamicCommentPublishUrl,
  dynamicDelete,
  dynamicGiveGift,
} from "@/lib/api/dynamic";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { PageParams } from "@/type";
import {
  useAsyncValue,
  useRevalidator,
  useSearchParams,
} from "react-router-dom";
import { DynamicResult } from "@/type/dynamic-result";
import { ApiResponse } from "@/lib/request";
import { PageWrapper } from "@/components/page-wrapper";
import { PageMain } from "@/components/page-main";
import { Fragment } from "react/jsx-runtime";
import { Dialog, Divider, Toast } from "antd-mobile";
import { CommentInput, FinishType } from "@/components/work/comment-input";
import { GiftResult } from "@/type/gift-result";
import { ReactNode, useState } from "react";
import { CommonCommentResult } from "@/type/common-comment-result";
import { useUploadQueueManager } from "@/hooks/use-upload-queue-manager";
import { getRandomParams } from "@/utils/common";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { SelectPopup } from "@/components/select-popup";
import { MoreIcon } from "@/components/svg-icon";
import { ReportTargetType } from "@/utils/enums";

export default function DynamicDetail() {
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id") || undefined;

  const { data } = useAsyncValue() as ApiResponse<DynamicResult>;

  const { revalidate } = useRevalidator();

  const queryClient = useQueryClient();

  const { navigateRoute, navigateBack } = useNavigateRoute();
  const { data: userInfo } = useFullUserInfo();

  const isSelf = userInfo?.id === data?.user?.id;

  const [quote, setQuote] = useState<
    { id: string; content: ReactNode } | undefined
  >();

  const commentQueryKey = ["dynamic", "comment", id];
  const commentQueryFn = (params: PageParams) => {
    return dynamicCommentPage({ targetId: id! }, params);
  };

  const handleRefresh = async () => {
    // 刷新列表
    revalidate();
    // 重置评论列表
    queryClient.resetQueries({ queryKey: commentQueryKey });
  };

  const dynamicGiveGiftMutations = useMutation({
    mutationFn: dynamicGiveGift,
  });

  const dynamicDeleteMutations = useMutation({
    mutationFn: dynamicDelete,
  });

  const handleReportClick = () => {
    navigateRoute("/report", {
      id: data?.id!,
      type: ReportTargetType.Dynamic,
    });
  };

  const handleDeleteClick = () => {
    Dialog.confirm({
      title: "提示",
      closeOnMaskClick: true,
      bodyClassName: "bg-scaffold-background",
      content: (
        <div className="text-center text-foreground">
          是否删除该动态，删除后不可恢复!
        </div>
      ),
      confirmText: <span className="text-[#FF3460]">删除</span>,
      cancelText: <span className="text-foreground">取消</span>,
      onConfirm: async () => {
        Toast.show({
          icon: "loading",
          content: "请稍后...",
          duration: 0,
        });

        const { ok } = await dynamicDeleteMutations.mutateAsync({
          id: id!,
        });

        if (ok) {
          Toast.show({
            content: "操作成功",
          });
          navigateBack();
        }
      },
    });
  };

  const handleGiveGift = async (gift: GiftResult, note?: string) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await dynamicGiveGiftMutations.mutateAsync({
      dynamicId: id!,
      giftId: gift.id!,
      note,
    });

    if (ok) {
      Toast.show({
        content: "礼物赠送成功",
      });
      revalidate();
    }
  };

  const dynamicCommentPublishCheckMutations = useMutation({
    mutationFn: dynamicCommentPublishCheck,
  });

  const { addToQueue } = useUploadQueueManager();

  const handleComment = async (data: FinishType) => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await dynamicCommentPublishCheckMutations.mutateAsync({
      targetId: id!,
      content: data.text,
      ats: data.ats,
      voice: data.voice ? getRandomParams() : undefined,
      resources: data.resource
        ? data.resource.map(() => getRandomParams())
        : undefined,
      topId: quote?.id,
      parentId: quote?.id,
    });

    if (ok) {
      addToQueue(
        {
          title: "动态评论",
          url: dynamicCommentPublishUrl,
          params: {
            targetId: id!,
            content: data.text,
            ats: data.ats,
            voice: data.voice,
            resources: data.resource,
            topId: quote?.id,
            parentId: quote?.id,
          },
        },
        () => {
          handleRefresh();
        }
      );
    }
  };

  const handleSetQuote = (quote: CommonCommentResult) => {
    setQuote({
      id: quote.id!,
      content: `回复：${quote?.user?.nickname} “${quote.voice ? "[语音]" : ""}${
        (quote.resources ?? []).some((r) => r.duration) ? "[视频]" : ""
      }${(quote.resources ?? []).some((r) => !r.duration) ? "[图片]" : ""}${
        quote.content
      }”`,
    });
  };

  return (
    <PageWrapper>
      <NavigationBar
        showBorder
        canBack
        title="动态详情"
        action={
          <SelectPopup
            options={
              isSelf
                ? [
                    {
                      title: "删除",
                      value: "delete",
                      onClick: handleDeleteClick,
                    },
                  ]
                : [
                    {
                      title: "举报",
                      value: "report",
                      onClick: handleReportClick,
                    },
                  ]
            }
          >
            <MoreIcon size={30} />
          </SelectPopup>
        }
      />
      <PageMain
        isEmpty={!data}
        extra={
          <CommentInput
            quote={quote}
            onClearQuote={() => setQuote(undefined)}
            onGiveGift={handleGiveGift}
            onFinish={handleComment}
          />
        }
        onRefresh={handleRefresh}
      >
        {data ? (
          <div className="pt-4 px-[14px] pb-[14px] flex flex-col gap-4">
            <DynamicItem
              showComment={false}
              data={data}
              canJump={false}
              secondaryColor="bg-scaffold-background"
            />
            <GiftContent data={data} />
            <div>
              <div className="font-medium text-base">
                全部评论({data?.commentNumber})
              </div>
              <ScrollLoadData
                queryKey={commentQueryKey}
                queryFn={commentQueryFn}
              >
                {(comments) => (
                  <div className="flex flex-col pt-5">
                    {comments.map((item, index) => (
                      <Fragment key={item.id}>
                        <CommentItem
                          type="dynamic"
                          id={data.id}
                          data={item}
                          onSetQuote={handleSetQuote}
                        />
                        {index !== comments.length - 1 ? (
                          <Divider className="border-divider" />
                        ) : null}
                      </Fragment>
                    ))}
                  </div>
                )}
              </ScrollLoadData>
            </div>
          </div>
        ) : null}
      </PageMain>
    </PageWrapper>
  );
}
