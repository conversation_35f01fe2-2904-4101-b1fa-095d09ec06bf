import { NavigationBar } from "@/components/navigation-bar";
import { NoRecord } from "@/components/no-record";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { userLevelSummary } from "@/lib/api/user";
import { PageParams } from "@/type";
import { Button } from "@/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/ui/table";
import { UserLevelType, userLevelTypeConfig } from "@/utils/enums";
import { useSearchParams } from "react-router-dom";

export default function UserLevelSummary() {
  const [searchParams] = useSearchParams();
  const { navigateRoute } = useNavigateRoute();

  const type = searchParams.get("type");

  const currentValue = userLevelTypeConfig[type as unknown as UserLevelType];

  const queryKey = ["user-level-summary", type];
  const queryFn = (params: PageParams) =>
    userLevelSummary(type as unknown as UserLevelType, params);

  const { handleRefresh } = usePageListRefresh(queryKey);

  const handleClick = () => {
    navigateRoute("/user-level-detailed", { type });
  };

  if (!currentValue) {
    return <NoRecord />;
  }

  return (
    <PageWrapper>
      <NavigationBar canBack title={`${currentValue.label}汇总`} />
      <PageMain className="p-[15px] pb-0" onRefresh={handleRefresh}>
        <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <Table className="w-full text-[15px]">
              <TableHeader className="bg-scaffold-background/50">
                <TableRow>
                  <TableHead className="text-center border border-scaffold-background">
                    <span className="text-[#6712FF] dark:text-white py-[15px]">
                      获取日期
                    </span>
                  </TableHead>
                  <TableHead className="text-center border border-scaffold-background">
                    <span className="text-[#6712FF] dark:text-white py-[15px]">
                      获取值
                    </span>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((item) => (
                  <TableRow>
                    <TableCell className="text-center border border-scaffold-background">
                      <span className="py-3">{item.date}</span>
                    </TableCell>
                    <TableCell className="text-center border border-scaffold-background">
                      <span className="py-3">{item.value}</span>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </ScrollLoadData>
      </PageMain>
      <div className="p-[15px]">
        <Button
          className="w-full"
          variant="primary"
          size="lg"
          onClick={handleClick}
        >
          查看明细
        </Button>
      </div>
    </PageWrapper>
  );
}
