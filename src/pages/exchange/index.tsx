import { CommonIcon } from "@/components/common-icon";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { tipsDialog } from "@/components/work/tips-dialog";
import { userWalletQueryKey, useUserWallet } from "@/hooks/use-user-wallet";
import { exchange } from "@/lib/api/wallet";
import { queryClient } from "@/provider/query-client";
import { LayoutConfigExchange } from "@/type/layout-config";
import { AspectRatio } from "@/ui/aspect-ratio";
import { Button } from "@/ui/button";
import { Input } from "@/ui/input";
import { userWalletType } from "@/utils/enums";
import { useMutation } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import classNames from "classnames";
import { useMemo, useState } from "react";
import { useAsyncValue } from "react-router-dom";

export default function Exchange() {
  const { data } = useAsyncValue() as { data: LayoutConfigExchange };

  const { getBalanceOrEmptyByType } = useUserWallet();

  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const [customAmount, setCustomAmount] = useState<string>("");

  const realAmount = useMemo(() => {
    return selectedIndex === data.items.length
      ? customAmount
      : data.items[selectedIndex];
  }, [selectedIndex, data.items, customAmount]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === "") {
      setCustomAmount("");
    } else {
      const parsedValue = parseInt(value.replace(/[^\d]/g, ""), 10);
      setCustomAmount(isNaN(parsedValue) ? "" : parsedValue.toString());
    }
  };

  const exchangeMutation = useMutation({
    mutationFn: exchange,
  });

  const handleExchange = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const { ok } = await exchangeMutation.mutateAsync(realAmount);

    if (ok) {
      tipsDialog.success({
        title: "兑换成功",
        content: `${userWalletType.currency.label}兑换成功`,
        confirmText: "知道了",
      });

      queryClient.refetchQueries({
        queryKey: userWalletQueryKey,
      });
    }
  };

  return (
    <PageWrapper className="relative z-10 bg-scaffold-background">
      <NavigationBar
        canBack
        title={`${userWalletType.currency.label}兑换`}
        className="bg-transparent"
      />
      <PageMain>
        <div className="h-[125px] flex justify-around">
          <div className="flex flex-col gap-[10px] items-center justify-center">
            <span className="text-[35px] text-white">
              {getBalanceOrEmptyByType(userWalletType.bean.value)}
            </span>
            <div className="flex gap-[5px] items-center">
              <CommonIcon name="bean" h={23} w={23} />
              <span className="text-base text-white">
                当前{userWalletType.bean.label}
              </span>
            </div>
          </div>
          <div className="flex flex-col gap-[10px] items-center justify-center">
            <span className="text-[35px] text-white">
              {getBalanceOrEmptyByType(userWalletType.currency.value)}
            </span>
            <div className="flex gap-[5px] items-center">
              <CommonIcon name="gold" h={23} w={23} />
              <span className="text-base text-white">
                当前{userWalletType.currency.label}
              </span>
            </div>
          </div>
        </div>
        <div className="flex flex-col rounded-t-[10px] bg-scaffold-background px-[15px] py-[10px]">
          <span className="py-[15px] text-[13px] text-hint-color text-center">
            请选择兑换数量
          </span>
          <div className="grid grid-cols-3 gap-[15px] text-[17px] text-white">
            {data.items.map((item, index) => (
              <AspectRatio ratio={1.5} key={item}>
                <div
                  className={classNames(
                    "w-full h-full rounded-[10px] flex items-center justify-center p-[15px]",
                    selectedIndex === index
                      ? "bg-[#9D64FF]"
                      : "bg-app-bar-background"
                  )}
                  onClick={() => setSelectedIndex(index)}
                >
                  {item}
                </div>
              </AspectRatio>
            ))}
            <AspectRatio ratio={1.5}>
              <div
                className={classNames(
                  "w-full h-full rounded-[10px] flex items-center justify-center p-[15px]",
                  selectedIndex === data.items.length
                    ? "bg-[#9D64FF]"
                    : "bg-app-bar-background"
                )}
                onClick={() => setSelectedIndex(data.items.length)}
              >
                <Input
                  placeholder="自定义"
                  className="border-none placeholder:text-white"
                  maxLength={7}
                  value={customAmount}
                  onChange={handleInputChange}
                />
              </div>
            </AspectRatio>
          </div>
          <div className="pt-[25px] pb-[15px]">
            <Button
              variant="primary"
              size="lg"
              className="w-full"
              disabled={!realAmount || !Number(realAmount)}
              onClick={handleExchange}
            >
              确认兑换
            </Button>
          </div>
          <div className="text-center">
            {!realAmount || !Number(realAmount)
              ? "请选择或输入数量"
              : `使用${realAmount}${userWalletType.bean.label}，可兑换${
                  Number(realAmount) * 10
                }${userWalletType.currency.label}，确认兑换`}
          </div>
        </div>
      </PageMain>
      <div className="absolute left-0 top-0 w-full h-[200px] withdrawal-bg z-[-1]" />
    </PageWrapper>
  );
}
