import { Button } from "@/ui/button";
import { Input } from "@/ui/input";
import { extractLeadingNumbers } from "@/utils";
import { userWalletType } from "@/utils/enums";
import { Popup } from "antd-mobile";
import { forwardRef, useEffect } from "react";
import { useImperativeHandle } from "react";
import { useState } from "react";

export interface PriceSettingModalRef {
  open: () => void;
  close: () => void;
}

interface Props {
  min: number;
  max: number;
  value: number;
  onChange: (value: number) => void;
}

export const PriceSettingModal = forwardRef<PriceSettingModalRef, Props>(
  ({ min, max, value, onChange }, ref) => {
    const [open, setOpen] = useState(false);
    const [price, setPrice] = useState(value);

    useEffect(() => {
      setPrice(value);
    }, [value]);

    const onOpen = () => {
      setOpen(true);
    };

    const onClose = () => {
      setOpen(false);
    };

    useImperativeHandle(ref, () => ({
      open: onOpen,
      close: onClose,
    }));

    return (
      <Popup
        destroyOnClose
        visible={open}
        bodyClassName="rounded-t-[10px]"
        onMaskClick={onClose}
        onClose={onClose}
      >
        <div className="h-[300px] flex flex-col gap-[15px] bg-app-bar-background p-[15px]">
          <div className="text-center text-base">费用修改</div>
          <div className="relative">
            <Input
              className="border-none bg-scaffold-background rounded pl-[15px] pr-[50px]"
              value={price}
              onChange={(e) =>
                setPrice(Number(extractLeadingNumbers(e.target.value)))
              }
            />
            <span className="text-hint-color absolute top-0 right-[20px] leading-[40px]">
              /{userWalletType.currency.label}
            </span>
          </div>
          <Button
            variant="primary"
            className="rounded-[10px] w-full"
            onClick={() => {
              if (price > max) {
                onChange(max);
              } else if (price < min) {
                onChange(min);
              } else {
                onChange(price);
              }

              onClose();
            }}
          >
            完成
          </Button>
          <div className="text-hint-color">
            短视频费用仅能设置为整数，最低
            {min}
            {userWalletType.currency.label}，最高可设置为
            {max}
            {userWalletType.currency.label}
          </div>
        </div>
      </Popup>
    );
  }
);
