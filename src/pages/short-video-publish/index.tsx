import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import {
  ShortVideoSearchModal,
  ShortVideoSearchModalRef,
} from "@/components/work/short-video-search-modal";
import { TagSelect, TagSelectRef } from "@/components/work/tag-select";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/ui/form";
import { Textarea } from "@/ui/textarea";
import { Switch, Toast } from "antd-mobile";
import { useEffect, useRef } from "react";
import { useForm } from "react-hook-form";
import { SingleMediaUpload } from "@/components/work/single-media-upload";
import { Button } from "@/ui/button";
import { useConfigList } from "@/hooks/use-config-list";
import { tipsDialog } from "@/components/work/tips-dialog";
import { EnabledLocation } from "@/components/work/enabled-location";
import {
  PriceSettingModal,
  PriceSettingModalRef,
} from "./components/price-setting-modal";
import { userWalletType } from "@/utils/enums";
import { useMutation } from "@tanstack/react-query";
import { shortVideoPublishCheck, shortVideoPublishUrl } from "@/lib/api/video";
import { useUploadQueueManager } from "@/hooks/use-upload-queue-manager";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { getRandomParams, getVideoDuration } from "@/utils/common";

export default function ShortVideoPublish() {
  const form = useForm();

  const customCover = form.watch("customCover");
  const price = form.watch("price");
  const charge = form.watch("charge");
  const resource = form.watch("resource");

  const tagSelectRef = useRef<TagSelectRef>(null);
  const shortVideoSearchModalRef = useRef<ShortVideoSearchModalRef>(null);
  const priceSettingModalRef = useRef<PriceSettingModalRef>(null);

  const { getSystemConfig } = useConfigList();

  const tips = getSystemConfig("SHORT_VIDEO_PUBLISH_TIPS");
  const min = getSystemConfig("SHORT_VIDEO_CHARGE_MIN_PLAY_PRICE");
  const max = getSystemConfig("SHORT_VIDEO_CHARGE_MAX_PLAY_PRICE");
  const defaultPrice = getSystemConfig(
    "SHORT_VIDEO_PUBLISH_DEFAULT_PLAY_PRICE"
  );

  useEffect(() => {
    if (resource) {
      getVideoDuration(resource).then((duration) => {
        form.setValue("duration", duration);
      });
    }
  }, [form, resource]);

  useEffect(() => {
    form.setValue("price", defaultPrice);
  }, [defaultPrice, form]);

  useEffect(() => {
    if (tips) {
      tipsDialog.success({
        title: "提示信息",
        content: <div className="whitespace-pre-wrap text-left">{tips}</div>,
        confirmText: "确定",
      });
    }
  }, [tips]);

  const { mutateAsync } = useMutation({
    mutationFn: shortVideoPublishCheck,
  });

  const { addToQueue } = useUploadQueueManager();
  const { navigateBack } = useNavigateRoute();

  const handleSubmit = async () => {
    Toast.show({
      icon: "loading",
      content: "请稍后...",
      duration: 0,
    });

    const data = form.getValues();

    const { cover, resource, customCover, charge, price, ...rest } = data;

    const { ok } = await mutateAsync({
      cover: customCover && cover ? getRandomParams() : undefined,
      resource: resource ? getRandomParams() : undefined,
      playPrice: charge ? price : undefined,
      ...rest,
    });

    if (ok) {
      addToQueue(
        {
          title: "短视频发布",
          url: shortVideoPublishUrl,
          params: {
            ...rest,
            cover: customCover ? cover : undefined,
            resource,
            playPrice: charge ? price : undefined,
          },
        },
        () => {
          navigateBack();
        }
      );
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="短视频发布" />
      <PageMain>
        <div className="p-[15px]">
          <Form {...form}>
            <div className="flex flex-col gap-[10px]">
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>描述</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          rows={5}
                          className="rounded-[10px] px-[15px] py-[10px] resize-none"
                          placeholder="请输入视频描述"
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>视频标签</FormLabel>
                      <FormControl>
                        <TagSelect
                          {...field}
                          ref={tagSelectRef}
                          append
                          options={[]}
                          moreClick={() =>
                            shortVideoSearchModalRef.current?.open()
                          }
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="charge"
                render={({ field }) => {
                  return (
                    <FormItem className="flex items-center justify-between">
                      <FormLabel>开启收费</FormLabel>
                      <div className="flex items-center gap-[10px]">
                        {charge ? (
                          <div
                            className="text-[15px] text-[#ffb400]"
                            onClick={() => priceSettingModalRef.current?.open()}
                          >
                            {price}
                            {userWalletType.currency.label}
                            (点击修改)
                          </div>
                        ) : null}
                        <FormControl>
                          <Switch
                            className="!m-0"
                            checked={field.value}
                            onChange={field.onChange}
                          />
                        </FormControl>
                      </div>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="enabledLocation"
                render={({ field }) => {
                  return (
                    <FormItem className="flex justify-between items-center">
                      <FormLabel>位置信息</FormLabel>
                      <FormControl>
                        <EnabledLocation {...field} />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              <FormField
                control={form.control}
                name="customCover"
                render={({ field }) => {
                  return (
                    <FormItem className="flex items-center justify-between">
                      <FormLabel>自定义封面</FormLabel>
                      <FormControl>
                        <Switch
                          className="!m-0"
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
              {customCover ? (
                <FormField
                  control={form.control}
                  name="cover"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel>封面</FormLabel>
                        <FormControl>
                          <SingleMediaUpload
                            accept="image/*"
                            {...field}
                            className="w-full h-[150px]"
                            placeholder="点击选择封面"
                          />
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
              ) : null}
              <FormField
                control={form.control}
                name="prohibitComment"
                render={({ field }) => {
                  return (
                    <FormItem className="flex items-center justify-between">
                      <FormLabel>禁止评论</FormLabel>
                      <FormControl>
                        <Switch
                          className="!m-0"
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />

              <FormField
                control={form.control}
                name="resource"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>视频资源</FormLabel>
                      <FormControl>
                        <SingleMediaUpload
                          accept="video/*"
                          {...field}
                          className="w-full h-[250px]"
                          placeholder="请选择视频"
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
            </div>
          </Form>
          <div className="my-[20px]">
            <Button variant="primary" className="w-full" onClick={handleSubmit}>
              立即上传
            </Button>
          </div>
        </div>
      </PageMain>
      <ShortVideoSearchModal
        ref={shortVideoSearchModalRef}
        onSelect={(name) => {
          tagSelectRef.current?.onAddTag({ id: name, label: name });
        }}
      />
      <PriceSettingModal
        min={Number(min)}
        max={Number(max)}
        value={Number(price)}
        onChange={(value) => form.setValue("price", value)}
        ref={priceSettingModalRef}
      />
    </PageWrapper>
  );
}
