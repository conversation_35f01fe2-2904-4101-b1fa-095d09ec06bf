import { accountSecurityList } from "@/lib/api/user";
import { queryClient } from "@/provider/query-client";
import { defer, LoaderFunction } from "react-router-dom";

export const loader: LoaderFunction = async () => {
  return defer({
    data: queryClient
      .fetchQuery({
        queryKey: ["account-security-list"],
        queryFn: accountSecurityList,
      })
      .catch((err) => err),
  });
};
