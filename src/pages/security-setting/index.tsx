import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ArrowIcon } from "@/components/svg-icon";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { getRoute } from "@/router/route-map";
import { AccountSecurityItem } from "@/type/account-security-item";
import classNames from "classnames";
import { useAsyncValue } from "react-router-dom";

export default function SecuritySetting() {
  const { data } = useAsyncValue() as { data: AccountSecurityItem[] };

  const { navigateRoute } = useNavigateRoute();

  const handleClick = (item: AccountSecurityItem) => {
    if (item.uri) {
      navigateRoute(getRoute(item.uri));
      return;
    }

    // todo: qq 微信绑定暂不支持
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="账号与安全" />
      <PageMain>
        <div className="flex flex-col p-[15px]">
          {data.map((item) => (
            <div
              key={item.title}
              className="flex justify-between py-[10px]"
              onClick={() => handleClick(item)}
            >
              <span className="text-[15px]">{item.title}</span>
              <div className="flex items-center gap-[5px]">
                <span
                  className={classNames(
                    "text-[13px]",
                    item.flag === 1 ? "text-[#FF3460]" : ""
                  )}
                >
                  {item.subtitle}
                </span>
                {item.uri || item.authType !== undefined ? <ArrowIcon /> : null}
              </div>
            </div>
          ))}
        </div>
      </PageMain>
    </PageWrapper>
  );
}
