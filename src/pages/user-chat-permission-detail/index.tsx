import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { confirmDialog } from "@/components/work/confirm-dialog";
import { useConfigList } from "@/hooks/use-config-list";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import {
  agreeUserChatPermission,
  closeUserChatPermission,
} from "@/lib/api/user";
import { UserChatPermissionResult } from "@/type/user-chat-permission-result";
import { Button } from "@/ui/button";
import {
  UserChatPermissionState,
  userChatPermissionStateConfig,
  userWalletType,
} from "@/utils/enums";
import { useMutation } from "@tanstack/react-query";
import { Toast } from "antd-mobile";
import { useAsyncValue, useNavigate, useSearchParams } from "react-router-dom";

export default function UserChatPermissionCreate() {
  const { data } = useAsyncValue() as { data: UserChatPermissionResult };

  const [searchParams] = useSearchParams();

  const id = searchParams.get("id");

  const { getSystemConfig } = useConfigList();
  const { data: userInfo } = useFullUserInfo();
  const navigate = useNavigate();

  const { mutateAsync: agree } = useMutation({
    mutationFn: agreeUserChatPermission,
  });

  const { mutateAsync: close } = useMutation({
    mutationFn: closeUserChatPermission,
  });

  const handleAgree = async () => {
    const result = await confirmDialog.show(
      `开通互动权限需要支付${data.price}${userWalletType.currency.label}，是否继续`,
      "收费提醒"
    );

    if (!result) return;

    Toast.show({
      content: "请稍后...",
      icon: "loading",
      duration: 0,
    });

    const { ok } = await agree(id!);

    if (ok) {
      Toast.show({
        content: "开通成功!",
      });

      navigate(-1);
    }
  };

  const handleClose = async () => {
    Toast.show({
      content: "请稍后...",
      icon: "loading",
      duration: 0,
    });

    const { ok } = await close(id!);

    if (ok) {
      Toast.show({
        content: "关闭成功!",
      });

      navigate(-1);
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="互动权限邀请" />
      <PageMain className="bg-scaffold-background">
        <div className="flex flex-col gap-[10px] p-[15px]">
          <div className="flex flex-col gap-[10px] bg-app-bar-background p-[15px] rounded-[5px]">
            <span>详细信息</span>
            <div className="flex flex-col">
              <span>价格: {data?.price ?? 0}</span>
              <span>
                天数: {data?.dayNumber ?? 0 <= 0 ? "永久" : data.dayNumber}
              </span>
              <span>
                状态:{" "}
                {typeof data.state === "number"
                  ? userChatPermissionStateConfig[data.state]
                  : "未知"}
              </span>
              {data.openDate ? <span>开通日期: {data.openDate}</span> : null}
              {data.expireDate ? (
                <span>失效日期: {data.expireDate}</span>
              ) : null}
            </div>
          </div>
          <div className="flex flex-col gap-[10px] p-[15px] bg-app-bar-background rounded-[5px]">
            <span>说明</span>
            <div>{getSystemConfig("USER_CHAT_PERMISSION_OPEN_NOTE")}</div>
          </div>
          <div className="pt-[30px]">
            {userInfo?.id === data.applicantUser?.id ? (
              <Button
                variant="primary"
                size="lg"
                className="w-full"
                disabled={data.state !== UserChatPermissionState.Wait}
                onClick={handleClose}
              >
                关闭
              </Button>
            ) : null}
            {userInfo?.id === data.respondentUser?.id ? (
              <Button
                variant="primary"
                size="lg"
                className="w-full"
                disabled={data.state !== UserChatPermissionState.Wait}
                onClick={handleAgree}
              >
                开通
              </Button>
            ) : null}
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
