import { defer, LoaderFunction, LoaderFunctionArgs } from "react-router-dom";
import { queryClient } from "@/provider/query-client";
import { parseUrl } from "@/utils/parse-url";
import { getUserChatPermission } from "@/lib/api/user";

export const loader: LoaderFunction = ({ request }: LoaderFunctionArgs) => {
  const { searchParams } = parseUrl(request.url);

  const id = searchParams.get("id");

  return defer({
    data: queryClient
      .fetchQuery({
        queryKey: ["user-chat-permission", "detail", id],
        queryFn: () => getUserChatPermission(id!),
      })
      .catch((err) => err),
  });
};
