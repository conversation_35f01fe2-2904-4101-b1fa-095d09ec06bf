/* eslint-disable @typescript-eslint/ban-ts-comment */
import { ApiResponse } from "@/lib/request";
import { PageParams } from "@/type";
import { QueryFunctionContext, useInfiniteQuery } from "@tanstack/react-query";
import { Swiper, SwiperRef } from "antd-mobile";
import { useEffect, useMemo, useRef, useState } from "react";
import { Button } from "@/ui/button";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { LiveRoomSimpleResult } from "@/type/live_room_simple_result";
import { LivePlayerWrapper } from "./live-player-wrapper";

interface Props {
  showTips?: boolean;
  defaultIndex?: number;
  queryKey: any[];
  queryFn: (params: PageParams) => Promise<ApiResponse<LiveRoomSimpleResult[]>>;
}

export const LivePlayerSwiper = (props: Props) => {
  const { queryKey, queryFn, showTips = true, defaultIndex = 0 } = props;

  const [activeIndex, setActiveIndex] = useState(defaultIndex);

  const swiperRef = useRef<SwiperRef>(null);

  // reload
  const { handleRefresh } = usePageListRefresh(queryKey);

  const reload = () => {
    handleRefresh();
    setActiveIndex(0);
    swiperRef.current?.swipeTo(0);
  };

  const {
    data: pageList,
    isFetching,
    hasNextPage,
    fetchNextPage,
  } = useInfiniteQuery({
    queryKey,
    queryFn: (ctx: QueryFunctionContext<string[], PageParams>) =>
      queryFn(ctx.pageParam),
    initialPageParam: {
      page: 1,
      size: 5,
    },
    getNextPageParam(lastPageData, _allPagesData, lastPageParam) {
      const page = lastPageParam.page + 1;
      const size = lastPageParam.size;

      const len = lastPageData.data?.length || 0;

      if (len < size) {
        //  没有下一页了
        return null;
      }

      return {
        page,
        size,
      };
    },
    staleTime: Infinity,
  });

  const datasource: LiveRoomSimpleResult[] = useMemo(
    () => pageList?.pages.map((page) => page.data ?? []).flat() || [],
    [pageList]
  );

  const handleIndexChange = (index: number) => {
    setActiveIndex(index);

    if (!isFetching && hasNextPage && index === datasource.length) {
      //  加载下一页
      fetchNextPage();
    }
  };

  const isFirstRender = useRef(true);
  useEffect(() => {
    if (datasource.length && isFirstRender.current) {
      isFirstRender.current = false;
      swiperRef.current?.swipeTo(defaultIndex);
    }
  }, [defaultIndex, datasource]);

  return (
    <div className="absolute top-0 left-0 w-full h-full">
      <Swiper
        ref={swiperRef}
        direction="vertical"
        indicator={false}
        style={{ height: "100%" }}
        onIndexChange={handleIndexChange}
      >
        {
          [
            ...datasource.map((item, index) => (
              <Swiper.Item key={item.id}>
                <LivePlayerWrapper
                  isActive={index === activeIndex}
                  data={item}
                />
              </Swiper.Item>
            )),
            showTips && isFetching ? (
              <Swiper.Item key="loading">
                <div className="absolute top-0 left-0 w-full h-full text-sm bg-black text-white flex items-center justify-center">
                  视频正在加载中，请耐心等待~
                </div>
              </Swiper.Item>
            ) : null,
            showTips && !isFetching ? (
              <Swiper.Item key="no-more">
                <div className="absolute top-0 left-0 w-full h-full text-sm bg-black text-white flex items-center justify-center">
                  <div className="flex flex-col gap-[10px] items-center justify-center">
                    <span className="text-sm text-white">
                      看起来你已经浏览完了所有直播间~
                    </span>
                    <Button
                      variant="primary"
                      className="px-[15px]"
                      onClick={reload}
                    >
                      重新加载
                    </Button>
                  </div>
                </div>
              </Swiper.Item>
            ) : null,
          ].filter(Boolean) as React.ReactElement[]
        }
      </Swiper>
    </div>
  );
};
