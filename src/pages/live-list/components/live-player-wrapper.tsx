import LivePlayerCom from "@/components/work/live-player-com";
import { LiveRoomSimpleResult } from "@/type/live_room_simple_result";
import { Image } from "antd-mobile";

interface Props {
  isActive: boolean;
  data: LiveRoomSimpleResult;
}

export const LivePlayerWrapper = (props: Props) => {
  const { isActive, data } = props;

  if (!isActive) {
    return <Image src={data.cover} width="100%" height="100%" />;
  }

  return <LivePlayerCom id={data.id!} />;
};
