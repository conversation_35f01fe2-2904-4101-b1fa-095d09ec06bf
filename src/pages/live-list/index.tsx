import { NavigationBar } from "@/components/navigation-bar";
import { PageWrapper } from "@/components/page-wrapper";
import { PageParams } from "@/type";
import { useSearchParams } from "react-router-dom";
import { getLayoutPageList } from "@/lib/api/layout";
import { LiveRoomSimpleResult } from "@/type/live_room_simple_result";
import { LivePlayerSwiper } from "./components/live-player-swiper";

export default function LiveListPage() {
  const [searchParams] = useSearchParams();

  const queryKeyParams = JSON.parse(searchParams.get("queryKey") ?? "") || [];
  const defaultIndex = Number(searchParams.get("defaultIndex"));

  const [url, , data] = queryKeyParams;

  const queryKey = queryKeyParams;
  const queryFn = (params: PageParams) => {
    return getLayoutPageList<LiveRoomSimpleResult[]>({
      url,
      params,
      data,
    });
  };

  return (
    <PageWrapper className="relative">
      <NavigationBar className="fixed z-10 bg-transparent" />
      <LivePlayerSwiper
        queryKey={queryKey}
        queryFn={queryFn}
        defaultIndex={defaultIndex}
      />
    </PageWrapper>
  );
}
