import { Avatar } from "@/components/avatar";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { UserInfo } from "@/components/work/user-info";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { RedEnvelopeResult } from "@/type/red-envelope-result";
import { Divider } from "antd-mobile";
import { useAsyncValue } from "react-router-dom";

export default function RedEnvelopeDetail() {
  const { data } = useAsyncValue() as { data: RedEnvelopeResult };

  const { data: userInfo } = useFullUserInfo();

  return (
    <PageWrapper className=" bg-[#FFFAF5] dark:bg-[#000510]">
      <NavigationBar
        canBack
        title="红包详细"
        className="bg-[#FF595D] text-white"
      />
      <PageMain>
        <div className="flex flex-col">
          <div className="h-[75px]" />
          <div className="flex justify-center items-center gap-[11px]">
            <Avatar src={data.user?.avatarUrl!} size={40} />
            <span className="text-[15px]">{data.user?.nickname}的红包</span>
          </div>
          <div className="h-[20px]" />
          {data.currentUserReceivedAmount ? (
            <>
              <div className="flex justify-center items-center">
                <span className="text-[#D0AB76] text-[55px]">
                  {data.currentUserReceivedAmount ?? 0}
                </span>
                <span className="text-[#D0AB76] text-[15px]">
                  {data.walletTypeLabel}
                </span>
              </div>
              <div className="h-[15px]" />
            </>
          ) : null}
          <div className="flex flex-col">
            <Divider className="border-divider" />
            <div className="px-[15px] text-[15px] text-hint-color">
              已领取{data.receivedNumber}/{data.number}个
              {userInfo?.id == data?.user?.id
                ? `共${data.receivedAmount ?? 0}/${data.availableAmount ?? 0}${
                    data.walletTypeLabel
                  }`
                : ""}
            </div>
            <Divider className="border-divider" />
          </div>
          <div className="flex flex-col gap-[29px] px-[15px]">
            {data.receiveDetailedList?.map((item, index) => (
              <div key={index} className="flex gap-[10px]">
                <UserInfo user={item.user!} size={41} />
                <div className="flex flex-col flex-none">
                  <span className="text-[15px] text-[#D0AB76]">
                    {item.amount ?? 0}
                    {data.walletTypeLabel}
                  </span>
                  {item.label ? (
                    <span className="text-xs text-[#D0AB76]">{item.label}</span>
                  ) : null}
                </div>
              </div>
            ))}
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
