import { redEnvelopeDetail } from "@/lib/api/wallet";
import { queryClient } from "@/provider/query-client";
import { parseUrl } from "@/utils/parse-url";
import { defer, LoaderFunction, LoaderFunctionArgs } from "react-router-dom";

export const loader: LoaderFunction = ({ request }: LoaderFunctionArgs) => {
  const { searchParams } = parseUrl(request.url);
  const id = searchParams.get("id");

  return defer({
    data: queryClient
      .fetchQuery({
        queryKey: ["red-envelope-detail", id],
        queryFn: () => redEnvelopeDetail(id!),
      })
      .catch((err) => err),
  });
};
