import { NavigationBar } from "@/components/navigation-bar";
import { NoRecord } from "@/components/no-record";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { userLevelDetailed } from "@/lib/api/user";
import { PageParams } from "@/type";
import { UserLevelType, userLevelTypeConfig } from "@/utils/enums";
import dayjs from "dayjs";
import { useSearchParams } from "react-router-dom";

export default function UserLevelDetailed() {
  const [searchParams] = useSearchParams();

  const type = searchParams.get("type");

  const currentValue = userLevelTypeConfig[type as unknown as UserLevelType];

  const queryKey = ["user-level-detailed", type];
  const queryFn = (params: PageParams) =>
    userLevelDetailed(type as unknown as UserLevelType, params);

  const { handleRefresh } = usePageListRefresh(queryKey);

  if (!currentValue) {
    return <NoRecord />;
  }

  return (
    <PageWrapper>
      <NavigationBar canBack title={`${currentValue.label}明细`} />
      <PageMain className="p-[15px]" onRefresh={handleRefresh}>
        <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
          {(data) => (
            <div className="flex flex-col gap-[15px]">
              {data.map((item, index) => (
                <div key={index} className="flex items-center">
                  <div className="flex-1 flex flex-col">
                    <span className="text-[15px] line-clamp-1">
                      {item.note}
                    </span>
                    <span className="text-[13px] text-hint-color">
                      {dayjs(item.cdate).format("YYYY-MM-DD HH:mm:ss")}
                    </span>
                  </div>
                  <span className="text-[15px] text-[#6712FF]">
                    +{item.value}
                  </span>
                </div>
              ))}
            </div>
          )}
        </ScrollLoadData>
      </PageMain>
    </PageWrapper>
  );
}
