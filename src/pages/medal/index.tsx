import { DefaultLoadImage } from "@/components/default-load-image";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { getMedalList } from "@/lib/api/user";
import { PageParams } from "@/type";
import { AspectRatio } from "@/ui/aspect-ratio";
import { Image } from "antd-mobile";

export default function MedalPage() {
  const queryKey = ["medal-list"];
  const queryFn = (params: PageParams) => getMedalList(params);

  return (
    <PageWrapper>
      <NavigationBar canBack title="勋章列表" />
      <PageMain>
        <div className="py-[30px]">
          <ScrollLoadData
            queryKey={queryKey}
            queryFn={queryFn}
            showTail={false}
          >
            {(data) => (
              <div className="grid grid-cols-3">
                {data.map((item) => {
                  return (
                    <AspectRatio key={item.name} ratio={1}>
                      <div className="flex justify-center items-center">
                        <div className="w-[75px] flex flex-col gap-[10px] items-center">
                          <Image
                            src={item.image}
                            width="100%"
                            height="auto"
                            placeholder={<DefaultLoadImage />}
                            fallback={<DefaultLoadImage />}
                            style={
                              item.collectionTime
                                ? {}
                                : { filter: "saturate(0)" }
                            }
                          />
                          <span className="text-[13px]">{item.name}</span>
                        </div>
                      </div>
                    </AspectRatio>
                  );
                })}
              </div>
            )}
          </ScrollLoadData>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
