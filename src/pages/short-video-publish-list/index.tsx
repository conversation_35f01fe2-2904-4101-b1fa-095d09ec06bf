import { NavigationBar } from "@/components/navigation-bar";
import { PageWrapper } from "@/components/page-wrapper";
import { useLocation, useSearchParams } from "react-router-dom";
import { ShortVideoSwiper } from "../home/<USER>/components/short-video-swiper";
import { PageParams } from "@/type";
import { getLayoutPageList } from "@/lib/api/layout";

export default function ShortVideoPublishList() {
  const [searchParams] = useSearchParams();
  const location = useLocation();

  const defaultIndex = Number(searchParams.get("defaultIndex"));

  const { queryKey, url, data } = location.state ?? {};

  const queryFn = (params: PageParams) => {
    return getLayoutPageList<any[]>({
      url,
      params,
      data,
    });
  };

  return (
    <PageWrapper className="relative">
      <NavigationBar canBack className="fixed z-10 bg-transparent" />
      <ShortVideoSwiper
        queryKey={queryKey}
        queryFn={queryFn}
        defaultIndex={defaultIndex}
      />
    </PageWrapper>
  );
}
