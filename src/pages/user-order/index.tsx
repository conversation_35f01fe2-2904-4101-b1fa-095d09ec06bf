import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { FilterTabs } from "@/components/work/filter-tabs";
import { LayoutConfigUserOrderHome } from "@/type/layout-config";
import { useState } from "react";
import { useAsyncValue } from "react-router-dom";
import { TabContent } from "./components/tab-content";

export default function UserOrderPage() {
  const { data: layoutConfig } = useAsyncValue() as {
    data: LayoutConfigUserOrderHome;
  };

  const tabs =
    layoutConfig?.tabs?.items.map((item, index) => ({
      ...item,
      key: index,
    })) || [];

  const [activeTab, setActiveTab] = useState(
    layoutConfig.tabs.defaultIndex ?? 0
  );

  return (
    <PageWrapper>
      <NavigationBar canBack title="我的订单" />
      <PageMain needScroll={false}>
        <div className="p-[15px] flex flex-col h-full">
          <div>
            <FilterTabs tabs={tabs} value={activeTab} onChange={setActiveTab} />
          </div>
          <div className="flex-1 h-0">
            {tabs.map((tab, index) => (
              <TabContent
                key={index}
                index={index}
                isActive={index === activeTab}
                data={tab}
              />
            ))}
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
