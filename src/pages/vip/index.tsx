import { CommonIcon } from "@/components/common-icon";
import { DefaultLoadImage } from "@/components/default-load-image";
import { vipBackground } from "@/components/image-list";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { UserAvatar } from "@/components/user-avatar";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { usePay } from "@/hooks/use-pay";
import { useScrollOpacity } from "@/hooks/user-scroll-opacity";
import { useUserVip } from "@/hooks/user-vip";
import { VipCostResult } from "@/type/vip-cost-result";
import { VipDescribeResult } from "@/type/vip-describe-result";
import { VipTypeResult } from "@/type/vip-type-result";
import { Button } from "@/ui/button";
import { ScrollArea, ScrollBar } from "@/ui/scroll-area";
import { PayBusinessType } from "@/utils/enums";
import { Image } from "antd-mobile";
import classNames from "classnames";
import { useEffect, useMemo, useRef, useState } from "react";
import { useAsyncValue } from "react-router-dom";

export default function VipPage() {
  const { vipType, vipCost, vipDescribe } = useAsyncValue() as {
    vipType: VipTypeResult[];
    vipCost: VipCostResult[];
    vipDescribe: VipDescribeResult[];
  };

  const targetRef = useRef<HTMLDivElement>(null);

  const { data: user } = useFullUserInfo();
  const { data: userVip } = useUserVip();

  const [selectedVipType, setSelectedVipType] = useState(vipType[0].id);

  const currentVipType = vipType.find(
    (item) => item.id === selectedVipType
  ) as VipTypeResult;

  const currentVipCost = useMemo(
    () => vipCost.filter((item) => item.vipTypeId === selectedVipType),
    [selectedVipType, vipCost]
  );

  const [selectedVipCost, setSelectedVipCost] = useState(currentVipCost[0].id);

  const haveCurrentVipType = useMemo(() => {
    return userVip?.items.some((item) => item.typeId === selectedVipType);
  }, [selectedVipType, userVip?.items]);

  const [initPosition, setInitPosition] = useState(200);
  const { opacity, scrollRef } = useScrollOpacity(initPosition);

  useEffect(() => {
    // 切换时，默认选中第一个
    setSelectedVipCost(currentVipCost[0].id);
  }, [currentVipCost]);

  useEffect(() => {
    const barHeight = (46 * window.innerWidth) / 375;

    setInitPosition(
      (targetRef.current?.getBoundingClientRect().top ?? 0) - barHeight
    );
  }, []);

  const { showPay } = usePay({
    type: PayBusinessType.Vip,
    params: {
      costId: selectedVipCost,
    },
  });

  const handleVip = () => {
    showPay();
  };

  return (
    <PageWrapper className="relative z-10 bg-scaffold-background">
      <NavigationBar
        canBack
        title="会员中心"
        className="bg-transparent"
        style={
          opacity > 0.5
            ? {
                background: `hsla(var(--app-bar-background)/${opacity})`,
                color: `hsla(var(--foreground)/${opacity})`,
              }
            : { color: "#fff" }
        }
      />
      <PageMain scrollRef={scrollRef}>
        <div className="pt-[30px] px-[15px] flex gap-[10px]">
          <UserAvatar src={user?.avatarUrl ?? ""} size={40} isVip={user?.vip} />
          <div className="flex flex-col justify-evenly">
            <span className="text-white text-[15px]">{user?.nickname}</span>
            {userVip ? (
              <span className="text-[#FFB74B] text-[13px]">{userVip.desc}</span>
            ) : null}
          </div>
        </div>
        <div className="pt-[20px] px-[15px]">
          <ScrollArea>
            <div className="flex gap-[10px]">
              {vipType.map((item) => (
                <div
                  key={item.id}
                  className={classNames(
                    "w-[108px] h-[45px] rounded-t-[5px] vip-type-item flex items-center justify-center",
                    item.id === selectedVipType
                      ? "vip-type-item-selected text-[#FF950C]"
                      : ""
                  )}
                  onClick={() => {
                    setSelectedVipType(item.id);
                  }}
                >
                  {item.id === selectedVipType ? (
                    <CommonIcon name="vip" w={16} h={16} className="mr-[5px]" />
                  ) : null}
                  <span>{item.name}</span>
                </div>
              ))}
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </div>
        <div className="bg-app-bar-background" ref={targetRef}>
          <ScrollArea>
            <div className="flex gap-[10px] p-[15px] pt-[25px]">
              {currentVipCost.map((item) => (
                <div
                  key={item.id}
                  className={classNames(
                    "relative w-[105px] h-[115px] rounded-[10px] bg-scaffold-background flex flex-col items-center justify-center border-[2px] border-solid border-scaffold-background",
                    item.id === selectedVipCost
                      ? "!bg-[#4857CD]/30 !border-[#4857CD]"
                      : ""
                  )}
                  onClick={() => {
                    setSelectedVipCost(item.id);
                  }}
                >
                  <span className="text-sm">{item.name}</span>
                  <div className="py-[5px]">
                    <span className="text-xs text-[#4D58CA] font-bold">¥</span>
                    <span className="text-[19px] text-[#4D58CA] font-bold">
                      {item.presentPrice}
                    </span>
                  </div>
                  <div>
                    <span className="inline-flex rounded-[3px] px-[5px] vip-cost-day-bg">
                      <span
                        className="text-[22px] text-[#4D58CA]"
                        style={{ zoom: 0.5 }}
                      >
                        {item.day}
                        {(item.give ?? 0) > 0 ? `+${item.give}` : ""}天
                      </span>
                    </span>
                  </div>
                  <div className="absolute top-[-6px] left-3 px-1 rounded inline-flex vip-describe-bg">
                    <span
                      className="text-[#FFCF90] text-[20px]"
                      style={{ zoom: 0.5 }}
                    >
                      享{currentVipType.describe?.length}项特权
                    </span>
                  </div>
                </div>
              ))}
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
          <div className="px-[15px] pb-[10px]">
            <Button
              variant="primary"
              size="lg"
              className="w-full"
              onClick={handleVip}
            >
              立即{haveCurrentVipType ? "续费" : "开通"}-{currentVipType.name}
            </Button>
          </div>
        </div>
        <div className="pt-[15px] bg-scaffold-background">
          <div className="p-[15px] bg-app-bar-background">
            <div className="flex flex-col items-center">
              <span className="text-base">特权列表</span>
              <span className="pt-[5px] text-hint-color text-[13px]">
                以下深色背景即为当前选中会员可享受权益
              </span>
              <span className="pb-[10px] text-hint-color text-[13px]">
                (点击图标可查看特权说明)
              </span>
            </div>
            <div className="bg-scaffold-background p-[15px] rounded-[5px] mb-[15px]">
              <div className="grid grid-cols-4">
                {vipDescribe.map((item) => {
                  const have = currentVipType.describe?.includes(item.id!);

                  return (
                    <div
                      key={item.id}
                      className="flex flex-col gap-[5px] justify-center items-center py-[10px]"
                    >
                      <Image
                        src={item.icon}
                        width={34}
                        height={34}
                        placeholder={<DefaultLoadImage />}
                        fallback={<DefaultLoadImage />}
                        style={have ? {} : { filter: "saturate(0)" }}
                      />
                      <span className="text-xs text-hint-color">
                        {item.title}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </PageMain>
      <div className="absolute z-[-1] top-0 left-0 w-full h-[183px]">
        <Image
          width="100%"
          height="100%"
          src={vipBackground}
          fit="cover"
          placeholder={null}
          fallback={null}
        />
      </div>
    </PageWrapper>
  );
}
