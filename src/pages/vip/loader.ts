import { vipCostList, vipDescribeList, vipTypeList } from "@/lib/api/system";
import { queryClient } from "@/provider/query-client";
import { defer, LoaderFunction } from "react-router-dom";

export const loader: LoaderFunction = async () => {
  const [vipType, vipCost, vipDescribe] = await Promise.all([
    queryClient.fetchQuery({
      queryKey: ["vip-type"],
      queryFn: vipTypeList,
    }),
    queryClient.fetchQuery({
      queryKey: ["vip-cost"],
      queryFn: vipCostList,
    }),
    queryClient.fetchQuery({
      queryKey: ["vip-describe"],
      queryFn: vipDescribeList,
    }),
  ]);

  return defer({
    data: {
      vipType: vipType.data,
      vipCost: vipCost.data,
      vipDescribe: vipDescribe.data,
    },
  });
};
