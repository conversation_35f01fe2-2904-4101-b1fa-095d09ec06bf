import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { changePrivacySetting } from "@/lib/api/user";
import {
  UserSettingGroup,
  UserSettingGroupItem,
} from "@/type/user-setting-group";
import { useMutation } from "@tanstack/react-query";
import { Switch, Toast } from "antd-mobile";
import { useAsyncValue, useRevalidator } from "react-router-dom";

export default function PrivacySettingPage() {
  const { data } = useAsyncValue() as { data: UserSettingGroup[] };
  const { revalidate } = useRevalidator();

  const changePrivacySettingMutation = useMutation({
    mutationFn: changePrivacySetting,
  });

  const handleChange = async (item: UserSettingGroupItem) => {
    Toast.show({
      icon: "loading",
      content: "操作中...",
      duration: 0,
    });

    const { ok } = await changePrivacySettingMutation.mutateAsync(item.params);

    if (ok) {
      revalidate();
    }
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="隐私设置" />
      <PageMain className="bg-scaffold-background">
        {data.map((group) => (
          <div key={group.name} className="flex flex-col">
            <div className="p-[15px] text-sm text-hint-color">{group.name}</div>
            <div className="flex flex-col bg-app-bar-background">
              {group.items.map((item) => (
                <div
                  key={item.title}
                  className="flex gap-[15px] items-center py-[10px] mx-[15px]"
                >
                  <div className="flex-1 flex flex-col">
                    <span className="text-base">{item.title}</span>
                    <span className="text-[13px]">{item.subtitle}</span>
                  </div>
                  <Switch
                    checked={item.open}
                    onChange={() => handleChange(item)}
                  />
                </div>
              ))}
            </div>
          </div>
        ))}
      </PageMain>
    </PageWrapper>
  );
}
