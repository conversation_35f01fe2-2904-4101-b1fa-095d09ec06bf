import {
  ResourcePreview,
  ResourcePreviewRef,
} from "@/components/resource-preview";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { ResourcePreviewData } from "@/type/resource-preview-data";
import { useEffect, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";

export default function ResourcePreviewPage() {
  const isBackRef = useRef(false);

  const { state } = useLocation();

  const { data = [], defaultIndex = 0 } = state as {
    data?: ResourcePreviewData[];
    defaultIndex?: number;
  };

  const previewRef = useRef<ResourcePreviewRef>(null);

  const navigate = useNavigate();
  const { navigateRoute } = useNavigateRoute();

  useEffect(() => {
    if (data.length === 0) {
      navigateRoute("/dynamic");
    }
  }, [data.length, navigateRoute]);

  useEffect(() => {
    previewRef.current?.swipeTo(defaultIndex);
  }, [defaultIndex]);

  const handleClose = () => {
    if (!isBackRef.current) {
      navigate(-1);
      isBackRef.current = true;
    }
  };

  return <ResourcePreview ref={previewRef} data={data} onClose={handleClose} />;
}
