import { CommonIcon } from "@/components/common-icon";
import { UserAvatar } from "@/components/user-avatar";
import { ArrowIcon } from "@/components/svg-icon";
import { ScrollArea, ScrollBar } from "@/ui/scroll-area";
import { Image } from "antd-mobile";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { PlaybackVideoResult } from "@/type/playback-video-result";

interface Props {
  data: PlaybackVideoResult;
}

export const GiftContent = (props: Props) => {
  const { data } = props;
  const { giftGiver, giftGiverCount, giftSummary } = data;

  const { navigateRoute } = useNavigateRoute();

  const handleGiftGiverClick = () => {
    navigateRoute("/video-gift-giver", { videoId: data.id });
  };

  return giftGiver?.length || giftSummary?.length ? (
    <div className="flex flex-col gap-5 mt-2">
      {giftGiver?.length ? (
        // 送礼人列表
        <div
          className="flex items-center relative"
          onClick={handleGiftGiverClick}
        >
          <div className="dynamic-detail-gift-icon-bg w-[47px] h-[47px] rounded-full flex justify-center items-center absolute">
            <CommonIcon name="gift" w={28} h={31} />
          </div>
          <div className="h-[29px] flex-1 w-0 flex gap-[10px] items-center bg-[#f7f5fb] dark:bg-[#131F31] rounded-[13px] pl-[51px] pr-3">
            <ScrollArea className="flex-1">
              <div className="flex gap-[5px]">
                {giftGiver.map((gift) => (
                  <UserAvatar
                    key={gift?.user?.id}
                    src={gift?.user?.avatarUrl ?? ""}
                    size={15}
                  />
                ))}
              </div>
              <ScrollBar orientation="horizontal" />
            </ScrollArea>
            <div className="text-xs text-[#728ba4] flex-none flex items-center gap-2">
              <span>{giftGiverCount}人赠送礼物</span>
              <ArrowIcon />
            </div>
          </div>
        </div>
      ) : null}
      {giftSummary?.length ? (
        // 收到的礼物汇总
        <div className="flex flex-col gap-3">
          <span className="font-medium text-base">收到的打赏</span>
          <div className="flex">
            <div className="flex-1 w-0">
              <ScrollArea>
                <div className="flex gap-[13px]">
                  {giftSummary.map((gift) => (
                    <div
                      key={gift.image}
                      className="flex flex-col items-center"
                    >
                      <div className="w-[60px] h-[60px] rounded-[5px] bg-[#f3f3f3] dark:bg-[#131f31] flex items-center justify-center">
                        <div className="w-[36px]">
                          <Image
                            src={gift.image ?? ""}
                            className="object-cover"
                            width="100%"
                            height="auto"
                            fit="cover"
                            placeholder={null}
                          />
                        </div>
                      </div>
                      <span className="text-[#8a90a8]">x{gift.number}</span>
                    </div>
                  ))}
                </div>
                <ScrollBar orientation="horizontal" />
              </ScrollArea>
            </div>
          </div>
        </div>
      ) : null}
    </div>
  ) : null;
};
