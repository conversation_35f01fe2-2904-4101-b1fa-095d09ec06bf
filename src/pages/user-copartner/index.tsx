import { copartnerBackground } from "@/components/image-list";
import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { UserAvatar } from "@/components/user-avatar";
import {
  UserInfoModal,
  UserInfoModalRef,
} from "@/components/work/user-info-modal";
import { useConfigList } from "@/hooks/use-config-list";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { usePay } from "@/hooks/use-pay";
import { useScrollOpacity } from "@/hooks/user-scroll-opacity";
import { getInviteList } from "@/lib/api/user";
import { PageParams } from "@/type";
import { Button } from "@/ui/button";
import { PayBusinessType } from "@/utils/enums";
import { formatTimeLine } from "@/utils/format-time-line";
import { Image } from "antd-mobile";
import dayjs from "dayjs";
import { useRef } from "react";
import { Fragment, useMemo } from "react";

export default function UserCopartnerPage() {
  const queryKey = ["user-copartner-invite-list"];
  const queryFn = (params: PageParams) => getInviteList(params);

  const { getSystemConfig } = useConfigList();
  const { data: userInfo } = useFullUserInfo();

  const userInfoModalRef = useRef<UserInfoModalRef>(null);

  const openUserInfoModal = (id: string) => {
    userInfoModalRef.current?.open(id);
  };

  const contents = useMemo(() => {
    return getSystemConfig("USER_COPARTNER_NOTE")
      ?.split("###\n")
      .map((item) => {
        const [title, ...rest] = item.split("\n") ?? [];
        return {
          title,
          content: rest.join("\n"),
        };
      });
  }, [getSystemConfig]);

  const { navigateRoute } = useNavigateRoute();

  const handleInvite = () => {
    navigateRoute("/user-invite-poster");
  };

  const { opacity, scrollRef } = useScrollOpacity(200);

  const { showPay } = usePay({
    type: PayBusinessType.UserPartner,
    params: {},
  });

  return (
    <PageWrapper className="relative z-10 bg-[#FE9232]">
      <NavigationBar
        canBack
        title="合伙人"
        className="bg-transparent"
        style={
          opacity > 0.5
            ? {
                background: `hsla(var(--app-bar-background)/${opacity})`,
                color: `hsla(var(--foreground)/${opacity})`,
              }
            : { color: "#fff" }
        }
      />
      <PageMain scrollRef={scrollRef}>
        <div className="flex flex-col items-center gap-[15px]">
          <span className="text-[34px] text-white">招募合伙人</span>
          <span className="text-[18px] text-white">邀请好友赚大钱</span>
        </div>
        <div className="mx-[15px] mt-[150px]">
          <div className="flex flex-col gap-[15px]">
            {contents?.map((item, index) => (
              <div
                key={index}
                className="flex flex-col gap-[15px] rounded-[10px] bg-white p-[15px]"
              >
                <span className="text-[17px] text-[#9F370D] font-bold text-center">
                  {item.title}
                </span>
                <span className="text-[14px] text-black whitespace-pre-wrap">
                  {item.content}
                </span>
              </div>
            ))}
          </div>
        </div>
        <div className="mx-[15px] mt-[15px]">
          {userInfo?.copartner ? (
            <Button
              variant="secondary"
              size="lg"
              className="w-full"
              onClick={handleInvite}
            >
              立即邀请
            </Button>
          ) : (
            <Button
              variant="primary"
              size="lg"
              className="w-full"
              onClick={showPay}
            >
              开通合伙人({getSystemConfig("PAY_USER_COPARTNER")}元)
            </Button>
          )}
        </div>
        <div className="p-[15px]">
          <div className="bg-white rounded-[10px] p-[15px] flex flex-col gap-[15px]">
            <span className="text-[17px] text-[#9F370D] font-bold text-center">
              我邀请的好友
            </span>
            <div>
              <ScrollLoadData
                queryKey={queryKey}
                queryFn={queryFn}
                noMoreStyle={{ color: "#e1dcff" }}
              >
                {(data) => (
                  <div className="w-full">
                    {data.map((item) => (
                      <Fragment key={item.user?.id}>
                        <div
                          className="flex gap-[10px] w-full"
                          onClick={() => openUserInfoModal(item.user?.id!)}
                        >
                          <UserAvatar
                            src={item.user?.avatarUrl!}
                            size={25}
                            isVip={item.user?.vip}
                          />
                          <span className="text-black text-sm line-clamp-1 flex-1">
                            {item.user?.nickname}
                          </span>
                          <span className="text-sm text-[#666]">
                            {formatTimeLine(dayjs(item.cdate!).valueOf())}
                            (今日收益:{item.todayIncome ?? 0} 总收益:
                            {item.totalIncome ?? 0})
                          </span>
                        </div>
                      </Fragment>
                    ))}
                  </div>
                )}
              </ScrollLoadData>
              <UserInfoModal ref={userInfoModalRef} />
            </div>
          </div>
        </div>
      </PageMain>
      <div className="absolute top-0 left-0 w-full h-auto z-[-1]">
        <Image
          src={copartnerBackground}
          width="100%"
          height="100%"
          placeholder={null}
          fallback={null}
          className="object-cover"
        />
      </div>
    </PageWrapper>
  );
}
