import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { Popup, Switch, Toast } from "antd-mobile";
import { useState } from "react";
import { CAMOUFLAGE_PASSWORD } from "@/type/cache";
import { Input } from "@/ui/input";
import { Button } from "@/ui/button";

export default function CamouflageSetting() {
  const [checked, setChecked] = useState(
    !!localStorage.getItem(CAMOUFLAGE_PASSWORD)
  );
  const [visible, setVisible] = useState(false);

  const [value, setValue] = useState("");

  const handleChange = (checked: boolean) => {
    if (checked) {
      setVisible(true);
    } else {
      localStorage.removeItem(CAMOUFLAGE_PASSWORD);
      setChecked(false);
    }
  };

  const handleFinish = () => {
    localStorage.setItem(CAMOUFLAGE_PASSWORD, value);
    setVisible(false);
    setChecked(true);

    Toast.show({
      content: "伪装设置成功，请牢记密码，刷新页面后生效!",
    });
  };

  return (
    <PageWrapper>
      <NavigationBar canBack title="伪装应用" />
      <PageMain>
        <div className="camouflage-setting flex gap-[15px] items-center p-[15px]">
          <div className="flex flex-col">
            <span>伪装应用</span>
            <span className="text-[13px]">
              打开伪装应用后启动APP会进入伪装页面而不是APP
            </span>
          </div>
          <div>
            <Switch checked={checked} onChange={handleChange} />
          </div>
        </div>
      </PageMain>
      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
        onClose={() => {
          setVisible(false);
        }}
        bodyClassName="h-[350px] rounded-t-[5px] bg-app-bar-background p-[15px]"
      >
        <div className="flex flex-col gap-[15px] justify-center">
          <span>伪装密码设置</span>
          <Input
            className="border-none bg-hint-color/10 rounded-[5px] px-[15px]"
            value={value}
            onChange={(e) => setValue(e.target.value)}
          />
          <Button
            variant="primary"
            size="lg"
            className="w-full rounded-[5px]"
            disabled={!value.trim()}
            onClick={handleFinish}
          >
            完成
          </Button>
          <div className="text-center">
            <span className="text-[13px] text-hint-color">
              请牢记伪装密码，如果遗忘将无法使用APP
            </span>
          </div>
        </div>
      </Popup>
    </PageWrapper>
  );
}
