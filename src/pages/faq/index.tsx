import { Collapse } from "antd-mobile";
import { NavigationBar } from "@/components/navigation-bar";
import { CommonIcon } from "@/components/common-icon";
import { useAsyncValue } from "react-router-dom";
import { FaqItem } from "@/type/faq-list";
import "./index.less";
import { ApiResponse } from "@/lib/request";
import { PageWrapper } from "@/components/page-wrapper";
import { PageMain } from "@/components/page-main";

export default function FaqPage() {
  const { data } = useAsyncValue() as ApiResponse<FaqItem[]>;

  return (
    <PageWrapper>
      <NavigationBar showBorder title="常见问题" />
      <PageMain
        isEmpty={!data?.length}
        className="bg-[#F6F6F6] dark:bg-[#000510]"
      >
        <div className="w-full h-full pb-[15px]">
          <Collapse>
            {data?.map((faq) => (
              <Collapse.Panel
                key={faq.title}
                title={
                  <div className="flex items-center gap-2 pl-[18px]">
                    <CommonIcon
                      name="question"
                      w={18}
                      h={18}
                      className="flex-none"
                    />
                    <span className="text-[15px] font-medium text-foreground">
                      {faq.title}
                    </span>
                  </div>
                }
              >
                <div className="text-[#666] pl-[44px] text-sm whitespace-pre-wrap">
                  {faq.content}
                </div>
              </Collapse.Panel>
            ))}
          </Collapse>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
