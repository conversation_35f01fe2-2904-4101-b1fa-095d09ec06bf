import { ScrollLoadData } from "@/components/scroll-load-data";
import { getLayoutPageList } from "@/lib/api/layout";
import { PageParams } from "@/type";
import { BaseUrlConfig } from "@/type/layout-config";
import { Fragment, useEffect, useState } from "react";
import { PullToRefresh } from "@/components/pull-to-refresh";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { ScrollArea } from "@/ui/scroll-area";
import { Divider } from "antd-mobile";
import { UserPurchasedResult } from "@/type/user-purchased-result";
import { UserPurchasedItem } from "./user-purchased-item";

interface Props {
  isActive: boolean;
  index: number;
  id?: string;
  data: BaseUrlConfig;
}

export const TabContent = (props: Props) => {
  const { isActive, index, data } = props;

  const [isInit, setIsInit] = useState(false);

  const queryKey = [data.url, index, data.params];
  const queryFn = (params: PageParams) => {
    return getLayoutPageList<UserPurchasedResult[]>({
      url: data.url,
      params,
      data: data.params,
    });
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  useEffect(() => {
    if (isActive) {
      setIsInit(true);
    }
  }, [isActive]);

  if (!isInit) {
    return null;
  }

  return (
    <ScrollArea className={isActive ? "h-full" : "hidden"}>
      <PullToRefresh onRefresh={handleRefresh}>
        <ScrollLoadData size={10} queryKey={queryKey} queryFn={queryFn}>
          {(list) => (
            <div className="flex flex-col w-full pt-[15px] bg-app-bar-background">
              {list.map((item, index) => (
                <Fragment key={item.id}>
                  <UserPurchasedItem data={item} />
                  {index !== list.length - 1 ? (
                    <Divider className="border-divider my-[5px]" />
                  ) : null}
                </Fragment>
              ))}
            </div>
          )}
        </ScrollLoadData>
      </PullToRefresh>
    </ScrollArea>
  );
};
