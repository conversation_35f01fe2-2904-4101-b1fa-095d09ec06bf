import { DefaultLoadImage } from "@/components/default-load-image";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { getRoute } from "@/router/route-map";
import { UserPurchasedResult } from "@/type/user-purchased-result";
import { userWalletType } from "@/utils/enums";
import { formatTimeLine } from "@/utils/format-time-line";
import { Image } from "antd-mobile";
import dayjs from "dayjs";

interface Props {
  data: UserPurchasedResult;
}

export const UserPurchasedItem = (props: Props) => {
  const { data } = props;

  const { navigateRoute } = useNavigateRoute();

  const handleDetail = () => {
    data.uri && navigateRoute(getRoute(data.uri));
  };

  return (
    <div className="p-[15px] flex gap-[15px]" onClick={handleDetail}>
      {data.cover ? (
        <div className="w-[70px] h-[70px] flex-none self-center">
          <Image
            src={data.cover}
            width="100%"
            height="100%"
            fit="cover"
            className="rounded-[5px]"
            lazy
            placeholder={<DefaultLoadImage />}
            fallback={<DefaultLoadImage />}
          />
        </div>
      ) : null}
      <div className="flex flex-col justify-evenly w-full">
        <div className="w-full">
          <span className="flex-none px-[5px] text-white text-xs rounded-[5px] bg-[#FF3460] mr-[2px]">
            {data.targetTypeName}
          </span>
          <span>{data.note}</span>
        </div>
        <div className="flex justify-between text-[13px] text-hint-color">
          <span>
            购买价格: {data.price ?? 0}
            {userWalletType.currency.label}
          </span>
          <span>{formatTimeLine(dayjs(data.cdate).valueOf())}</span>
        </div>
      </div>
    </div>
  );
};
