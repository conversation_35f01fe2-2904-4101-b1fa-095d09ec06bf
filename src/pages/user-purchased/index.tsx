import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { Tabs } from "@/components/work/tabs";
import { LayoutConfigUserPurchased } from "@/type/layout-config";
import { Scroll<PERSON><PERSON>, ScrollBar } from "@/ui/scroll-area";
import { useState } from "react";
import { useAsyncValue } from "react-router-dom";
import { TabContent } from "./components/tab-content";

export default function UserPurchased() {
  const { data } = useAsyncValue() as { data: LayoutConfigUserPurchased };

  const defaultIndex = data.tabs.defaultIndex;
  const tabs = data.tabs.items;

  const [activeKey, setActiveKey] = useState(defaultIndex);

  return (
    <PageWrapper>
      <NavigationBar canBack title="购买记录" />
      <PageMain needScroll={false} className="bg-scaffold-background">
        <div className="p-[15px] h-full flex flex-col">
          <ScrollArea>
            <div>
              <Tabs tabs={tabs} value={activeKey} onChange={setActiveKey} />
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
          <div className="flex-1 h-0">
            {tabs.map((tab, index) => (
              <TabContent
                key={index}
                index={index}
                isActive={index === activeKey}
                data={tab}
              />
            ))}
          </div>
        </div>
      </PageMain>
    </PageWrapper>
  );
}
