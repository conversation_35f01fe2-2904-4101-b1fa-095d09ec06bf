import { NavigationBar } from "@/components/navigation-bar";
import { PageMain } from "@/components/page-main";
import { PageWrapper } from "@/components/page-wrapper";
import { ScrollLoadData } from "@/components/scroll-load-data";
import { StateView } from "@/components/state-view";
import { UserAvatar } from "@/components/user-avatar";
import { UserInfo } from "@/components/work/user-info";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { usePageListRefresh } from "@/hooks/use-page-list-refresh";
import { useUserVip } from "@/hooks/user-vip";
import { getVisitorRecords, getVisitorSummary } from "@/lib/api/user";
import { PageParams } from "@/type";
import { Button } from "@/ui/button";
import { VipPermission } from "@/utils/enums";
import { formatTimeLine } from "@/utils/format-time-line";
import { useQuery } from "@tanstack/react-query";
import dayjs from "dayjs";

export default function Visitor() {
  const { hasPermission } = useUserVip();
  const { navigateRoute } = useNavigateRoute();

  const canAccessRecords = hasPermission(VipPermission.AccessRecords);

  const { data: visitorSummary, isLoading } = useQuery({
    queryKey: ["visitor-summary"],
    queryFn: getVisitorSummary,
    enabled: !canAccessRecords,
  });

  const queryKey = ["visitor-records"];

  const queryFn = (params: PageParams) => {
    return getVisitorRecords(params);
  };

  const { handleRefresh } = usePageListRefresh(queryKey);

  const handleOpenVip = () => {
    navigateRoute("/vip");
  };

  return (
    <PageWrapper className="bg-scaffold-background">
      <NavigationBar canBack title="访客记录" />
      <PageMain onRefresh={canAccessRecords ? handleRefresh : undefined}>
        {canAccessRecords ? (
          <ScrollLoadData queryKey={queryKey} queryFn={queryFn}>
            {(data) => (
              <div className="py-[15px] flex flex-col gap-[15px]">
                {data.map((item) => (
                  <div
                    key={item.user?.id}
                    className="p-[15px] bg-app-bar-background flex flex-col gap-[10px]"
                  >
                    <span className="text-hint-color text-[13px]">
                      {formatTimeLine(dayjs(item.cdate).valueOf())}
                    </span>
                    <UserInfo user={item.user!} size={47} />
                  </div>
                ))}
              </div>
            )}
          </ScrollLoadData>
        ) : (
          <StateView isLoading={isLoading}>
            <div className="px-[32px] py-[50px] flex flex-col gap-[25px]">
              <div className="flex flex-wrap gap-[25px] justify-center">
                {visitorSummary?.data?.userAvatars?.map((avatar) => (
                  <UserAvatar key={avatar} src={avatar} size={55} />
                ))}
              </div>
              <div className="text-[15px] text-center">
                过去有{visitorSummary?.data!.unreadCount ?? 0}人访问过您的主页
              </div>
              <Button
                variant="primary"
                size="lg"
                className="w-full"
                onClick={handleOpenVip}
              >
                开启VIP，查看哪些人看过你
              </Button>
            </div>
          </StateView>
        )}
      </PageMain>
    </PageWrapper>
  );
}
