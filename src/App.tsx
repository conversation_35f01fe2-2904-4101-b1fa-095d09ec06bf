import { RouterProvider } from "react-router-dom";
import { router } from "./router";
import { ThemeProvider } from "./provider/theme-provider";
import { QueryProvider } from "./provider/query-provider";
import { Fallback } from "./router/fallback";
import { Toast } from "antd-mobile";
import { UIKitProvider } from "@tencentcloud/uikit-base-component-react";
import { AliveScope } from "react-activation";

Toast.config({
  duration: 1500,
});

function App() {
  return (
    <div className="w-screen h-screen text-foreground bg-app-bar-background text-sm leading-normal">
      <ThemeProvider>
        <QueryProvider>
          <UIKitProvider language="zh-CN">
            <AliveScope>
              <RouterProvider router={router} fallbackElement={<Fallback />} />
            </AliveScope>
          </UIKitProvider>
        </QueryProvider>
      </ThemeProvider>
    </div>
  );
}

export default App;
