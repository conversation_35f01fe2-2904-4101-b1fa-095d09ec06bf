import { useContext } from "react";
import { ThemeProviderContext } from "./theme-provider";

export const useTheme = () => {
  const { theme } = useContext(ThemeProviderContext);

  if (theme === undefined)
    throw new Error("useTheme must be used within a ThemeProvider");

  if (theme === "system") {
    const systemTheme = window.matchMedia("(prefers-color-scheme: dark)")
      .matches
      ? "dark"
      : "light";

    return systemTheme;
  }

  return theme;
};
