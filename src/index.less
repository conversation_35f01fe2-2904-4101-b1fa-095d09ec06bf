:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

html,
body {
  overscroll-behavior: none;
}

// videojs 公共样式
.video-js {
  .vjs-control-bar {
    background: transparent;
  }

  .vjs-volume-panel {
    display: none;
  }

  .vjs-picture-in-picture-control {
    display: none;
  }
  .vjs-current-time {
    display: block;
  }

  .vjs-control {
    width: 3em;
  }

  .vjs-control:focus-visible {
    outline: none;
  }

  .vjs-menu-content li:focus-visible {
    outline: none;
  }

  .vjs-remaining-time {
    visibility: hidden;
  }

  .vjs-modal-dialog-content {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: pre-wrap;
  }

  .vjs-load-progress > div {
    background: #fff;
    border-radius: 10px;
  }

  .vjs-play-progress {
    background: linear-gradient(90deg, #ff3460 0%, #ff7b57 100%);
    border-radius: 10px;
  }

  .vjs-duration {
    display: block;
    position: absolute;
    top: 0;
    width: 3em; /* 总时长的预估宽度 */
    text-align: right;
  }
}

// large badge
.large-badge {
  .adm-badge-content {
    font-size: 12px;
    line-height: 14px;
    padding: 1px 5px;
  }
}

.ka-wrapper {
  height: 100%;
}

.ka-content {
  height: 100%;
}
