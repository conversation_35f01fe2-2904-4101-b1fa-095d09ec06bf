import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground rounded-full",
        primary:
          "bg-gradient text-white rounded-full disabled:text-white/50 disabled:bg-disabled-color disabled:bg-none",
        destructive: "bg-destructive text-destructive-foreground rounded-full",
        outline: "border border-input bg-app-bar-background rounded-full",
        secondary:
          "rounded-full bg-[#FF005C]/10 text-primary-foreground border-solid border border-[#FF005C]",
        ghost: "hover:bg-accent rounded-full",
        link: "text-primary underline-offset-4 rounded-full",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-[24px] px-[19px]",
        lg: "h-[50px] text-lg",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
