import * as React from "react";

import { cn } from "@/lib/utils";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full border-b border-input bg-transparent px-[5px] file:border-0 file:bg-transparent file:font-medium placeholder:text-input-placeholder focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 focus:border-[#FF005C]",
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Input.displayName = "Input";

export { Input };
