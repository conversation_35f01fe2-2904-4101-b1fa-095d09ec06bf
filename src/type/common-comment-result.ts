import { Resource } from "./resource";
import { User } from "./user";

// 评论结果
export interface CommonCommentResult {
  /**
   * 评论ID
   */
  id?: string;

  /**
   * 上级评论
   */
  parent?: CommonCommentResult;

  /**
   * 发布人
   */
  user?: User;

  /**
   * 文本内容
   */
  content?: string;

  /**
   * 语音
   */
  voice?: Resource;

  /**
   * 资源列表
   */
  resources?: Resource[];

  /**
   * 点赞数
   */
  likeNumber?: number;

  /**
   * 评论数
   */
  commentNumber?: number;

  /**
   * 发布时间
   */
  releaseTime?: string;

  /**
   * 子评论
   */
  children?: CommonCommentResult[];

  /**
   * 是否点赞该评论
   */
  hasLike?: boolean;

  /**
   * 艾特列表
   */
  ats?: { [key: string]: string };
}
