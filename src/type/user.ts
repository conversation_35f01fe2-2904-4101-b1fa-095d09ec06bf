import {
  UserExtent,
  UserGender,
  UserOnlineState,
  UserRole,
  UserSexualOrientation,
} from "@/utils/enums";

// 用户信息
export interface User {
  /** ID */
  id?: string;
  /** 手机号码 */
  phone?: string;
  /** 用户ID */
  userid?: number;
  /** 头像 */
  avatar?: string;
  /** 头像URL */
  avatarUrl?: string;
  /** 头像大图· */
  avatarBigImageUrl?: string;
  /** 昵称 */
  nickname?: string;
  /** 性别 */
  gender?: UserGender;
  /** 年龄 */
  age?: number;
  /** 生日 */
  birthday?: Date;
  /** 性取向 */
  sexualOrientation?: UserSexualOrientation;
  /** 角色 */
  role?: UserRole;
  /** 程度 */
  extent?: UserExtent;
  /** 在线状态 */
  onlineState?: UserOnlineState;
  /** 经纬度 */
  latitudeLongitude?: string;
  /** 真实地址 */
  realAddress?: string;
  /** 是否官方账号 */
  official?: boolean;
  /** 签名 */
  signature?: string;
  /** 豪气等级 */
  heroismGrade?: number;
  /** 魅力等级 */
  charmGrade?: number;
  /** 积分等级 */
  integralGrade?: number;
  /** 信用评分 */
  creditScore?: number;
  /** 是否VIP */
  vip?: boolean;
  /** VIP标识 */
  vipSign?: string;
  /** 是否实名认证 */
  realNameAuth?: boolean;
  /** 是否自拍认证 */
  selfAuth?: boolean;
  /** 粉丝数量 */
  fansNumber?: number;
  /** 关注数量 */
  followNumber?: number;
  /** 连续签到天数 */
  checkInStreak?: number;
  /** 是否代理人 */
  agent?: boolean;
  /** 是否合伙人 */
  copartner?: boolean;
  /** 最后上线时间 */
  lastOnlineTime?: string;
  /** 标签 */
  tags?: UserTag[];
  /** 是否存在违规记录 */
  existViolation?: boolean;
  /** 是否关注 */
  hasFollow?: boolean;
  /** 是否是主播账号 */
  liveAnchor?: boolean;
}

export type UserTag = {
  label: string;
  color: number;
  backgroundColor: number;
};
