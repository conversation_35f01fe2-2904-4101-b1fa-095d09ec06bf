import { UserOrderState } from "@/utils/enums";
import { User } from "./user";

/** 用户订单详细结果对象 */
export interface UserOrderDetailedResult {
  /** 订单ID */
  id?: string;

  /** 下单用户 */
  user?: User;

  /** 商家 */
  merchant?: User;

  /** 订单号 */
  orderNumber?: string;

  /** 第三方交易订单号 */
  thirdPartyOrderNumber?: string;

  /** 封面 */
  cover?: string;

  /** 标题 */
  title?: string;

  /** 内容 */
  content?: string;

  /** 单价 */
  singlePrice?: number;

  /** 购买数量 */
  number?: number;

  /** 运费 */
  freightPrice?: number;

  /** 总价 */
  totalPrice?: number;

  /** 折扣价格 */
  discountAmount?: number;

  /** 应支付金额 */
  shouldPayAmount?: number;

  /** 实际支付金额 */
  actualPayAmount?: number;

  /** 目标描述文本：交易、官方商城 */
  targetLabel?: string;

  /** 目标类型 */
  targetType?: number;

  /** 目标ID */
  targetId?: string;

  /** 快递单号 */
  trackingNumber?: string;

  /** 创建时间 */
  createDate?: Date;

  /** 支付时间 */
  payDate?: Date;

  /** 发货时间 */
  dispatchDate?: Date;

  /** 收货时间 */
  receiveDate?: Date;

  /** 自动收货时间 */
  autoReceiveDate?: Date;

  /** 订单失效时间 */
  expireDate?: Date;

  /** 订单状态 */
  state?: UserOrderState;

  /** 是否存在退款 */
  existRefund?: boolean;

  /** 收货人姓名 */
  receiveName?: string;

  /** 收货人手机 */
  receivePhone?: string;

  /** 收货人省份 */
  receiveProvince?: string;

  /** 收货人城市 */
  receiveCity?: string;

  /** 收货人区域 */
  receiveRegion?: string;

  /** 收货人详细地址 */
  receiveAddress?: string;

  /** 条目点击跳转页面 */
  targetUri?: string;

  /** 完整地址 */
  fullAddress?: string;

  /** 订单留言 */
  note?: string;
}
