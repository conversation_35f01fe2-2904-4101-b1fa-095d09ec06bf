import { LiveRoomType } from "@/utils/enums";
import { User } from "./user";

/** 直播间详细结果对象 */
export interface LiveRoomDetailedResult {
  /** 封面 */
  cover?: string;

  /** 直播间ID */
  id?: string;

  /** 直播间群ID */
  groupId?: string;

  /** 用户信息 */
  user?: User;

  /** 价格 */
  price?: number;

  /** 收费类型 */
  chargeType?: number;

  /** 收费描述 */
  chargeTips?: string;

  /** 直播间类型 */
  type?: LiveRoomType;

  /** 成员数量 */
  memberNumber?: number;

  /** 成员数量文本 */
  memberNumberText?: string;

  /** 播放地址 */
  playUrl?: {
    rtmp?: string;
    hls?: string;
    flv?: string;
  };

  /** token */
  token?: string;

  /** 是否已购买直播间 */
  hasBuy?: boolean;

  /** 直播间是否允许预览 */
  allowPreview?: boolean;

  /** 最大可预览时间 */
  maxPreviewTime?: number;

  /** 加入后的提示信息 */
  tips?: string[];

  /** 成员列表 */
  members?: User[];

  /** 禁止发言 */
  disabledChat?: boolean;

  /** 禁止发言提示信息 */
  disabledChatTips?: string;
}
