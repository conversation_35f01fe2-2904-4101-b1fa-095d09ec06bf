/** 用户自助解封信息对象 */
export interface UserUnbanInfoResult {
  /** 封禁内容 */
  content?: UserUnbanInfoResultContent[];
  /** 说明 */
  note?: string;
  /** 操作标题 */
  optTitle?: string;
  /** 是否可以申请解封 */
  canApply: boolean;
}

/** 封禁内容信息 */
interface UserUnbanInfoResultContent {
  /** 记录ID */
  id?: string;

  /** 用户ID */
  userId?: string;

  /** 配置ID */
  configId?: string[];

  /** 封禁内容 */
  content?: string;

  /** 解封时间 */
  recoveryDate?: Date;

  /** 封禁原因 */
  note?: string;
}
