import { CustomNodeDataType } from "@/utils/constant";
import { User } from "./user";

interface ICustomNodeDataType {
  type: CustomNodeDataType;
}

export interface ISystemNoticeNode extends ICustomNodeDataType {
  cover?: string;
  title?: string;
  subtitle?: string;
  target?: string;
  operator?: User;
}

export interface ITipsNode extends ICustomNodeDataType {
  canHidden?: boolean;
  content?: string;
  viewableUserList?: string[];
}

export interface IRedEnvelopeNode extends ICustomNodeDataType {
  id?: string;
  content?: string;
}

export interface IShareCardNode extends ICustomNodeDataType {
  label?: string;
  cover?: string;
  title?: string;
  subtitle?: string;
  targetUrl?: string;
}
