import { UserChatPermissionState } from "@/utils/enums";
import { User } from "./user";

/** 用户互动权限结果对象 */
export interface UserChatPermissionResult {
  /** 申请人信息 */
  applicantUser?: User;

  /** 响应人信息 */
  respondentUser?: User;

  /** 价格 */
  price?: number;

  /** 天数 */
  dayNumber?: number;

  /** 状态 */
  state?: UserChatPermissionState;

  /** 开启时间 */
  openDate?: string;

  /** 过期时间 */
  expireDate?: string;
}
