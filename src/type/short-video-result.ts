import { ShortVideoTagResult } from "./short-video-tag-result";
import { User } from "./user";

export interface ShortVideoResult {
  /** 短视频ID */
  id?: string;

  /** 发布人 */
  user?: User;

  /** 描述信息 */
  content?: string;

  /** 视频地址 */
  video?: string;

  /** 封面 */
  image?: string;

  /** 是否播放购买 */
  hasBuyPlayer?: boolean;

  /** 是否下载购买 */
  hasBuyDownload?: boolean;

  /** 播放价格 */
  playPrice?: number;

  /** 下载价格 */
  downloadPrice?: number;

  /** 地址 */
  address?: string;

  /** 是否精选 */
  featured?: boolean;

  /** 状态 */
  state?: number;

  /** 收益 */
  income?: number;

  /** 点赞数 */
  likeNumber?: number;

  /** 评论数 */
  commentNumber?: number;

  /** 阅读数 */
  readNumber?: number;

  /** 播放数量 */
  playNumber?: number;

  /** 下载数量 */
  downloadNumber?: number;

  /** 礼物数量 */
  giftNumber?: number;

  /** 分享数量 */
  shareNumber?: number;

  /** 购买数量 */
  buyNumber?: number;

  /** 收藏数量 */
  collectNumber?: number;

  /** 发布时间 */
  releaseDate?: string;

  /** 时长 */
  duration?: number;

  /** 是否点赞 */
  hasLike?: boolean;

  /** 是否收藏 */
  hasCollect?: boolean;

  /** 标签列表 */
  tags?: ShortVideoTagResult[];

  /** 提示信息 */
  tips?: string;
}
