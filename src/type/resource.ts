export interface Resource {
  /**
   * 资源ID
   */
  id?: string;

  /**
   * 资源URL
   */
  url?: string;

  /**
   * 图片列表
   */
  images?: ResourceImageGroup;

  /**
   * 资源名称
   */
  name?: string;

  /**
   * 资源后缀
   */
  suffix?: string;

  /**
   * 资源类型
   */
  contentType?: string;

  /**
   * 资源大小
   */
  size?: string;

  /**
   * 描述
   */
  note?: string;

  /**
   * 创建时间
   */
  cdate?: Date;

  /**
   * 时长
   */
  duration?: number;
}

// 资源图片组
interface ResourceImageGroup {
  /**
   * 原图
   */
  Original?: ResourceImage;

  /**
   * 大图
   */
  BigPicture?: ResourceImage;

  /**
   * 缩略图
   */
  Thumbnail?: ResourceImage;
}

// 图片文件
interface ResourceImage {
  /**
   * 图片类型
   */
  type?: ResourceImageType;

  /**
   * 图片URL
   */
  url?: string;
}

// 资源图片类型
type ResourceImageType = "Original" | "BigPicture" | "Thumbnail";
