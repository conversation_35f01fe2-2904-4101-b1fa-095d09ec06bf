import { User } from "./user";

/** 红包结果对象 */
export interface RedEnvelopeResult {
  /** 发红包的用户 */
  user?: User;

  /** 红包类型 */
  walletType?: number;

  /** 红包类型描述 */
  walletTypeLabel?: string;

  /** 红包总数 */
  number?: number;

  /** 红包金额 */
  availableAmount?: number;

  /** 已领取金额 */
  receivedAmount?: number;

  /** 已领取数量 */
  receivedNumber?: number;

  /** 领完时间 */
  receivedDate?: Date;

  /** 红包描述 */
  content?: string;

  /** 红包状态 */
  state?: number;

  /** 当前用户领取金额 */
  currentUserReceivedAmount?: number;

  /** 领取详情 */
  receiveDetailedList?: ReceiveDetailedList[];
}

/** 领取详细 */
export interface ReceiveDetailedList {
  /** 领取金额 */
  amount?: number;

  /** 领取时间 */
  date?: string;

  /** 领取用户 */
  user?: User;

  /** 标签 */
  label?: string;
}
