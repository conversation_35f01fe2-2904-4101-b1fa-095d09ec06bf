import { User } from "./user";
import { UserChargeConfigResult } from "./user-charge-config-result";

/** 用户聊天详细结果对象 */
export interface UserChatProfileResult {
  /** 用户对象 */
  user?: User;

  /** 是否禁用 */
  disabled?: boolean;

  /** 禁用提示 */
  disabledTips?: string;

  /** 聊天是否收费 */
  chatCharge?: boolean;

  /** 聊天收费配置 */
  chargeConfig?: UserChargeConfigResult;

  /** 收费提示信息 */
  chargeTips?: string;

  /** 新会话聊天提示信息 */
  newChatTips?: string[];
}
