import { UserExtent, UserRole, UserSexualOrientation } from "@/utils/enums";

/** 用户个人资料信息 */
export interface UserProfile {
  /** 头像URL */
  avatar?: string;

  /** 头像是否审核中 */
  hasAuditAvatar?: boolean;

  /** 昵称 */
  nickname?: string;

  /** 昵称是否审核中 */
  hasAuditNickname?: boolean;

  /** 性取向 */
  sexualOrientation?: UserSexualOrientation;

  /** 角色 */
  role?: UserRole;

  /** 程度 */
  extent?: UserExtent;

  /** 生日 */
  birthday?: Date;

  /** 个性签名 */
  signature?: string;

  /** 签名是否审核中 */
  hasAuditSignature?: boolean;

  /** 提示信息 */
  tips?: string;
}
