import { UserTransactionTenderState } from "@/utils/enums";
import { Resource } from "./resource";
import { User } from "./user";

/** 用户交易投标结果对象 */
export interface UserTransactionTenderResult {
  /** ID */
  id?: string;

  /** 发布人 */
  user?: User;

  /** 封面 */
  cover?: string;

  /** 内容 */
  content?: string;

  /** 语音 */
  voice?: Resource;

  /** 资源列表 */
  resources?: Resource[];

  /** 价格 */
  price?: number;

  /** 运费 */
  freightPrice?: number;

  /** 状态 */
  state?: UserTransactionTenderState;

  /** 创建时间 */
  cdate?: string;
}
