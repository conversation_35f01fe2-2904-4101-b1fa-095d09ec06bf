import { DynamicState } from "@/utils/enums";
import { Resource } from "./resource";
import { User } from "./user";
import { CommonCommentResult } from "./common-comment-result";
import { GiftGiverResult } from "./gift-giver-result";
import { GiftSummer } from "./gift-summary";

export interface DynamicResult {
  /**
   * 动态ID
   */
  id: string;

  /**
   * 发布人
   */
  user?: User;

  /**
   * 语音
   */
  voice?: Resource;

  /**
   * 公开文本内容
   */
  publicContent?: string;

  /**
   * 收费文本内容
   */
  paidContent?: string;

  /**
   * 资源列表
   */
  resources?: Resource[];

  /**
   * 发布人地址
   */
  address?: string;

  /**
   * 是否匿名
   */
  anonymous?: boolean;

  /**
   * 点赞用户列表
   */
  likes?: User[];

  /**
   * 当前登录用户是否对该动态点赞
   */
  hasLike: boolean;

  /**
   * 点赞数
   */
  likeNumber: number;

  /**
   * 评论数
   */
  commentNumber: number;

  /**
   * 阅读数
   */
  readNumber: number;

  /**
   * 礼物数
   */
  giftNumber: number;

  /**
   * 分享数
   */
  shareNumber: number;

  /**
   * 动态发布时间
   */
  releaseTime: string;

  /**
   * 动态状态
   */
  state?: DynamicState;

  /**
   * 是否精选动态
   */
  featured?: boolean;

  /**
   * 是否置顶
   */
  stickie?: boolean;

  /**
   * 付费信息
   */
  charge?: Charge;

  /**
   * 评论列表
   */
  comments?: CommonCommentResult[];

  /**
   * 是否收藏
   */
  hasCollect?: boolean;

  /**
   * 赠送礼物的人数
   */
  giftGiverCount?: number;

  /**
   * 礼物赠送人
   */
  giftGiver?: GiftGiverResult[];

  /**
   * 动态提及结果对象
   */
  mention?: Mention;

  /**
   * 礼物汇总
   */
  giftSummary?: GiftSummer[];

  /**
   * 艾特列表
   */
  ats?: { [key: string]: string };
}

// 动态收费配置
export interface Charge {
  /**
   * 支付方式
   */
  payWay?: number;

  /**
   * 支付方式文本描述
   */
  payWayText?: string;

  /**
   * 价格
   */
  price?: number;

  /**
   * 需要付费的资源列表
   */
  resources?: string[];

  /**
   * 是否已购买
   */
  buy?: boolean;
}

// 提及信息
interface Mention {
  /**
   * 目标ID
   */
  targetId?: string;

  /**
   * 目标类型
   */
  targetType?: number;

  /**
   * 目标标题
   */
  targetTitle?: string;

  /**
   * 目标URL
   */
  targetUrl?: string;

  /**
   * 封面
   */
  cover?: string;

  /**
   * 标题
   */
  title?: string;

  /**
   * 子标题
   */
  subtitle?: string;
}
