import { UserTransactionState, UserTransactionType } from "@/utils/enums";
import { Resource } from "./resource";
import { User } from "./user";

export interface UserTransactionResult {
  /// ID
  id?: string;

  /// 语音
  voice?: Resource;

  /// 资源列表
  resources?: Resource[];

  /// 用户
  user?: User;

  /// 封面
  cover?: string;

  /// 标题
  title?: string;

  /// 价格
  price?: number;

  /// 运费
  freightPrice?: number;

  /// 保证金
  deposit?: number;

  /// 库存
  stock?: number;

  /// 地址
  address?: string;

  /// 内容
  content?: string;

  /// 类型
  type?: UserTransactionType;

  /// 状态
  state?: UserTransactionState;

  /// 是否点赞
  hasLike?: boolean;

  /// 点赞数量
  likeNumber?: number;

  /// 评论数量
  commentNumber?: number;

  /// 阅读数量
  readNumber?: number;

  /// 创建时间
  cdate?: string;
}
