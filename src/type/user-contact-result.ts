import { UserContactState } from "@/utils/enums";

/** 用户联系方式结果对象 */
export interface UserContactResult {
  /** 联系方式ID */
  id?: string;

  /** 类型 */
  type?: number;

  /** 类型文本 */
  typeLabel?: string;

  /** 目标值 */
  targetValue?: string;

  /** 价格 */
  price?: number;

  /** 描述 */
  note?: string;

  /** 是否购买 */
  hasBuy?: boolean;

  /** 提示信息 */
  tips?: string;

  /** 状态 */
  state?: UserContactState;
}
