import { MedalResult } from "./medal-result";
import { UserContactResult } from "./user-contact-result";

/** 用户扩展结果对象 */
export interface UserExpandResult {
  /** 礼物汇总 */
  giftSummary?: UserExpandGiftSummary[];

  /** 勋章 */
  medals?: MedalResult[];

  /** 分组信息 */
  groups?: UserExpandGroup[];

  /** 联系方式 */
  contact?: UserContactResult;
}

/** 礼物汇总 */
interface UserExpandGiftSummary {
  /** 礼物图片 */
  image?: string;

  /** 礼物数量 */
  number?: number;
}

/** 用户扩展组 */
interface UserExpandGroup {
  /** 组标题 */
  title?: string;

  /** 目标链接 */
  uri?: string;

  /** 链接显示文本 */
  linkTips?: string;

  /** 子条目 */
  items?: UserExpandGroupItem[];
}

/** 用户扩展组项目 */
interface UserExpandGroupItem {
  /** 封面图片 */
  cover?: string;
  /** 标题 */
  title?: string;
  /** 内容 */
  content?: string;
  /** 链接 */
  uri?: string;
}
