import { UserOrderRefundState } from "@/utils/enums";
import { User } from "./user";
import { UserOrderDetailedResult } from "./user-order-detailed-result";

/** 用户订单售后结果对象 */
export interface UserOrderRefundResult {
  /** ID */
  id?: string;

  /** 订单结果对象 */
  order?: UserOrderDetailedResult;

  /** 购买用户 */
  user?: User;

  /** 商家 */
  merchant?: User;

  /** 审核人ID */
  auditUserId?: string;

  /** 申请金额 */
  applyAmount?: number;

  /** 退款金额 */
  refundAmount?: number;

  /** 申请原因 */
  applyReason?: string;

  /** 审核原因 */
  auditReason?: string;

  /** 自动审核时间 */
  autoReasonDate?: Date;

  /** 当前状态 */
  state?: UserOrderRefundState;

  /** 创建时间 */
  cdate?: Date;
}
