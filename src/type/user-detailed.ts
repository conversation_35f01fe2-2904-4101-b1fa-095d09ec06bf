import { Resource } from "./resource";
import { User } from "./user";
import { UserAlbum } from "./user-album";
import { UserChargeConfigResult } from "./user-charge-config-result";

/** 用户详细 */
export interface UserDetailed extends User {
  /** 封面 */
  cover?: Resource;

  /** 收费配置 */
  chargeConfig?: UserChargeConfigResult;

  /** 用户相册 */
  album?: UserAlbum;

  /** 当前用户是否将对方加入黑名单 */
  block?: boolean;
}
