import { GiftType } from "@/utils/enums";

/** 礼物结果对象 */
export interface GiftResult {
  /** ID */
  id?: string;

  /** 名称 */
  name?: string;

  /** 图标 */
  image?: string;

  /** 标识 */
  sign?: string;

  /** 动画 */
  animation?: string;

  /** 动画类型 */
  animationType?: number;

  /** 原价 */
  originalPrice?: number;

  /** 现价 */
  presentPrice?: number;

  /** VIP */
  vip?: boolean;

  /** 类型 */
  type?: GiftType;
}
