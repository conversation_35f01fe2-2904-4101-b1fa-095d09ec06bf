import { ShareType } from "@/utils/enums";

type BaseConfig = {
  label: string;
  uri: string;
};

export type BaseUrlConfig = {
  label: string;
  url: string;
  params?: Record<string, any>;
};

export type Action = {
  uri: string;
  icon: {
    light: string;
    dark: string;
    width: number;
  };
};

type SearchBar = {
  content?: string;
  action?: Action[];
};

type NavigationItem = BaseConfig & {
  tips?: string;
};

type BottomNavigationBar = {
  /** 默认打开的 tab */
  defaultIndex: number;
  items: NavigationItem[];
};

/**
 * LayoutConfigKey.Home
 */
export interface LayoutConfigHome {
  searchBar: SearchBar;
  bottomNavigationBar: BottomNavigationBar;
}

/**
 * LayoutConfigKey.Community
 */
export interface LayoutConfigCommunity {
  tabs: {
    defaultIndex: number;
    items: BaseConfig[];
  };
}

type FilterValue = {
  label: string;
  value?: string;
};

export type Filter = {
  key: string;
  label: string;
  value: FilterValue[];
};

export type CommunityItemType = BaseUrlConfig & {
  custom?: {
    showBanner?: boolean;
    showTopic?: boolean;
  };
  disabledFilter?: boolean;
};

export type CommunityTabType = CommunityItemType & { key: number };

/**
 * LayoutConfigKey.DynamicHome
 */
export interface LayoutConfigDynamicHome {
  filter?: Filter[];
  tabs: {
    defaultIndex: number;
    items: CommunityItemType[];
  };
}

/**
 * LayoutConfigKey.ChatRoomHome
 */
export interface LayoutConfigChatRoomHome {
  filter?: Filter[];
  tabs: {
    defaultIndex: number;
    items: CommunityItemType[];
  };
}

/**
 * LayoutConfigKey.UserPlazaHome
 */
export interface LayoutConfigUserPlazaHome {
  filter?: Filter[];
  tabs: {
    defaultIndex: number;
    items: CommunityItemType[];
  };
}

/**
 * LayoutConfigKey.UserTransactionHome
 */
export interface LayoutConfigUserTransactionHome {
  filter?: Filter[];
  tabs: {
    defaultIndex: number;
    items: CommunityItemType[];
  };
}

/**
 * LayoutConfigKey.Topic
 */
export interface LayoutConfigTopic {
  search: boolean;
  tabs: {
    defaultIndex: number;
    items: BaseUrlConfig[];
  };
}

/**
 * LayoutConfigKey.TopicDetail
 */
export interface LayoutConfigTopicDetail {
  tabs: {
    defaultIndex: number;
    items: BaseUrlConfig[];
  };
}

/**
 * LayoutConfigKey.LiveHome
 */
export interface LayoutConfigLiveHome {
  tabs: {
    defaultIndex: number;
    items: BaseUrlConfig[];
  };
}

/**
 * LayoutConfigKey.LivePlayback
 */
export interface LayoutConfigLivePlayback {
  tabs: {
    defaultIndex: number;
    items: BaseUrlConfig[];
  };
}

/**
 * LayoutConfigKey.Message
 */
export interface LayoutConfigMessage {
  tabs: {
    defaultIndex: number;
    items: BaseConfig[];
  };
}

/**
 * LayoutConfigKey.Video
 */
export interface LayoutConfigVideo {
  filter: Filter[];
  tabs: {
    defaultIndex: number;
    items: Array<
      BaseUrlConfig & {
        children: BaseUrlConfig[];
      }
    >;
  };
}

/**
 * LayoutConfigKey.ShortVideo
 */
export interface LayoutConfigShortVideo {
  filter: Filter[];
  tabs: {
    defaultIndex: number;
    items: Array<BaseUrlConfig & { disabledFilter: boolean }>;
  };
}

/**
 * LayoutConfigKey.UserOrderHome
 */
export interface LayoutConfigUserOrderHome {
  tabs: {
    defaultIndex: number;
    items: BaseUrlConfig[];
  };
}

/**
 * LayoutConfigKey.MerchantOrderHome
 */
export interface LayoutConfigMerchantOrderHome {
  tabs: {
    defaultIndex: number;
    items: BaseUrlConfig[];
  };
}

/**
 * LayoutConfigKey.UserOrderRefundHome
 */
export interface LayoutConfigUserOrderRefundHome {
  tabs: {
    defaultIndex: number;
    items: BaseUrlConfig[];
  };
}

/**
 * LayoutConfigKey.UserPublish
 */
export interface LayoutConfigUserPublish {
  tabs: {
    defaultIndex: number;
    items: Array<BaseUrlConfig & { custom: { widget: string } }>;
  };
}

/**
 * LayoutConfigKey.UserPurchased
 */
export interface LayoutConfigUserPurchased {
  tabs: {
    defaultIndex: number;
    items: BaseUrlConfig[];
  };
}

export type SettingItem = {
  uri: string;
  title: string;
  subtitle: string;
  icon: {
    dark: string;
    light: string;
  };
};

/**
 * LayoutConfigKey.Setting
 */
export interface LayoutConfigSetting {
  items: SettingItem[];
}

/**
 * LayoutConfigKey.Exchange
 */
export interface LayoutConfigExchange {
  items: string[];
}

export type ShareItem = {
  icon?: {
    light: string;
    dark: string;
  };
  label?: string;
  type: ShareType;
};

/**
 * LayoutConfigKey.Share
 */
export interface LayoutConfigShare {
  items: ShareItem[];
}

/**
 * LayoutConfigKey.UserSearchDetailed
 */
export interface LayoutConfigUserSearchDetailed {
  tabs: {
    defaultIndex: number;
    items: Array<BaseUrlConfig & { custom: { widget: string } }>;
  };
}

/**
 * LayoutConfigKey.UserLevelRanking
 */
export interface LayoutConfigUserLevelRanking {
  tabs: {
    defaultIndex: number;
    items: Array<{
      label: string;
      tabs: {
        defaultIndex: number;
        items: BaseUrlConfig[];
      };
    }>;
  };
}

export type ChatMoreItem = {
  id: string;
  label: string;
  icon: {
    light: string;
    dark: string;
    width: number;
  };
};

/**
 * LayoutConfigKey.Chat
 */
export interface LayoutConfigChat {
  systemImUserList: string[];
  more: {
    c2c: ChatMoreItem[];
    group: ChatMoreItem[];
  };
}

/**
 * LayoutConfigKey.RedEnvelopePublish
 */
export interface LayoutConfigRedEnvelopePublish {
  c2c: {
    defaultParams: {
      number: number;
      scene: number;
      type: number;
      walletType: number;
    };
  };
  group: {
    defaultParams: {
      scene: number;
      type: number;
      walletType: number;
    };
  };
  defaultContent: string;
}

/**
 * LayoutConfigKey.DynamicPublish
 */
export interface LayoutConfigDynamicPublish {
  publicContent: {
    hint: string;
    minLines: number;
  };
  paidContent: {
    hint: string;
    minLines: number;
  };
  ops: {
    sign: string;
    title?: string;
    icon?: { dark: string; light: string; width: number; height: number };
  }[];
  payWay: { label: string; value: string }[];
}
