import { User } from "./user";

export interface MindWallResult {
  // ID
  id?: string;

  // 收礼人
  user?: User;

  // 赠礼人
  giverUser?: User;

  // 标题
  title?: string;

  // 图标
  resource?: string;

  // 赠送数量
  number?: number;

  // 总价
  totalPrice?: number;

  // 单价
  unitPrice?: number;

  // 内容
  content?: string;

  // 是否点赞
  hasLike?: boolean;

  // 点赞数量
  likeNumber?: number;

  // 评论数量
  commentNumber?: number;

  // 阅读数量
  readNumber?: number;

  // 心意时间
  cdate?: string;
}
