import { LiveRoomType } from "@/utils/enums";
import { User } from "./user";

// 直播房间简单数据对象
export interface LiveRoomSimpleResult {
  /// 直播间ID
  id?: string;

  /// 用户信息
  user?: User;

  /// 封面
  cover?: string;

  /// 描述
  note?: string;

  /// 价格
  price?: number;

  /// 收费类型
  chargeType?: number;

  /// 收费描述
  chargeTips?: string;

  /// 直播间类型
  type?: LiveRoomType;

  /// 成员数量
  memberNumber?: number;

  memberText?: string;

  /// 位置信息
  address?: string;

  /// 播放地址
  playUrl?: string;
}
