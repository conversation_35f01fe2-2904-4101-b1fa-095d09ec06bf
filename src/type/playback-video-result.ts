import { GiftGiverResult } from "./gift-giver-result";
import { GiftSummer } from "./gift-summary";
import { User } from "./user";

// 视频结果对象
export interface PlaybackVideoResult {
  // 视频ID
  id?: string;

  // 视频标题
  title?: string;

  // 视频内容
  content?: string;

  // 封面
  cover?: string;

  // 时常
  duration?: number;

  // 播放价格
  playPrice?: number;

  // 下载价格
  downloadPrice?: number;

  // 是否精选
  featured?: boolean;

  // 视频状态
  state?: number;

  // 总收益
  income?: number;

  // 喜欢数量
  likeNumber?: number;

  // 评论数量
  commentNumber?: number;

  // 阅读数量
  readNumber?: number;

  // 播放数量
  playNumber?: number;

  // 下载数量
  downloadNumber?: number;

  // 礼物数量
  giftNumber?: number;

  // 分享数量
  shareNumber?: number;

  // 购买数量
  buyNumber?: number;

  // 发布时间
  releaseDate?: string;

  // 发布用户
  user?: User;

  // 是否点赞
  hasLike?: boolean;

  // 是否收藏
  hasCollection?: boolean;

  // 是否购买播放
  hasBuyPlayer?: boolean;

  // 是否购买下载
  hasBuyDownload?: boolean;

  // 是否隐藏
  hidden?: boolean;

  // 是否置顶
  top?: boolean;

  // 视频资源
  resource?: string;

  // 当前是否使用的VIP特权播放
  vipPrivilegePlay?: boolean;

  // VIP播放特权可使用总数
  vipPlayerNumber?: number;

  // VIP播放特权今日剩余次数
  vipPlayerTodayRemainingNumber?: number;

  // 收到的礼物总数
  giftGiverCount?: number;

  // 礼物赠送人
  giftGiver?: GiftGiverResult[];

  // 礼物汇总
  giftSummary?: GiftSummer[];
}
