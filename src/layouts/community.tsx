import { <PERSON><PERSON><PERSON><PERSON>, Sc<PERSON>Bar } from "@/ui/scroll-area";
import { Outlet, useLocation } from "react-router-dom";
import { LayoutConfigCommunity } from "@/type/layout-config";
import { getRoute } from "@/router/route-map";
import { Search } from "@/pages/home/<USER>/search";
import { RouterTabs } from "@/pages/home/<USER>/router-tabs";
import { useQuery } from "@tanstack/react-query";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { layoutConfigOptions } from "@/utils/query-options";

export default function CommunityLayout() {
  const { data: config } = useQuery(
    layoutConfigOptions(LayoutConfigKey.Community)
  );

  const data = config?.data as LayoutConfigCommunity;

  const { pathname } = useLocation();

  const defaultIndex = pathname === "/trade" ? 4 : 1;

  const tabs =
    data?.tabs?.items?.map((item) => {
      return {
        title: item.label,
        to: getRoute(item.uri),
      };
    }) || [];

  return (
    <div className="h-full flex flex-col">
      <div className="mx-[15px] pt-[8px] pb-[4px]">
        <Search defaultIndex={defaultIndex} />
      </div>
      <div className="mx-[15px]">
        <ScrollArea>
          <div>
            <RouterTabs tabs={tabs} />
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>
      <div className="flex-1 h-0">
        <Outlet />
      </div>
    </div>
  );
}
