import { ThemeIcon } from "@/components/theme-icon";
import { confirmDialog } from "@/components/work/confirm-dialog";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useNavigateRoute } from "@/hooks/use-navigate-route";
import { ApiResponse } from "@/lib/request";
import { getRoute } from "@/router/route-map";
import { LayoutConfigHome } from "@/type/layout-config";
import { Badge } from "antd-mobile";
import { Link, Outlet, useAsyncValue, useLocation } from "react-router-dom";
import { StoreName, TUIStore } from "@tencentcloud/chat-uikit-engine";
import { useEffect, useState } from "react";

// 底部导航支持的配置项
const config = [
  {
    includes: ["/video"],
    to: "/video",
    icon: "video",
  },
  {
    includes: [
      "/community",
      "/dynamic",
      "/mind-wall",
      "/chat-room",
      "/user-plaza",
      "/trade",
    ],
    to: "/community",
    icon: "community",
  },
  {
    includes: ["/live"],
    to: "/live",
    icon: "live",
  },
  {
    includes: ["/message", "/im", "/group"],
    to: "/message",
    icon: "message",
  },
  {
    includes: ["/my"],
    to: "/my",
    icon: "my",
  },
];

export default function HomeLayout() {
  const [totalUnreadCount, setTotalUnreadCount] = useState(0);

  const { pathname } = useLocation();
  const { navigateRoute } = useNavigateRoute();
  const { data: userInfo } = useFullUserInfo();
  const { data } = useAsyncValue() as ApiResponse<LayoutConfigHome>;

  const navigationConfig =
    data?.bottomNavigationBar?.items?.map((item) => {
      const { label, uri, tips } = item;
      const target = config.find(
        (configItem) => configItem.to === getRoute(uri)
      );

      return {
        name: label,
        includes: target?.includes ?? [],
        to: target?.to ?? "",
        icon: target?.icon ?? "",
        tips,
      };
    }) ?? [];

  useEffect(() => {
    const onTotalUnreadCount = (unreadCount: number) => {
      setTotalUnreadCount(unreadCount);
    };

    TUIStore.watch(StoreName.CONV, {
      totalUnreadCount: onTotalUnreadCount,
    });

    return () => {
      TUIStore.unwatch(StoreName.CONV, {
        totalUnreadCount: onTotalUnreadCount,
      });
    };
  }, []);

  return (
    <div className="h-screen w-full flex flex-col overflow-auto">
      <div className="flex-1 h-0">
        <Outlet />
      </div>
      <div className="flex-none text-xs flex pt-[10px] pb-[15px] bg-bottom-navigation border-t border-solid border-[#ECECEC] dark:border-[#3A3A3A]">
        {navigationConfig.map((item) => (
          <Link
            key={item.to}
            to={item.to}
            className="flex-1 flex flex-col items-center"
            onClick={async (e) => {
              if (item.tips) {
                e.preventDefault();

                const cacheValue = localStorage.getItem(
                  `HOME_BOTTOM_TIPS_${userInfo?.id}:${item!.to}`
                );

                if (!cacheValue) {
                  const result = await confirmDialog.show(item.tips);

                  if (result) {
                    localStorage.setItem(
                      `HOME_BOTTOM_TIPS_${userInfo?.id}:${item!.to}`,
                      "1"
                    );
                    navigateRoute(item.to);
                  }
                } else {
                  navigateRoute(item.to);
                }
              }
            }}
          >
            <Badge
              className="large-badge"
              style={{ "--top": "6px", "--right": "2px" }}
              content={
                item.to === "/message"
                  ? totalUnreadCount || undefined
                  : undefined
              }
            >
              <ThemeIcon
                name={item.icon}
                w={32}
                h={32}
                className={item.includes.includes(pathname) ? "hidden" : ""}
              />
              <ThemeIcon
                name={`${item.icon}-selected`}
                w={32}
                h={32}
                className={!item.includes.includes(pathname) ? "hidden" : ""}
              />
            </Badge>
            <span
              className={
                !item.includes.includes(pathname)
                  ? "text-bottom-navigation-foreground"
                  : "text-bottom-navigation-selected-foreground"
              }
            >
              {item.name}
            </span>
          </Link>
        ))}
      </div>
    </div>
  );
}
