import {
  darkHomeTopBackground,
  lightHomeTopBackground,
} from "@/components/image-list";
import { useTheme } from "@/provider/useTheme";
import { AspectRatio } from "@/ui/aspect-ratio";
import classNames from "classnames";
import { Outlet } from "react-router-dom";

export default function HomeBackgroundLayout() {
  const theme = useTheme();
  const bgImg =
    theme === "light" ? lightHomeTopBackground : darkHomeTopBackground;

  return (
    <div className="h-full w-full relative dark:bg-scaffold-background">
      <div className="absolute top-0 w-full">
        <AspectRatio
          ratio={750 / 566}
          className={classNames("top-0 absolute w-full bg-cover bg-no-repeat")}
          style={{
            backgroundImage: `url(${bgImg})`,
          }}
        />
        <div className="home-bg w-full h-full top-0 left-0 absolute" />
      </div>
      <div className="relative w-full h-full">
        <Outlet />
      </div>
    </div>
  );
}
