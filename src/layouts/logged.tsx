import {
  RtcCallModal,
  RtcCallModalRef,
} from "@/components/work/rtc-call-modal/rtc-call-modal";
import { useAmapLocation } from "@/hooks/use-amap-location";
import { useFullUserInfo } from "@/hooks/use-full-user-info";
import { useGiftList } from "@/hooks/use-gift-list";
import { useImLogin } from "@/hooks/use-im-login";
import { useUserMenus } from "@/hooks/use-user-menus";
import { useUserWallet } from "@/hooks/use-user-wallet";
import { useUserVip } from "@/hooks/user-vip";
import { SpinLoading } from "antd-mobile";
import { Outlet } from "react-router-dom";
import { useRef, useState } from "react";
import AgoraRTC, { AgoraRTCProvider } from "agora-rtc-react";
import { useSystemMounts } from "@/hooks/use-system-mounts";
import { useSystemJoinSpecial } from "@/hooks/use-system-join-special";

export default function LoggedLayout() {
  const rtcCallModalRef = useRef<RtcCallModalRef>(null);
  const [client] = useState(() =>
    AgoraRTC.createClient({ mode: "rtc", codec: "vp8" })
  );

  const { isLoading: fullUserInfoLoading } = useFullUserInfo();
  const { isLoading: userVipLoading } = useUserVip();
  const { loading: imLoginLoading } = useImLogin(rtcCallModalRef);
  useUserWallet();
  useUserMenus();
  useGiftList();
  useAmapLocation();
  useSystemMounts();
  useSystemJoinSpecial();

  if (fullUserInfoLoading || userVipLoading || imLoginLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center text-[#9d64ff]">
        <SpinLoading color="currentColor" style={{ "--size": "32px" }} />
      </div>
    );
  }

  return (
    <AgoraRTCProvider client={client}>
      <Outlet />
      <RtcCallModal ref={rtcCallModalRef} />
    </AgoraRTCProvider>
  );
}
