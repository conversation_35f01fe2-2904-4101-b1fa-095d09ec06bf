import {
  darkLoginBackground,
  lightLoginBackground,
} from "@/components/image-list";
import { useTheme } from "@/provider/useTheme";
import { Outlet } from "react-router-dom";

export default function LoginLayout() {
  const theme = useTheme();

  const bgImg = theme === "light" ? lightLoginBackground : darkLoginBackground;

  return (
    <div className="w-full h-screen overflow-y-auto">
      <div
        className="bg-cover bg-no-repeat h-full w-full"
        style={{
          backgroundImage: `url(${bgImg})`,
        }}
      >
        <Outlet />
      </div>
    </div>
  );
}
