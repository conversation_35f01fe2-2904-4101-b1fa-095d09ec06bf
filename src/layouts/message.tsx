import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>B<PERSON> } from "@/ui/scroll-area";
import { Outlet } from "react-router-dom";
import { LayoutConfigMessage } from "@/type/layout-config";
import { getRoute } from "@/router/route-map";
import { Search } from "@/pages/home/<USER>/search";
import { RouterTabs } from "@/pages/home/<USER>/router-tabs";
import { useMutation, useQuery } from "@tanstack/react-query";
import { layoutConfigOptions } from "@/utils/query-options";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { fullUserInfoKey, useFullUserInfo } from "@/hooks/use-full-user-info";
import { UserOnlineState, userOnlineStateConfig } from "@/utils/enums";
import { ArrowIcon } from "@/components/svg-icon";
import { SelectPopup } from "@/components/select-popup";
import { updateUserOnlineState } from "@/lib/api/user";
import { Toast } from "antd-mobile";
import { queryClient } from "@/provider/query-client";

export default function MessageLayout() {
  const { data: config } = useQuery(
    layoutConfigOptions(LayoutConfigKey.Message)
  );

  const { data: userInfo } = useFullUserInfo();

  const userOnlineState =
    userOnlineStateConfig[
      (userInfo?.onlineState ?? UserOnlineState.online) as UserOnlineState
    ];

  const data = config?.data as LayoutConfigMessage;

  const { mutateAsync } = useMutation({
    mutationFn: updateUserOnlineState,
  });

  const handleOnlineStateChange = async (value: UserOnlineState) => {
    Toast.show({
      icon: "loading",
      content: "操作中...",
      duration: 0,
    });

    const { ok, data: resData } = await mutateAsync({
      onlineState: value,
    });

    if (ok && resData) {
      queryClient.setQueryData(fullUserInfoKey, () => {
        return {
          data: {
            ...resData,
            onlineState: value,
          },
        };
      });
    }
  };

  const tabs =
    data?.tabs?.items?.map((item) => {
      return {
        title: item.label,
        to: getRoute(item.uri),
      };
    }) || [];

  return (
    <div className="h-full flex flex-col">
      <div className="mx-[15px] pt-[8px] pb-[4px]">
        <Search />
      </div>
      <div className="mx-[15px] flex items-center gap-[15px]">
        <ScrollArea className="flex-1 w-0">
          <div>
            <RouterTabs tabs={tabs} />
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
        <SelectPopup
          options={Object.entries(userOnlineStateConfig).map(([key, item]) => ({
            title: (
              <div className="flex gap-[5px] items-center justify-center">
                <span
                  className="w-[8px] h-[8px] rounded-full"
                  style={{
                    backgroundColor: item.color,
                  }}
                />
                {item.label}
              </div>
            ),
            value: item.label,
            onClick: () => handleOnlineStateChange(Number(key)),
          }))}
        >
          <div className="bg-black/30 rounded-[15px] px-[6px] py-[4px] flex gap-[5px] items-center">
            <span
              className="w-[8px] h-[8px] rounded-full"
              style={{
                backgroundColor: userOnlineState.color,
              }}
            />
            <span className="text-[12px] text-white">
              {userOnlineState.label}
            </span>
            <ArrowIcon size={12} className="rotate-90" />
          </div>
        </SelectPopup>
      </div>
      <div className="flex-1 h-0">
        <Outlet />
      </div>
    </div>
  );
}
