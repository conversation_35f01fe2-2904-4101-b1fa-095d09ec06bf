import { useEffect } from "react";
import { Outlet } from "react-router-dom";
import { SpinLoading } from "antd-mobile";
import { useUserLevelConfig } from "@/hooks/use-user-level-config";
import { useConfigList } from "@/hooks/use-config-list";
import { useUploadInfo } from "@/hooks/use-upload-info";
import { useDefaultResource } from "@/hooks/use-default-avatar";
import { useCamouflageState } from "@/store/camouflage-state";
import { CAMOUFLAGE_PASSWORD } from "@/type/cache";
import Camouflage from "@/pages/camouflage";

export default function GlobalLayout() {
  const { camouflageState } = useCamouflageState();

  // 处理伪装应用功能
  const password = localStorage.getItem(CAMOUFLAGE_PASSWORD);

  useEffect(() => {
    // app 拉起初始化
    if (window.OpenInstall) {
      new window.OpenInstall({
        appKey: import.meta.env.VITE_OPEN_INSTALL_KEY,
        onready: function () {
          window.wakeupOrInstall = this.wakeupOrInstall;
        },
      });
    }
  }, []);

  // 初始化系统配置项
  const { isLoading: configLoading } = useConfigList();
  // 初始化用户等级配置
  useUserLevelConfig();
  useUploadInfo();
  useDefaultResource();

  if (configLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center text-[#9d64ff]">
        <SpinLoading color="currentColor" style={{ "--size": "32px" }} />
      </div>
    );
  }

  if (password && !camouflageState.state) {
    return <Camouflage />;
  }

  return <Outlet />;
}
