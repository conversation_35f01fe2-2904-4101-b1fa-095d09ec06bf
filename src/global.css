@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --scaffold-background: 210 22% 96%;
    --foreground: 0 0% 0%;

    --hint-color: 0 0% 40%;

    --primary: 335 100% 98%;
    --primary-foreground: 338 100% 50%;

    --input: 228 15% 26%;
    --input-placeholder: 231 9% 48%;

    --secondary-foreground: 238 15% 40%;

    --bottom-navigation: 0 0 100%;
    --bottom-navigation-foreground: 0 0 65%;
    --bottom-navigation-selected-foreground: 271 59% 34%;

    --filter-tab-foreground: 210 22% 55%;
    --filter-tab: 0 0 100%;

    --filter-tab-active: 260 100% 91%;
    --filter-tab-active-foreground: 257 58% 58%;

    --destructive: 0 100% 50%;

    --app-bar-background: 0 0% 100%;

    --divider: #dadada;
    --disabled-color: #d8d8d8;
  }

  .dark {
    --scaffold-background: 218 47% 7%;
    --foreground: 0 0% 100%;

    --hint-color: 210 2.99% 73.73%;

    --primary-foreground: 338 100% 50%;
    --primary: 309 41% 13%;

    --bottom-navigation: 217 36% 10%;
    --bottom-navigation-foreground: 217 7% 37%;
    --bottom-navigation-selected-foreground: 272 100% 56%;

    --filter-tab-foreground: 219 100% 90%;
    --filter-tab: 0 0 100%/20%;

    --app-bar-background: 219 33% 10%;

    --divider: #2b2b2b;
    --disabled-color: #323741;
  }
}

@layer utilities {
  .home-bg {
    background: linear-gradient(180deg, transparent 0%, #ffffff 100%);
  }

  .dark .home-bg {
    background: linear-gradient(
      180deg,
      transparent 0%,
      hsl(var(--scaffold-background)) 100%
    );
  }

  .bg-gradient {
    /** primary 渐变背景色 */
    background: linear-gradient(90deg, #ff3460 0%, #ff7b57 100%);
  }

  .bg-black-room-gradient {
    background: linear-gradient(
      215deg,
      hsl(276 100% 69%) 0%,
      hsl(227 100% 62%) 100%
    );
  }

  .filter-tab-bg-arrow::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: #dfcfff transparent transparent transparent;
  }

  .mind-wall-item-bg {
    background: linear-gradient(130deg, #9406ff 0%, #4e59ff 100%);
  }

  .dark .mind-wall-item-bg {
    background: linear-gradient(
      180deg,
      #03001f 0%,
      rgba(34, 19, 45, 0.98) 100%
    );
  }

  .mind-wall-item-message-bg {
    background: linear-gradient(
      220deg,
      hsl(13, 100%, 67%, 0.48) 0%,
      hsl(347, 100%, 60%, 0.48) 100%
    );
    border-radius: 10px 10px 10px 0px;
  }
  .dark .mind-wall-item-message-bg {
    background: linear-gradient(
      220deg,
      hsl(13, 100%, 67%, 0.24) 0%,
      hsl(347, 100%, 60%, 0.24) 100%
    );
  }

  .chat-room-create-bg {
    background: linear-gradient(
      215deg,
      hsl(276, 100%, 69%, 0.3) 0%,
      hsl(227, 100%, 62%, 0.3) 100%
    );
  }

  .chat-room-bg {
    background: linear-gradient(
      180deg,
      #fbedff 0%,
      hsl(var(--app-bar-background)) 50%,
      #fbedff 100%
    );
  }

  .dark .chat-room-bg {
    background: linear-gradient(
      180deg,
      #0f1a30 0%,
      hsl(var(--app-bar-background)) 50%,
      #0f1a30 100%
    );
  }

  .chat-room-gold-bg {
    background: linear-gradient(
      180deg,
      rgba(255, 104, 210, 0.6) 0%,
      rgba(243, 103, 189, 0.4) 100%
    );
  }

  .user-plaza-item-recommend {
    background: linear-gradient(130deg, #9406ff 0%, #4e59ff 100%);
  }

  .dynamic-detail-gift-icon-bg {
    background: linear-gradient(90deg, #ffe4f5 0%, #f7f5fb 100%);
  }

  .dark .dynamic-detail-gift-icon-bg {
    background: linear-gradient(90deg, #2b425f 0%, #131f31 100%);
  }

  .voice-play-bg {
    background: linear-gradient(90deg, #9406ff 0%, #4e59ff 100%);
  }

  .dark .voice-play-bg {
    background: linear-gradient(90deg, #273083 0%, #4f1191 100%);
  }

  .live-charge-tips {
    background: linear-gradient(90deg, #9406ff 0%, #4e59ff 100%);
  }

  .my-user-info-bg {
    background: linear-gradient(
      to bottom right,
      rgba(190, 148, 222, 0.5) 0%,
      rgba(139, 145, 225, 0.5) 100%
    );
  }

  .dark .my-user-info-bg {
    background: linear-gradient(
      to bottom right,
      rgba(44, 2, 76, 0.5) 0%,
      rgba(22, 25, 75, 0.5) 100%
    );
  }

  .vip-banner-btn {
    background: linear-gradient(to right, #fe7400 0%, #ff9944 100%);
  }

  .dialog-bg {
    background: linear-gradient(to bottom, #ffcfdc 0%, #fff 60px);
  }

  .wallet-home-bg {
    background: linear-gradient(
      to bottom,
      #a075ff 0%,
      hsl(var(--scaffold-background)) 300px
    );
  }

  .wallet-recharge-btn {
    background: linear-gradient(to right, #cd92fc 0%, #b0b3fc 100%);
  }

  .wallet-main-opt-btn {
    background: linear-gradient(to right, #9406ff 0%, #4e59ff 100%);
  }

  .vip-type-item {
    background: linear-gradient(to bottom, #424166 0%, #2a2f3b 100%);
  }

  .vip-type-item-selected {
    background: linear-gradient(to bottom, #ede7ff 0%, #fff 100%);
  }

  .vip-cost-day-bg {
    background: linear-gradient(to right, #dcdcff 0%, #c2c2ff 100%);
  }

  .vip-describe-bg {
    background: linear-gradient(to bottom, #373737 0%, #686868 100%);
  }

  .user-invite-poster-copy-btn {
    background: linear-gradient(to right, #fcb23b 0%, #fb9842 100%);
  }

  .face-certified-bg {
    background: linear-gradient(
      to bottom,
      #e2e1ff 0%,
      hsl(var(--scaffold-background)) 100%
    );
  }

  .dark .face-certified-bg {
    background: linear-gradient(
      to bottom,
      #2e2d57 0%,
      hsl(var(--scaffold-background)) 100%
    );
  }

  .user-home-contact-bg {
    background: linear-gradient(to bottom, #ffcfdc 0%, #fff 100%);
  }

  .withdrawal-bg,
  .exchange-bg {
    background: linear-gradient(to bottom, #9406ff 0%, #4e59ff 100%);
  }

  .gift-shine-text {
    background: linear-gradient(92deg, #fdd02c 0%, #ffffff 50%, #fdd02c 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    animation: shine 2s linear infinite;
    background-size: 200% auto;
  }

  .short-video-price-bg {
    background: linear-gradient(to right, #9e0fff 0%, #4600f6 100%);
  }

  .short-video-featured-bg {
    background: linear-gradient(to right, #ff7b57 0%, #ff3460 100%);
  }

  .user-margin-bg {
    background: linear-gradient(to right, #e0baec 0%, #c8c1eb 100%);
  }

  @keyframes shine {
    to {
      background-position: 200% center;
    }
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-app-bar-background text-foreground;
  }
}

html {
  /* prettier-ignore */
  font-size: clamp(16Px, 4.26vw, 24Px);
}

body {
  /* 安全区域 */
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

:root {
  --adm-color-primary: #9d64ff;
}

.adm-switch {
  --checked-color: #5aed4f;
}
