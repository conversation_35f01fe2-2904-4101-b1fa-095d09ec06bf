import { v4 as uuidv4 } from "uuid";
import { sm2 } from "sm-crypto";
import { AxiosResponse } from "axios";

/**
 * 加密函数
 */
export const encrypt = (secretKey: string, data: Record<string, any> = {}) => {
  const timestamp = +new Date();
  const nonce = uuidv4().replace(/-/g, "");
  const sign = `04${sm2.doEncrypt(`${timestamp}${nonce}`, secretKey)}`;
  const encryptData = `04${sm2.doEncrypt(JSON.stringify(data), secretKey)}`;

  return {
    encryptData,
    timestamp,
    nonce,
    sign,
  };
};

/**
 * 解密函数
 */
export const decrypt = (secretKey: string, res: AxiosResponse) => {
  const encryptData = res.data.encryptData.toString().replace(/^04/, "");
  const data = sm2.doDecrypt(encryptData, secretKey);

  res.data = JSON.parse(data);
  return res.data;
};
