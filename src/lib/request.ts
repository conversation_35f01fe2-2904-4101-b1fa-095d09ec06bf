import axios, { AxiosRequestConfig } from "axios";
import { decrypt, encrypt } from "./crypto";
import { Toast } from "antd-mobile";
import { tipsDialog } from "@/components/work/tips-dialog";
import { navigate } from "@/router";
import { getEnabledLocation } from "@/utils/enabled-location";
import { queryClient } from "@/provider/query-client";

export interface ApiResponse<T> {
  code: number;
  codeMsg: string;
  ok: boolean;
  error: boolean;
  msg?: string;
  requestId?: string;
  data?: T;
}

export interface PageResponse<T> {
  last: boolean;
  total: string;
  totalPage: string;
  currentPage: string;
  size: string;
  data?: T;
}

const request = axios.create({
  timeout: 60 * 1000,
});

// 无需加密接口白名单
const whiteList = [
  "/api/message/sms/send-code",
  "/api/system/system/version/last",
];

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    if (import.meta.env.DEV) {
      console.log("request", config.url, config.data);
    }

    // GET请求不进行处理
    if (config.method === "get") {
      return config;
    }

    if (!config.url?.startsWith("/api")) {
      return config;
    }

    // 白名单不进行处理
    if (config.url && whiteList.includes(config.url)) {
      return config;
    }

    // 登录后，token在cookie中
    config.headers["x-version"] = 62;
    config.headers["x-platform"] = "Ios";
    config.headers["x-enabled-location"] = getEnabledLocation();
    config.headers["x-device-id"] = "dev-test";
    config.headers["isEmulator"] = false;
    // 可以在这里添加其他请求头信息
    config.headers["Content-Type"] = "application/json";

    config.data = encrypt(
      import.meta.env.VITE_PUBLIC_KEY as string,
      config.data
    );

    return config;
  },
  (error) => {
    // 处理请求错误
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 处理响应数据
    let data = response.data;

    if (response.data.encryptData) {
      const decryptData = decrypt(
        import.meta.env.VITE_PRAVATE_KEY as string,
        response
      );

      data = decryptData;

      if (import.meta.env.DEV) {
        // 方便调试
        console.log("response", response.config.url, data);
      }
    }

    // 统一清除loading，Toast只能同时显示一个，外部不好判断
    Toast.clear();

    if (data.code === 401) {
      queryClient.invalidateQueries({
        queryKey: ["logged"],
      });
      navigate("/login");

      return data;
    }

    //如果是未开通VIP，则弹出开通VIP窗口
    if (data.code === 501) {
      tipsDialog.error({
        title: "权限不足",
        content: data.msg,
        confirmText: "前往开通",
        cancelText: "取消",
        onConfirm() {
          navigate("/vip");
        },
      });

      return data;
    }

    //如果定位权限检查失败
    if (data.code === 502) {
      tipsDialog.error({
        title: "定位失败",
        content: data.msg,
        confirmText: "前往查看",
        cancelText: "取消",
        onConfirm() {
          // todo: 暂无
          navigate("/permissions-setting");
        },
      });

      return data;
    }

    //如果余额不足
    if (data.code === 503) {
      tipsDialog.error({
        title: "余额不足",
        content: data.msg,
        confirmText: "前往充值",
        cancelText: "取消",
        onConfirm() {
          navigate("/recharge");
        },
      });

      return data;
    }

    //权限不足
    if (data.code === 504) {
      tipsDialog.error({
        title: "权限不足",
        content: data.msg,
        confirmText: "知道了",
      });

      return data;
    }

    // 发生错误
    if (data.code === 505) {
      tipsDialog.error({
        title: "发生错误",
        content: data.msg,
        confirmText: "知道了",
      });

      return data;
    }

    //如果是未实名认证
    if (data.code === 506) {
      tipsDialog.error({
        title: "权限不足",
        content: data.msg,
        confirmText: "前往认证",
        cancelText: "取消",
        onConfirm() {
          navigate("/idcard-auth");
        },
      });

      return data;
    }

    //需要UP主
    if (data.code === 507) {
      tipsDialog.error({
        title: "权限不足",
        content: data.msg,
        confirmText: "前往申请",
        cancelText: "取消",
        onConfirm() {
          navigate("/video-uploader-apply");
        },
      });

      return data;
    }

    //需要绑定手机号
    if (data.code === 508) {
      tipsDialog.error({
        title: "权限不足",
        content: data.msg,
        confirmText: "前往绑定",
        cancelText: "取消",
        onConfirm() {
          navigate("/login/bind-phone");
        },
      });

      return data;
    }

    if (data.code && data.code !== 200) {
      Toast.show({
        content: data.msg ?? "请求异常",
        position: "bottom",
      });

      return data;
    }

    return data;
  },
  (error) => {
    // 处理响应错误
    if (error.response) {
      // 请求已发送，服务器状态码不在 2xx 范围
      console.error("Response error", error.response);
    } else if (error.request) {
      // 请求已发送但未收到响应
      console.error("Request error", error.request);
    } else {
      // 其他错误
      console.error("Error", error.message);
    }

    Toast.show({ content: "网络连接超时，请重试", position: "bottom" });

    return Promise.reject({
      code: error.response.status,
      codeMsg: "网络异常",
      ok: false,
      error: true,
      msg: "网络异常",
    } as ApiResponse<unknown>);
  }
);

const _post = <T>(
  url: string,
  data?: Record<string, any>,
  config?: AxiosRequestConfig<Record<string, any>>
): Promise<ApiResponse<T>> => {
  return request.post(url, data, config);
};

const _get = <T>(
  url: string,
  config?: AxiosRequestConfig<Record<string, any>>
): Promise<ApiResponse<T>> => {
  return request.get(url, config);
};

export const fetch = {
  post: _post,
  get: _get,
};
