import { UserTransactionResult } from "@/type/user-transaction-result";
import { fetch } from "../request";
import { CommonCommentResult } from "@/type/common-comment-result";
import { PageParams } from "@/type";
import { UserTransactionTenderResult } from "@/type/user-transaction-tender-result";

export const userTransactionGet = (data: { id: string }) => {
  return fetch.post<UserTransactionResult>(
    `/api/transaction/transaction/user-transaction/get/${data.id}`
  );
};

export const userTransactionCommentPage = (
  data: { targetId: string },
  params: PageParams
) => {
  return fetch.post<CommonCommentResult[]>(
    "/api/transaction/transaction/user-comment/page",
    data,
    { params }
  );
};

export const userTransactionCommentPageChildren = (
  data: { targetId: string; topId: string },
  params: PageParams
) => {
  return fetch.post<CommonCommentResult[]>(
    "/api/transaction/transaction/user-comment/page/children",
    data,
    { params }
  );
};

/**
 * 评论点赞
 */
export const userTransactionCommentLike = (data: {
  commentId: string;
  like: boolean;
}) => {
  return fetch.post(
    "/api/transaction/transaction/user-comment-operation/like",
    data
  );
};

type UserTransactionCommentPublishParams = {
  targetId: string;
  topId?: string;
  parentId?: string;
  content?: string;
  ats?: Record<string, string>;
  voice?: string;
  resources?: string[];
};

/**
 * 评论发布前检测
 */
export const userTransactionCommentPublishCheck = (
  data: UserTransactionCommentPublishParams
) => {
  return fetch.post(
    "/api/transaction/transaction/user-comment/publish/check",
    data
  );
};

/**
 * 发布评论
 */
export const userTransactionCommentPublishUrl =
  "/api/transaction/transaction/user-comment/publish";

export const userTransactionTenderPage = (
  data: { targetId: string },
  params: PageParams
) => {
  return fetch.post<UserTransactionTenderResult[]>(
    "/api/transaction/transaction/user-tender/page",
    data,
    { params }
  );
};

export const userTransactionTenderConfirm = (data: { id: string }) => {
  return fetch.post<{ id: string }>(
    "/api/transaction/transaction/user-tender/confirm",
    data
  );
};

export const userTransactionTenderLike = (data: {
  id: string;
  like: boolean;
}) => {
  return fetch.post("/api/transaction/transaction/user-operation/like", data);
};

export const userTransactionCreateOrder = (data: { id: string }) => {
  return fetch.post<{ id: string }>(
    "/api/transaction/transaction/user-transaction/create-order",
    data
  );
};

// 上架
export const userTransactionTakeOn = (data: { id: string }) => {
  return fetch.post(
    `/api/transaction/transaction/user-transaction/take-on/${data.id}`
  );
};

// 下架
export const userTransactionTakeOff = (data: { id: string }) => {
  return fetch.post(
    `/api/transaction/transaction/user-transaction/take-off/${data.id}`
  );
};

// 关闭
export const userTransactionClose = (data: { id: string }) => {
  return fetch.post(
    `/api/transaction/transaction/user-transaction/close/${data.id}`
  );
};

/**
 * 交易发布前检测
 */
export const userTransactionParticipateCheck = (data: any) => {
  return fetch.post(
    "/api/transaction/transaction/user-tender/participate-check",
    data
  );
};

/**
 * 发布交易
 */
export const userTransactionParticipateUrl =
  "/api/transaction/transaction/user-tender/participate";

/**
 * 交易发布前检测
 */
export const userTransactionPublishCheck = (data: any) => {
  return fetch.post(
    "/api/transaction/transaction/user-transaction/publish-check",
    data
  );
};

/**
 * 交易发布
 */
export const userTransactionPublishUrl =
  "/api/transaction/transaction/user-transaction/publish";
