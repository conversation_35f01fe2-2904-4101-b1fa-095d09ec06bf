import { RtcRoomResult } from "@/type/rtc-room-result";
import { fetch } from "../request";
import { RtcRoomScene, RtcRoomType } from "@/utils/enums";
import { RtcRoomJoinResult } from "@/type/rtc-room-join-result";

export const rtcRoomCreate = (data: {
  type: RtcRoomType;
  scene: RtcRoomScene;
  targetUserId: string;
}) => {
  return fetch.post<RtcRoomResult>("/api/chat/rtc/room/create", data);
};

export const rtcRoomGetById = (id: string) => {
  return fetch.post<RtcRoomResult>(`/api/chat/rtc/room/get/${id}`);
};

export const rtcRoomHangUp = (data: { id: string; note?: string }) => {
  return fetch.post<any>("/api/chat/rtc/room/hang-up", data);
};

export const rtcRoomJoin = (data: { id: string }) => {
  return fetch.post<RtcRoomJoinResult>("/api/chat/rtc/room/join", data);
};

export const rtcRoomExpand = (roomId: string) => {
  return fetch.post<{ tips: string }>(`/api/chat/rtc/room/expand/${roomId}`);
};
