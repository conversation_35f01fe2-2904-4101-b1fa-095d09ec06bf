import { VideoResult } from "@/type/video-result";
import { fetch } from "../request";
import { PageParams } from "@/type";
import { GiftGiverResult } from "@/type/gift-giver-result";
import { CommonCommentResult } from "@/type/common-comment-result";
import { ShortVideoResult } from "@/type/short-video-result";
import { PlaybackVideoResult } from "@/type/playback-video-result";
import { ShortVideoTagResult } from "@/type/short-video-tag-result";
import { VideoTagResult } from "@/type/video-tag-result";
import { VideoClassifyResult } from "@/type/video-classify-result";

export const getVideoLayout = () => {
  return fetch.post("/api/video/video/video/getLayout");
};

/**
 * 获取视频详情
 */
export const videoDetail = (id: string) => {
  return fetch.post<VideoResult>(`/api/video/video/video/get/${id}`);
};

export const vipPlayer = (id: string) => {
  return fetch.post(`/api/video/video/video/vip-player/${id}`);
};

export const buyVideo = (data: { id: string; type: number }) => {
  return fetch.post("/api/video/video/video/buy", data);
};

export const likeVideo = (data: { id: string; like: boolean }) => {
  return fetch.post("/api/video/video/operation/like", data);
};

export const downloadVideo = (id: string) => {
  return fetch.post<{ url: string }>(`/api/video/video/video/download/${id}`);
};

export const collectionVideo = (id: string) => {
  return fetch.post<{ url: string }>(
    `/api/video/video/operation/collection/${id}`
  );
};

export const videoGiftGiver = (data: { id: string }, params: PageParams) => {
  return fetch.post<GiftGiverResult[]>(
    `/api/video/video/gift/list-giver/${data.id}`,
    params
  );
};

export const videoRelated = (id: string) => {
  return fetch.post<VideoResult[]>(
    `/api/video/video/video/related/${id}?size=4`
  );
};

export const videoComment = (id: string, params: PageParams) => {
  return fetch.post<CommonCommentResult[]>(
    `/api/video/video/comment/page`,
    { targetId: id },
    { params }
  );
};

export const videoCommentLike = (data: {
  commentId: string;
  like: boolean;
}) => {
  return fetch.post("/api/video/video/comment-operation/like", data);
};

type VideoCommentPublishParams = {
  targetId: string;
  topId?: string;
  parentId?: string;
  content?: string;
  ats?: Record<string, string>;
  voice?: string;
  resources?: string[];
};

export const videoCommentPublishCheck = (data: VideoCommentPublishParams) => {
  return fetch.post("/api/video/video/comment/publish/check", data);
};

/**
 * 发布评论
 */
export const videoCommentPublishUrl = "/api/video/video/comment/publish";

export const videoGiftGive = (data: {
  videoId: string;
  giftId: string;
  note?: string;
}) => {
  return fetch.post("/api/video/video/gift/give", {
    ...data,
    number: 1,
  });
};

export const videoCommentChildren = (
  data: {
    targetId: string;
    topId?: string;
  },
  params: PageParams
) => {
  return fetch.post<CommonCommentResult[]>(
    "/api/video/video/comment/page/children",
    data,
    { params }
  );
};

/**
 * 短视频点赞
 */
export const shortVideoLike = (data: { id: string; like: boolean }) => {
  return fetch.post("/api/video/short-video/operation/like", data);
};

/**
 * 短视频收藏
 */
export const shortVideoCollection = (id: string) => {
  return fetch.post(`/api/video/short-video/operation/collection/${id}`);
};

/**
 * 短视频取消收藏
 */
export const shortVideoCancelCollection = (id: string) => {
  return fetch.post(`/api/video/short-video/operation/cancelCollection/${id}`);
};

/**
 * 删除短视频
 */
export const shortVideoDel = (id: string) => {
  return fetch.post(`/api/video/short-video/short-video/delete/${id}`);
};

/**
 * 短视频购买
 */
export const shortVideoBuy = (data: { id: string; type: number }) => {
  return fetch.post<ShortVideoResult>(
    `/api/video/short-video/short-video/buy`,
    data
  );
};

/**
 * 短视频下载
 */
export const shortVideoDownload = (id: string) => {
  return fetch.post<{ url: string }>(
    `/api/video/short-video/short-video/download/${id}`
  );
};

/**
 * 短视频标签
 */
export const shortVideoTag = (data: { id: string }, params: PageParams) => {
  return fetch.post<ShortVideoResult[]>(
    "/api/video/short-video/short-video/page",
    data,
    { params }
  );
};

export const shortVideoCommentList = (
  data: { targetId: string },
  params: PageParams
) => {
  return fetch.post<CommonCommentResult[]>(
    "/api/video/short-video/comment/page",
    data,
    { params }
  );
};

type DynamicCommentPublishParams = {
  targetId: string;
  topId?: string;
  parentId?: string;
  content?: string;
  ats?: Record<string, string>;
  voice?: string;
  resources?: string[];
};

/**
 * 短视频评论发布前检测
 */
export const shortVideoCommentPublishCheck = (
  data: DynamicCommentPublishParams
) => {
  return fetch.post("/api/video/short-video/comment/publish/check", data);
};

/**
 * 短视频评论发布
 */
export const shortVideoCommentPublishUrl =
  "/api/video/short-video/comment/publish";

/**
 * 短视频评论点赞
 */
export const shortVideoCommentLike = (data: {
  commentId: string;
  like: boolean;
}) => {
  return fetch.post("/api/video/short-video/comment-operation/like", data);
};

/**
 * 短视频评论子评论
 */
export const shortVideoCommentChildren = (
  data: { targetId: string; topId?: string },
  params: PageParams
) => {
  return fetch.post<CommonCommentResult[]>(
    "/api/video/short-video/comment/page/children",
    data,
    { params }
  );
};

/**
 * 获取回放视频详情
 */
export const playbackVideoDetail = (id: string) => {
  return fetch.post<PlaybackVideoResult>(`/api/video/playback/video/get/${id}`);
};

export const playbackVideoRelated = (id: string) => {
  return fetch.post<PlaybackVideoResult[]>(
    `/api/video/playback/video/related/${id}?size=4`
  );
};

export const playbackVideoLike = (data: { id: string; like: boolean }) => {
  return fetch.post("/api/video/playback/video-operation/like", data);
};

export const playbackVideoCollection = (id: string) => {
  return fetch.post<{ url: string }>(
    `/api/video/playback/video-operation/collection/${id}`
  );
};

export const playbackVideoBuy = (data: { id: string; type: number }) => {
  return fetch.post("/api/video/playback/video/buy", data);
};

export const playbackVideoDownload = (id: string) => {
  return fetch.post<{ url: string }>(
    `/api/video/playback/video/download/${id}`
  );
};

export const playbackVideoVipPlayer = (id: string) => {
  return fetch.post(`/api/video/playback/video/vip-player/${id}`);
};

type PlaybackVideoCommentPublishParams = {
  targetId: string;
  topId?: string;
  parentId?: string;
  content?: string;
  ats?: Record<string, string>;
  voice?: string;
  resources?: string[];
};

export const playbackVideoCommentPublishCheck = (
  data: PlaybackVideoCommentPublishParams
) => {
  return fetch.post("/api/video/playback/comment/publish/check", data);
};

/**
 * 发布评论
 */
export const playbackVideoCommentPublishUrl =
  "/api/video/playback/comment/publish";

export const playbackVideoGiftGive = (data: {
  videoId: string;
  giftId: string;
  note?: string;
}) => {
  return fetch.post("/api/video/playback/video-gift/give", {
    ...data,
    number: 1,
  });
};

/**
 * 回放视频评论点赞
 */
export const playbackVideoCommentLike = (data: {
  commentId: string;
  like: boolean;
}) => {
  return fetch.post("/api/video/playback/comment-operation/like", data);
};

export const playbackVideoComment = (id: string, params: PageParams) => {
  return fetch.post<CommonCommentResult[]>(
    `/api/video/playback/comment/page`,
    { targetId: id },
    { params }
  );
};

export const playbackVideoCommentChildren = (
  data: { targetId: string; topId?: string },
  params: PageParams
) => {
  return fetch.post<CommonCommentResult[]>(
    "/api/video/playback/comment/page/children",
    data,
    { params }
  );
};

export const shortVideoTagList = (
  data: { name: string },
  params: PageParams
) => {
  return fetch.post<ShortVideoTagResult[]>(
    "/api/video/short-video/tag/page",
    data,
    { params }
  );
};

export const shortVideoPublishCheck = (data: any) => {
  return fetch.post("/api/video/short-video/short-video/publish-check", data);
};

export const shortVideoPublishUrl =
  "/api/video/short-video/short-video/publish";

export const videoUploaderHas = () => {
  return fetch.post<boolean>("/api/video/video/uploader/has");
};

export const videoTagList = () => {
  return fetch.post<VideoTagResult[]>("/api/video/video/tag/list");
};

export const videoClassifyList = () => {
  return fetch.post<VideoClassifyResult[]>("/api/video/video/classify/list");
};

export const videoPublishCheck = (data: any) => {
  return fetch.post("/api/video/video/video/publish-check", data);
};

export const videoPublishUrl = "/api/video/video/video/publish";

export const shortVideoDetail = (id: string) => {
  return fetch.post<ShortVideoResult>(
    `/api/video/short-video/short-video/get/${id}`
  );
};

export const playbackVideoUpdate = (data: any) => {
  return fetch.post("/api/video/playback/video/update", data);
};
