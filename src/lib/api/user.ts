import { UserLevelConfigResult } from "@/type/user-level-config-result";
import { fetch } from "../request";
import { UserLoginResult } from "@/type/user-login-result";
import {
  ShareTargetType,
  ShareType,
  UserAuthType,
  UserLevelType,
  UserOnlineState,
} from "@/utils/enums";
import { User } from "@/type/user";
import { UserMenu } from "@/type/user-menu";
import { PageParams } from "@/type";
import { UserVipResult } from "@/type/user-vip-result";
import { UserInviteInfoResult } from "@/type/user-invite-info-result";
import { UserInviteRelationshipResult } from "@/type/user-invite-relationship-result";
import { UserInvitePosterResult } from "@/type/user-invite-poster-result";
import { UserDetailed } from "@/type/user-detailed";
import { UserVisitorSummaryResult } from "@/type/user-visitor-summary-result";
import { UserVisitorResult } from "@/type/user-visitor-result";
import { UserPunchInItemResult } from "@/type/user-punch-in-item-result";
import { UserTaskResult } from "@/type/user-task-result";
import { MedalResult } from "@/type/medal-result";
import { UserIdcardCertifiedResult } from "@/type/user-idcard-certified-result";
import { UserLevelResult } from "@/type/user-level-result";
import { UserLevelSummaryResult } from "@/type/user-level-summary-result";
import { UserLevelRecordResult } from "@/type/user-level-record-result";
import { UserCollectionResult } from "@/type/user-collection-result";
import { UserMountsResult } from "@/type/user-mounts-result";
import { UserJoinSpecialResult } from "@/type/user-join-special-result";
import { UserIdentifyCardResult } from "@/type/user-identify-card-result";
import { UserExpandResult } from "@/type/user-expand-result";
import { AccountSecurityItem } from "@/type/account-security-item";
import { UserSettingGroup } from "@/type/user-setting-group";
import { UserProfile } from "@/type/user-profile";
import { AmapLocationResult } from "@/type/amap-location-result";
import { UserChatProfileResult } from "@/type/user-chat-profile-result";
import { UserChatPermissionResult } from "@/type/user-chat-permission-result";
import { UserOrderDetailedResult } from "@/type/user-order-detailed-result";
import { UserAddressResult } from "@/type/user-address-result";
import { UserAlbum } from "@/type/user-album";
import { UserOrderRefundResult } from "@/type/user-order-refund-result";
import { UserBackpackGiftResult } from "@/type/user-backpack-gift-result";

export const userConfigList = () => {
  return fetch.post<UserLevelConfigResult[]>(
    "/api/user/level/user-config/list"
  );
};

export const loginSmsCode = (data: { phone: string; code: string }) => {
  return fetch.post<UserLoginResult>(
    "/api/user/user/user/login/sms-code",
    data
  );
};

export const loginPassword = (data: { phone: string; password: string }) => {
  return fetch.post<UserLoginResult>(
    "/api/user/user/user/login/password",
    data
  );
};

export const loginAuthToken = (data: {
  token: string;
  type: UserAuthType;
  inviteCode?: string;
}) => {
  return fetch.post<UserLoginResult>(
    "/api/user/user/user/login/auth-token",
    data
  );
};

export const loginInitialization = (data: {
  token: string;
  gender?: number;
  role?: number;
  sexualOrientation?: number;
  extent?: number;
  avatar?: string;
  nickname?: string;
  birthday?: string;
  inviteCode?: string;
}) => {
  return fetch.post<UserLoginResult>(
    "/api/user/user/user/login/initialization",
    data
  );
};

export const getPreviewById = (id: string) => {
  return fetch.post<{ url: string }>(`/api/user/user/preview/preview/${id}`);
};

export const loginBindPhone = (data: {
  token: string;
  phone: string;
  code: string;
}) => {
  return fetch.post<UserLoginResult>(
    "/api/user/user/user/login/bind-phone",
    data
  );
};

export const fullUserInfo = () => {
  return fetch.post<User>("/api/user/user/user/login-user/full-info");
};

export const userMenus = () => {
  return fetch.post<UserMenu[]>("/api/user/user/user/login-user/menus");
};

export const fansList = (data: { userId?: string }, params: PageParams) => {
  return fetch.post<User[]>("/api/user/user/follow/page-fans", data, {
    params,
  });
};

export const followList = (data: { userId?: string }, params: PageParams) => {
  return fetch.post<User[]>("/api/user/user/follow/page-follow", data, {
    params,
  });
};

/**
 * 关注
 */
export const follow = (id: string) => {
  return fetch.post("/api/user/user/follow/follow", {
    userId: id,
  });
};

/**
 * 取消关注
 */
export const unfollow = (id: string) => {
  return fetch.post("/api/user/user/follow/unfollow", {
    userId: id,
  });
};

export const userVip = () => {
  return fetch.post<UserVipResult>(
    "/api/user/vip/user-vip/login-user/full-info"
  );
};

export const getInviteInfo = () => {
  return fetch.post<UserInviteInfoResult>("/api/user/user/invite-info/get");
};

/**
 * 我邀请的好友
 */
export const getInviteList = (params: PageParams) => {
  return fetch.post<UserInviteRelationshipResult[]>(
    "/api/user/user/invite-relationship/pageInvite",
    {
      params,
    }
  );
};

/**
 * 获取邀请海报
 */
export const getInvitePoster = () => {
  return fetch.post<UserInvitePosterResult>(
    "/api/user/user/invite-info/get/invite-poster"
  );
};

/**
 * 申请代理人
 */
export const agentApply = (content?: string) => {
  return fetch.post<UserInvitePosterResult>(
    "/api/user/user/agent-apply-record/apply",
    {
      content,
    }
  );
};

/**
 * 查询指定用户详细信息
 */
export const getUserDetailed = (userId?: string) => {
  return fetch.post<UserDetailed>("/api/user/user/user/detailed", { userId });
};

/**
 * 加载扩展用户信息
 */
export const getUserExpand = (userId?: string) => {
  return fetch.post<UserExpandResult>("/api/user/user/user/expand", { userId });
};

/**
 * 加入黑名单
 */
export const joinBlacklist = (userId: string) => {
  return fetch.post("/api/user/user/blacklist/join", { userId });
};

/**
 * 移除黑名单
 */
export const removeBlacklist = (userId: string) => {
  return fetch.post("/api/user/user/blacklist/remove", { userId });
};

/**
 * 获取访客记录汇总信息
 */
export const getVisitorSummary = () => {
  return fetch.post<UserVisitorSummaryResult>(
    "/api/user/user/visitor/login-user/summary"
  );
};

/**
 * 获取访客记录
 */
export const getVisitorRecords = (params: PageParams) => {
  return fetch.post<UserVisitorResult[]>(
    "/api/user/user/visitor/page",
    {},
    {
      params,
    }
  );
};

/**
 * 获取用户签到信息
 */
export const getPunchInInfo = (month: string) => {
  return fetch.post<UserPunchInItemResult[]>(
    "/api/user/punch-in/user-record/login-user/list-month",
    {
      month,
    }
  );
};

/**
 * 补签
 */
export const rePunchIn = (date: string) => {
  return fetch.post(
    "/api/user/punch-in/user-re-record/login-user/re-punch-in",
    {
      date,
    }
  );
};

/**
 * 获取任务列表
 */
export const getTaskList = (params: PageParams) => {
  return fetch.post<UserTaskResult[]>(
    "/api/user/task/user-task/login-user/list",
    {},
    {
      params,
    }
  );
};

/**
 * 领取任务奖励
 */
export const receiveTaskReward = (id: string) => {
  return fetch.post("/api/user/task/user-task/login-user/reward", {
    id,
  });
};

/**
 * 取消订单
 */
export const cancelOrder = (id: string) => {
  return fetch.post(`/api/user/order/user-order/close/${id}`);
};

/**
 * 订单发货
 */
export const orderShip = (data: { id: string; trackingNumber: string }) => {
  const { id, trackingNumber } = data;

  return fetch.post(`/api/user/order/user-order/ship/${id}`, {
    trackingNumber,
  });
};

/**
 * 订单确认收货
 */
export const orderReceipt = (id: string) => {
  return fetch.post(`/api/user/order/user-order/receipt/${id}`);
};

/**
 * 订单退款申请
 */
export const orderRefund = (data: { id: string; applyReason: string }) => {
  const { id, applyReason } = data;

  return fetch.post<{ id: string }>(`/api/user/order/user-refund/apply`, {
    id,
    applyReason,
  });
};

/**
 * 获取勋章列表
 */
export const getMedalList = (params: PageParams) => {
  return fetch.post<MedalResult[]>(
    "/api/user/user/join-medal/list",
    {},
    {
      params,
    }
  );
};

/**
 * 获取身份证认证信息
 */
export const getIdcardAuth = () => {
  return fetch.post<UserIdcardCertifiedResult>(
    "/api/user/user/idcard-certified/get"
  );
};

/**
 * 获取身份证认证信息
 */
export const certifiedIdcard = (data: Record<string, any>) => {
  return fetch.post<UserIdcardCertifiedResult>(
    "/api/user/user/idcard-certified/certified",
    data
  );
};

/**
 * 获取用户等级信息
 */
export const userLevelList = () => {
  return fetch.post<UserLevelResult[]>(
    "/api/user/level/user-level/login-user/list"
  );
};

/**
 * 获取用户等级汇总信息
 */
export const userLevelSummary = (type: UserLevelType, params: PageParams) => {
  return fetch.post<UserLevelSummaryResult[]>(
    "/api/user/level/user-record/summary",
    {
      type,
    },
    {
      params,
    }
  );
};

/**
 * 获取用户等级明细
 */
export const userLevelDetailed = (type: UserLevelType, params: PageParams) => {
  return fetch.post<UserLevelRecordResult[]>(
    "/api/user/level/user-record/page",
    {
      type,
    },
    {
      params,
    }
  );
};

/**
 * 获取收藏列表
 */
export const collectionList = (params: PageParams) => {
  return fetch.post<UserCollectionResult[]>(
    "/api/user/user/collection/page",
    {},
    {
      params,
    }
  );
};

/**
 * 获取坐骑列表
 */
export const getMountsList = (params: PageParams) => {
  return fetch.post<UserMountsResult[]>(
    "/api/user/user/mounts/list",
    {},
    {
      params,
    }
  );
};

/**
 * 穿戴坐骑
 */

export const wearMounts = (id?: string) => {
  return fetch.post("/api/user/user/mounts/wear", {
    id,
  });
};

/**
 * 我的入场名片
 */
export const joinSpecialList = (params: PageParams) => {
  return fetch.post<UserJoinSpecialResult[]>(
    "/api/user/user/join-special/list",
    {},
    {
      params,
    }
  );
};

/**
 * 穿戴入场名片
 */
export const wearJoinSpecial = (id?: string) => {
  return fetch.post("/api/user/user/join-special/wear", {
    id,
  });
};

/**
 * 获取用户身份卡
 */
export const getIdentifyCard = () => {
  return fetch.post<UserIdentifyCardResult>(
    "/api/user/user/user/identify-card/get"
  );
};

/**
 * 创建身份卡
 */
export const createIdentifyCard = (data?: { phone: string; code: string }) => {
  return fetch.post<UserIdentifyCardResult>(
    "/api/user/user/user/identify-card/create",
    data
  );
};

/**
 * 赠送礼物
 */
export const giveGift = (data: {
  userId: string;
  giftId: string;
  note?: string;
}) => {
  return fetch.post("/api/user/user/gift-record/give", {
    number: 1,
    ...data,
  });
};

/**
 * 购买联系方式
 */
export const buyContact = (id: string) => {
  return fetch.post("/api/user/contact/user-contact/buy", { id });
};

/**
 * 停止出售联系方式
 */
export const closeSellContact = () => {
  return fetch.post("/api/user/contact/user-contact/close");
};

/**
 * 设置联系方式
 */
export const setContact = (data: Record<string, any>) => {
  return fetch.post("/api/user/contact/user-contact/set", data);
};

/**
 * 获取账号安全设置列表
 */
export const accountSecurityList = () => {
  return fetch.post<AccountSecurityItem[]>(
    "/api/user/user/user/login-user/list-account-security"
  );
};

/**
 * 修改密码
 */
export const updatePassword = (data: Record<string, any>) => {
  return fetch.post("/api/user/user/user/login-user/update-password", data);
};

/**
 * 获取隐私设置列表
 */
export const privacySettingList = () => {
  return fetch.post<UserSettingGroup[]>(
    "/api/user/user/user/login-user/list-privacy-setting"
  );
};

/**
 * 修改隐私设置
 */
export const changePrivacySetting = (data: Record<string, any>) => {
  return fetch.post("/api/user/user/setting/login-user/change", data);
};

/**
 * 获取消息设置列表
 */
export const messageSettingList = () => {
  return fetch.post<UserSettingGroup[]>(
    "/api/user/user/user/login-user/list-message-setting"
  );
};

/**
 * 修改消息设置
 */
export const changeMessageSetting = (data: Record<string, any>) => {
  return fetch.post("/api/user/user/setting/login-user/change", data);
};

/**
 * 获取黑名单列表
 */
export const blacklistList = (params: PageParams) => {
  return fetch.post<User[]>("/api/user/user/blacklist/page", {}, { params });
};

/**
 * 获取个人资料信息
 */
export const getUserProfile = () => {
  return fetch.post<UserProfile>("/api/user/user/user/profile");
};

/**
 * 修改个人资料信息
 */
export const updateUserProfile = (data: UserProfile) => {
  return fetch.post("/api/user/user/user/login-user/update-info", data);
};

/**
 * 取消收藏
 */
export const cancelCollection = (data: { targetId: string }) => {
  return fetch.post<User>("/api/user/user/collection/cancelCollection", data);
};

/**
 * 分享
 */
export const share = (data: {
  type: ShareType;
  targetType: ShareTargetType;
  id?: string;
}) => {
  return fetch.post<Record<string, any>>("/api/user/user/share/share", data);
};

/**
 * 用户搜索
 */
export const userSearch = (data: { keyword: string }, params: PageParams) => {
  return fetch.post<User[]>("/api/user/user/user/search", data, { params });
};

/**
 * 获得单个用户
 */
export const userSearchOne = (data: { keyword: string }) => {
  return fetch.post<User>("/api/user/user/user/search/one", data);
};

/**
 * 获取搜索热词
 */
export const searchRanking = () => {
  return fetch.post<{ content?: string; tips?: string }[]>(
    "/api/user/user/search-record/ranking"
  );
};

/**
 * 上报位置
 */
export const reportLocation = (data: AmapLocationResult) => {
  return fetch.post("/api/user/user/location-record/login-user/report", data);
};

/**
 * 获取用户聊天详细信息
 */
export const getUserChatProfile = (data: { userId: string }) => {
  return fetch.post<UserChatProfileResult>(
    `/api/user/user/user/get-chat-profile/${data.userId}`
  );
};

/**
 * 获取用户聊天权限邀请说明
 */
export const createUserChatPermission = (data: {
  userId: string;
  dayNumber: number;
  price: number;
}) => {
  return fetch.post<string>("/api/user/user/chat-permission/create", data);
};

/**
 * 获取用户互动权限
 */
export const getUserChatPermission = (id: string) => {
  return fetch.post<UserChatPermissionResult>(
    `/api/user/user/chat-permission/get/${id}`
  );
};

/**
 * 关闭用户互动权限
 */
export const closeUserChatPermission = (id: string) => {
  return fetch.post(`/api/user/user/chat-permission/closed/${id}`);
};

/**
 * 同意用户互动权限
 */
export const agreeUserChatPermission = (id: string) => {
  return fetch.post(`/api/user/user/chat-permission/agree/${id}`);
};

/**
 * 获取订单创建信息
 */
export const getUserOrderCreate = (id: string) => {
  return fetch.post<UserOrderDetailedResult>(
    `/api/user/order/user-order/get/${id}`
  );
};

/**
 * 获取用户地址
 */
export const getUserOrderAddress = (params: PageParams) => {
  return fetch.post<UserAddressResult[]>(
    "/api/user/order/user-address/list",
    {},
    {
      params,
    }
  );
};

/**
 * 新增或编辑地址
 */
export const saveUserAddress = (data: Record<string, any>) => {
  return fetch.post(
    `/api/user/order/user-address/${data.id ? "update" : "save"}`,
    data
  );
};

/**
 * 获取地址
 */
export const getUserAddress = (id: string) => {
  return fetch.post<UserAddressResult>(
    `/api/user/order/user-address/get/${id}`
  );
};

/**
 * 设置默认地址
 */
export const setUserAddressDefault = (id: string) => {
  return fetch.post<Record<string, any>>(
    `/api/user/order/user-address/setDefault/${id}`
  );
};

/**
 * 删除地址
 */
export const deleteUserAddress = (id: string) => {
  return fetch.post(`/api/user/order/user-address/del/${id}`);
};

/**
 * 更新订单地址
 */
export const updateUserOrderAddress = (data: {
  id: string;
  addressId: string;
}) => {
  return fetch.post<Record<string, any>>(
    `/api/user/order/user-order/update/${data.id}`,
    {
      addressId: data.addressId,
    }
  );
};

/**
 * 获取用户相册
 */
export const getUserAlbum = (data: { userId: string }) => {
  return fetch.post<UserAlbum>("/api/user/album/user-album/get", data);
};

/**
 * 购买用户相册
 */
export const buyUserAlbum = (data: { userId: string }) => {
  return fetch.post("/api/user/album/user-album/buy", data);
};

/**
 * 删除用户相册
 */
export const removeUserAlbum = (data: { id: string }) => {
  return fetch.post("/api/user/album/user-album/remove", data);
};

export const userAlbumAddCheck = (data: any) => {
  return fetch.post("/api/user/album/user-album/add-check", data);
};

export const userAlbumAdd = "/api/user/album/user-album/add";

export const userAlbumSetPrice = (data: { price: number }) => {
  return fetch.post("/api/user/album/user-album/set", data);
};

export const getUserOrderRefundDetail = (id: string) => {
  return fetch.post<UserOrderRefundResult>(
    `/api/user/order/user-refund/get/${id}`
  );
};

export const auditUserOrderRefund = (data: {
  id: string;
  state: boolean;
  note?: string;
}) => {
  return fetch.post("/api/user/order/user-refund/audit", {
    ...data,
    state: data.state ? 2 : 1,
  });
};

export const getBackpackGiftList = (params: PageParams) => {
  return fetch.post<UserBackpackGiftResult[]>(
    "/api/user/user/backpack/page/gift",
    {},
    {
      params,
    }
  );
};

export const rePunchInRecord = (params: PageParams) => {
  return fetch.post<{ optDate: string; cdate: string }[]>(
    "/api/user/punch-in/user-re-record/login-user/page",
    {},
    {
      params,
    }
  );
};

export const shareFriend = (data: {
  targetType: ShareTargetType;
  id: string;
  cid: string;
  uid?: string;
  gid?: string;
}) => {
  return fetch.post<Record<string, any>>(
    "/api/user/user/share/share/friend",
    data
  );
};

export const updateUserOnlineState = (data: {
  onlineState: UserOnlineState;
}) => {
  return fetch.post<User>(
    "/api/user/user/user/login-user/update-online-state",
    data
  );
};
