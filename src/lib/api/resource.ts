import { ResourceUploadInfo } from "@/type/resource-upload-info";
import { fetch } from "../request";

export const getImageUploadInfo = () => {
  return fetch.post<ResourceUploadInfo>(
    "/api/resource/resource/resource/get-upload-info",
    {
      type: 0,
    }
  );
};

export const getVideoUploadInfo = () => {
  return fetch.post<ResourceUploadInfo>(
    "/api/resource/resource/resource/get-upload-info",
    {
      type: 1,
    }
  );
};

/**
 * 检查文件是否已经上传
 */
export const checkFileCloudTranscoding = (data: {
  id: string;
  md5: string;
  filename: string;
}) => {
  return fetch.post(
    "/api/resource/resource/resource/cloud-transcoding/check",
    data
  );
};

/**
 * 云转码上传成功后就调用资源系统的合并
 */
export const uploadFileCloudTranscodingChunk = (data: {
  id: string;
  uniqueFileName: string;
  filename: string;
  md5: string;
  chunks: number;
  duration?: number;
}) => {
  return fetch.post(
    "/api/resource/resource/resource/cloud-transcoding/chunk",
    data
  );
};
