import { SubmitWebPay, WebPayOrderInfo } from "@/type/web-pay";
import { fetch } from "../request";
import { UserWallet } from "@/type/user-wallet";
import { UserWalletRecordResult } from "@/type/user-wallet-record-result";
import { PageParams } from "@/type";
import { UserBankCard } from "@/type/user-bank-card";
import { PayChannelResult } from "@/type/pay-channel-result";
import { PayBusinessType, PayType } from "@/utils/enums";
import { RedEnvelopeResult } from "@/type/red-envelope-result";
import { UserChargeConfigResult } from "@/type/user-charge-config-result";
import { UserChargeConfigItemResult } from "@/type/user-charge-config-item-result";

export const getWebPay = (orderNumber: string) => {
  return fetch.post<WebPayOrderInfo>(
    `/api/wallet/pay/channel/get/web-pay/${orderNumber}`
  );
};

export const submitWebPay = (data: { orderNumber: string; code: string }) => {
  return fetch.post<SubmitWebPay>(
    "/api/wallet/pay/channel/submit/web-pay",
    data
  );
};

export const userWallet = () => {
  return fetch.post<UserWallet[]>(
    "/api/wallet/wallet/user-wallet/login-user/list"
  );
};

export const userWalletBill = (params: PageParams) => {
  return fetch.post<UserWalletRecordResult[]>(
    "/api/wallet/wallet/user-record/login-user/page",
    {},
    { params }
  );
};

export const cdkActive = (number: string) => {
  return fetch.post("/api/wallet/wallet/cdk/active", { number });
};

/**
 * 绑定银行卡
 */
export const bindBankCard = (data: Record<string, any>) => {
  return fetch.post("/api/wallet/wallet/user-bank-card/bind", data);
};

/**
 * 获取银行卡列表
 */
export const bankList = () => {
  return fetch.post<UserBankCard[]>(
    "/api/wallet/wallet/user-bank-card/list",
    {},
    {
      params: {
        page: 1,
        size: 9999,
      },
    }
  );
};

/**
 * 删除银行卡
 */
export const deleteBankCard = (id: string) => {
  return fetch.post(`/api/wallet/wallet/user-bank-card/unbind`, { id });
};

/**
 * 提现
 */
export const withdrawApply = (data: { bankCardId: string; amount: string }) => {
  return fetch.post("/api/wallet/wallet/user-wallet/withdrawal-apply", data);
};

/**
 * 兑换
 */
export const exchange = (number: string) => {
  return fetch.post("/api/wallet/wallet/user-wallet/exchange", { number });
};

/**
 * 充值配置
 */
export const rechargeCostConfig = () => {
  return fetch.post("/api/wallet/wallet/recharge-cost-config/listEnabled");
};

/**
 * 获取支付渠道
 */
export const payChannel = () => {
  return fetch.post<PayChannelResult[]>("/api/wallet/pay/channel/getChannel");
};

/**
 * 支付订单创建
 */
export const createOrder = (data: {
  id: string;
  payType: PayType;
  userId?: string;
  sign?: string;
  type: PayBusinessType;
  params: Record<string, any>;
}) => {
  return fetch.post<any>("/api/wallet/pay/channel/create-order", data);
};

/**
 * 领取红包
 */
export const receiveRedEnvelope = (id: string) => {
  return fetch.post(`/api/wallet/red-envelope/red-envelope/receive/${id}`);
};

/**
 * 获取红包详情
 */
export const redEnvelopeDetail = (id: string) => {
  return fetch.post<RedEnvelopeResult>(
    `/api/wallet/red-envelope/red-envelope/get/${id}`
  );
};

/**
 * 创建红包
 */
export const redEnvelopeCreate = (data: {
  userId?: string;
  groupId?: string;
  data: Record<string, any>;
}) => {
  return fetch.post("/api/wallet/red-envelope/red-envelope/create", {
    userId: data.userId,
    groupId: data.groupId,
    ...data.data,
  });
};

/**
 * 获取充值配置
 */
export const getChargeConfig = (data: { userId: string }) => {
  return fetch.post<UserChargeConfigResult>(
    "/api/wallet/wallet/user-charge-config/get",
    data
  );
};

export const userChargeConfigItems = () => {
  return fetch.post<UserChargeConfigItemResult[]>(
    "/api/wallet/wallet/user-charge-config/login-user/items"
  );
};

export const updateChargeConfig = (data: { key: string; value: number }) => {
  return fetch.post("/api/wallet/wallet/user-charge-config/login-user/update", {
    [data.key]: data.value,
  });
};
