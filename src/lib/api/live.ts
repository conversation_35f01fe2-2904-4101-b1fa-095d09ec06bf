import { LiveRoomDetailedResult } from "@/type/live-room-detailed-result";
import { fetch } from "../request";
import { LiveroomMember } from "@/type/live-room-member";
import { PageParams } from "@/type";

/**
 * 加载直播间
 */
export const joinLiveRoom = (id: string) => {
  return fetch.post<LiveRoomDetailedResult>("/api/live/live/room/join", { id });
};

/**
 * 直播间观众
 */
export const LiveRoomMembers = (data: { id: string }, params: PageParams) => {
  return fetch.post<LiveroomMember[]>("/api/live/live/room/members", data, {
    params,
  });
};

/**
 * 直播间送礼物
 */
export const LiveRoomSendGift = (data: {
  id: string;
  userId: string;
  giftId?: string;
  targetId?: string;
  number: number;
}) => {
  return fetch.post("/api/live/live/gift/give", data);
};

/**
 * 退出直播间
 */
export const exitLiveRoom = (data: { id: string }) => {
  return fetch.post("/api/live/live/room/exit", data);
};

/**
 * 购买直播间
 */
export const buyLiveRoom = (data: { id: string }) => {
  return fetch.post<LiveRoomDetailedResult>("/api/live/live/room/buy", data);
};

export const liveRoomCheckTimeDeduction = (data: { id: string }) => {
  return fetch.post<boolean>("/api/live/live/room/check-time-deduction", data);
};
