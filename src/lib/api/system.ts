import { SystemConfig } from "@/type/system-config";
import { fetch } from "../request";
import { Banner } from "@/type/banner";
import { DefaultResource } from "@/type/default-resource";
import { FaqItem } from "@/type/faq-list";
import { UserBanRecord } from "@/type/user-ban-record";
import { PageParams } from "@/type";
import { DiscountActivityTargetResult } from "@/type/discount-activity-target-result";
import { UserUnbanInfoResult } from "@/type/user-unban-info-result";
import { VipTypeResult } from "@/type/vip-type-result";
import { VipCostResult } from "@/type/vip-cost-result";
import { VipDescribeResult } from "@/type/vip-describe-result";
import { GiftResult } from "@/type/gift-result";
import { DiscountActivityResult } from "@/type/discount-activity-result";
import { MountsResult } from "@/type/mounts-result";
import { JoinSpecialResult } from "@/type/join-special-result";

export const configList = () => {
  return fetch.post<SystemConfig>("/api/system/system/config/list");
};

export const getConfig = <T = any>(key: string) => {
  return fetch.post<T>(`/api/system/static-config/layout/${key}`);
};

/** 首页 banner */
export const getHomeBannerList = () => {
  return fetch.post<Banner[]>("/api/system/system/banner/list/HOME");
};

/** 用户推荐 banner */
export const recommendUserBanner = () => {
  return fetch.post<DefaultResource[]>(
    "/api/system/system/default-resource/list/RECOMMEND_USER_BANNER"
  );
};

/** 默认头像 */
export const defaultAvatar = () => {
  return fetch.post<DefaultResource[]>(
    "/api/system/system/default-resource/list/DEFAULT_AVATAR"
  );
};

/** 首充Banner */
export const firstRechargeBanner = () => {
  return fetch.post<DefaultResource[]>(
    "/api/system/system/default-resource/list/FIRST_RECHARGE_BANNER"
  );
};

/** 回归用户Banner */
export const returnUserBanner = () => {
  return fetch.post<DefaultResource[]>(
    "/api/system/system/default-resource/list/RETURN_USER_BANNER"
  );
};

/** 常见问题 */
export const faqList = () => {
  return fetch.post<FaqItem[]>("/api/system/system/faq/list");
};

/** 封禁用户列表 */
export const userBanList = (data: { keyword?: string }, params: PageParams) => {
  return fetch.post<UserBanRecord[]>("/api/system/ban/user-record/page", data, {
    params,
  });
};

/** 加载回归用户 */
export const getReback = () => {
  return fetch.post<DiscountActivityTargetResult>(
    "/api/system/activity/discount-activity/get/reback"
  );
};

/** 加载充值用户 */
export const getFirstRecharge = () => {
  return fetch.post<DiscountActivityTargetResult>(
    "/api/system/activity/discount-activity/get/first-recharge"
  );
};

/** 获取用户解封信息 */
export const getUserUnBanInfo = () => {
  return fetch.post<UserUnbanInfoResult>(
    "/api/system/ban/user-self-unrecord/login-user/get"
  );
};

/** 查询类型列表 */
export const vipTypeList = () => {
  return fetch.post<VipTypeResult[]>("/api/system/vip/type/listEnabled");
};

/** 查询套餐列表 */
export const vipCostList = () => {
  return fetch.post<VipCostResult[]>("/api/system/vip/cost/listEnabled");
};

/** 查询特权列表 */
export const vipDescribeList = () => {
  return fetch.post<VipDescribeResult[]>("/api/system/vip/describe/listResult");
};

/**
 * 礼物列表
 */
export const giftList = () => {
  return fetch.post<GiftResult[]>("/api/system/system/gift/list-enabled");
};

/** 优惠活动列表 */
export const getDiscountActivity = (sign: string) => {
  return fetch.post<DiscountActivityResult>(
    `/api/system/activity/discount-activity/get/${sign}`
  );
};

/** 举报类型列表 */
export const reportTypeList = () => {
  return fetch.post<{ id: string; note?: string }[]>(
    "/api/system/system/report-category/list"
  );
};

export const reportRecordCheck = (data: any) => {
  return fetch.post("/api/system/system/report-record/report-check", data);
};

export const reportRecordUrl = "/api/system/system/report-record/report";

export const feedbackSubmitCheck = (data: any) => {
  return fetch.post("/api/system/system/feedback-record/submit-check", data);
};

export const feedbackUrl = "/api/system/system/feedback-record/submit";

export const systemMountsList = () => {
  return fetch.post<MountsResult[]>("/api/system/system/mounts/list");
};

export const systemJoinSpecialList = () => {
  return fetch.post<JoinSpecialResult[]>(
    "/api/system/system/join-special/list"
  );
};
