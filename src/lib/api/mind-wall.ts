import { PageParams } from "@/type";
import { fetch } from "../request";
import { MindWallResult } from "@/type/mind-wall-result";
import { CommonCommentResult } from "@/type/common-comment-result";

export const mindWallNewest = (params: PageParams) => {
  return fetch.post<MindWallResult[]>(
    "/api/mind-wall/mind-wall/mind-wall/newest",
    {},
    { params }
  );
};

export const mindWallOperationLike = (data: { id: string; like: boolean }) => {
  return fetch.post("/api/mind-wall/mind-wall/operation/like", data);
};

export const mindWallDetailed = (data: { id: string }) => {
  return fetch.post<MindWallResult>(
    `/api/mind-wall/mind-wall/mind-wall/detailed/${data.id}`
  );
};

export const mindWallCommentPage = (
  data: { targetId: string },
  params: PageParams
) => {
  return fetch.post<CommonCommentResult[]>(
    "/api/mind-wall/comment/mind-wall-comment/page",
    data,
    { params }
  );
};

type MindWallCommentPublishParams = {
  targetId: string;
  topId?: string;
  parentId?: string;
  content?: string;
  ats?: Record<string, string>;
  voice?: string;
  resources?: string[];
};

/**
 * 评论发布前检测
 */
export const mindWallCommentPublishCheck = (
  data: MindWallCommentPublishParams
) => {
  return fetch.post(
    "/api/mind-wall/comment/mind-wall-comment/publish/check",
    data
  );
};

/**
 * 发布评论
 */
export const mindWallCommentPublishUrl =
  "/api/mind-wall/comment/mind-wall-comment/publish";

/**
 * 评论点赞
 */
export const mindWallCommentLike = (data: {
  commentId: string;
  like: boolean;
}) => {
  return fetch.post("/api/mind-wall/comment/mind-wall-operation/like", data);
};

/**
 * 评论子评论
 */
export const mindWallCommentChildren = (
  data: { targetId: string; topId?: string },
  params: PageParams
) => {
  return fetch.post<CommonCommentResult[]>(
    "/api/mind-wall/comment/mind-wall-comment/page/children",
    data,
    { params }
  );
};

/**
 * 获取心意墙列表
 */
export const mindWallPageByUserId = (
  data: { userId: string },
  params: PageParams
) => {
  return fetch.post<MindWallResult[]>(
    "/api/mind-wall/mind-wall/mind-wall/pageByUserId",
    data,
    { params }
  );
};
