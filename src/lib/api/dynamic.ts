import { PageParams } from "@/type";
import { fetch } from "../request";
import { DynamicResult } from "@/type/dynamic-result";
import { CommonCommentResult } from "@/type/common-comment-result";
import { DynamicTopic } from "@/type/dynamic-topic";
import { User } from "@/type/user";
import { GiftGiverResult } from "@/type/gift-giver-result";
import { DynamicMentionParseResult } from "@/type/dynamic-mention-parse-result";

/** 动态详情 */
export const dynamicGet = (params: { id: string }) => {
  return fetch.post<DynamicResult>("/api/dynamic/dynamic/dynamic/get", {
    id: params.id,
  });
};

export const dynamicCommentPage = (
  data: { targetId: string },
  params: PageParams
) => {
  return fetch.post<CommonCommentResult[]>(
    "/api/dynamic/comment/dynamic-comment/page",
    data,
    { params }
  );
};

export const dynamicCommentPageChildren = (
  data: { targetId: string; topId: string },
  params: PageParams
) => {
  return fetch.post<CommonCommentResult[]>(
    "/api/dynamic/comment/dynamic-comment/page/children",
    data,
    { params }
  );
};

/** 热门推荐 */
export const topicHot = () => {
  return fetch.post<DynamicTopic[]>("/api/dynamic/dynamic/topic/hot");
};

export const topicDetail = (data: { id: string; name?: string }) => {
  return fetch.post<DynamicTopic>("/api/dynamic/dynamic/topic/get", data);
};

/**
 * 点赞
 */
export const dynamicLike = (data: { id: string; like: boolean }) => {
  return fetch.post("/api/dynamic/dynamic/operation/like", data);
};

/**
 * 收藏
 */
export const dynamicCollect = (data: { id: string }) => {
  return fetch.post("/api/dynamic/dynamic/operation/collection", data);
};

/**
 * 点赞列表
 */
export const dynamicLikeList = (data: { id: string }, params: PageParams) => {
  return fetch.post<User[]>("/api/dynamic/dynamic/dynamic/like/list", data, {
    params,
  });
};

/**
 * 动态购买
 */
export const dynamicBuy = (data: { id: string }) => {
  return fetch.post("/api/dynamic/dynamic/operation/buy", data);
};

/**
 * 赠礼人列表
 */
export const dynamicGiftGiver = (data: { id: string }, params: PageParams) => {
  return fetch.post<GiftGiverResult[]>(
    "/api/dynamic/dynamic/gift/list-giver",
    data,
    {
      params,
    }
  );
};

/**
 * 评论点赞
 */
export const dynamicCommentLike = (data: {
  commentId: string;
  like: boolean;
}) => {
  return fetch.post<number>(
    "/api/dynamic/comment/dynamic-operation/like",
    data
  );
};

/**
 * 评论详情
 */
export const dynamicCommentDetailed = (
  data: { targetId: string; topId: string },
  params: PageParams
) => {
  return fetch.post<CommonCommentResult[]>(
    "/api/dynamic/comment/dynamic-comment/page/children",
    data,
    { params }
  );
};

/**
 * 动态赠送礼品
 */
export const dynamicGiveGift = (data: {
  dynamicId: string;
  giftId: string;
  note?: string;
}) => {
  return fetch.post("/api/dynamic/dynamic/gift/give", {
    number: 1,
    ...data,
  });
};

type DynamicCommentPublishParams = {
  targetId: string;
  topId?: string;
  parentId?: string;
  content?: string;
  ats?: Record<string, string>;
  voice?: string;
  resources?: string[];
};

/**
 * 评论发布前检测
 */
export const dynamicCommentPublishCheck = (
  data: DynamicCommentPublishParams
) => {
  return fetch.post("/api/dynamic/comment/dynamic-comment/publish/check", data);
};

/**
 * 发布评论
 */
export const dynamicCommentPublishUrl =
  "/api/dynamic/comment/dynamic-comment/publish";

export const dynamicParseMention = (data: {
  targetType: number;
  targetId: string;
}) => {
  return fetch.post<DynamicMentionParseResult>(
    "/api/dynamic/dynamic/dynamic/parse/mention",
    data
  );
};

export const dynamicPublishCheck = (data: any) => {
  return fetch.post("/api/dynamic/dynamic/dynamic/publish/check", data);
};

export const dynamicPublishUrl = "/api/dynamic/dynamic/dynamic/publish";

export const dynamicDelete = (data: { id: string }) => {
  return fetch.post("/api/dynamic/dynamic/dynamic/delete", data);
};
