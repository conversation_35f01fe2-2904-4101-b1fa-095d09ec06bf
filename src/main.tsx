import ReactDOM from "react-dom/client";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
// import VConsole from "vconsole";
import videojs from "video.js";
import zhCN from "video.js/dist/lang/zh-CN.json";
import App from "./App.tsx";
import "video.js/dist/video-js.css";
import "./global.css";
import "./index.less";

dayjs.extend(duration);
videojs.addLanguage("zh-CN", zhCN);

// if (import.meta.env.DEV) {
//   new VConsole();
// }

ReactDOM.createRoot(document.getElementById("root")!).render(<App />);
