import { getImageUploadInfo, getVideoUploadInfo } from "@/lib/api/resource";
import { useQuery } from "@tanstack/react-query";

export const useUploadInfo = () => {
  const { isLoading: imageLoading, data: imageData } = useQuery({
    queryKey: ["image-upload-info"],
    queryFn: () => getImageUploadInfo(),
    staleTime: Infinity,
  });

  const { isLoading: videoLoading, data: videoData } = useQuery({
    queryKey: ["video-upload-info"],
    queryFn: () => getVideoUploadInfo(),
    staleTime: Infinity,
  });

  return {
    isLoading: imageLoading && videoLoading,
    imageUploadInfo: imageData?.data,
    videoUploadInfo: videoData?.data,
  };
};
