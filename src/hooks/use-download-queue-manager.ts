import { produce } from "immer";
import { v4 as uuidv4 } from "uuid";
import { DownloadQueueItem, useDownloadQueue } from "@/store/download-queue";
import { downloadFile } from "@/utils/download-file";

export const useDownloadQueueManager = () => {
  const { downloadQueue, setDownloadQueue } = useDownloadQueue();

  const addToQueue = async (item: DownloadQueueItem) => {
    setDownloadQueue(
      produce(downloadQueue, (draft) => {
        draft.push({
          id: uuidv4(),
          date: +new Date(),
          subtitle: "下载中...",
          ...item,
        });
      })
    );

    await downloadFile(item.url, item.title);

    setDownloadQueue(
      produce(downloadQueue, (draft) => {
        draft.splice(
          draft.findIndex((val) => val.id === item.id),
          1
        );
      })
    );
  };

  return {
    addToQueue,
  };
};
