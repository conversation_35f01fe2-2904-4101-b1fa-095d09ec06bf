import { fullUserInfo } from "@/lib/api/user";
import { localCustomMessage } from "@/utils/local-custom-message";
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";

export const fullUserInfoKey = ["logged", "full-user-info"];

export const useFullUserInfo = () => {
  const { data, isFetching: isLoading } = useQuery({
    queryKey: fullUserInfoKey,
    queryFn: fullUserInfo,
    staleTime: Infinity,
  });

  useEffect(() => {
    if (data?.data) {
      localCustomMessage.init(data.data.id!);
    }
  }, [data?.data]);

  return {
    isLoading,
    data: data?.data,
  };
};
