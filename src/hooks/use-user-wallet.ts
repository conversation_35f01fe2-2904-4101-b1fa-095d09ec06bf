import { userWallet } from "@/lib/api/wallet";
import { useQuery } from "@tanstack/react-query";
import dayjs from "dayjs";

export const userWalletQueryKey = ["logged", "user-wallet"];

export const useUserWallet = () => {
  const { data, isFetching: isLoading } = useQuery({
    queryKey: userWalletQueryKey,
    queryFn: userWallet,
    staleTime: Infinity,
  });

  /** 根据钱包类型获得余额，如果钱包不存在则会返回空 */
  const getBalanceOrEmptyByType = (type: number) => {
    for (const item of data?.data ?? []) {
      if (item.type === type) {
        if (item.expiration && dayjs(item.expiration).isBefore(dayjs())) {
          return 0;
        }

        return item.balance;
      }
    }
  };

  /** 根据钱包类型获得余额，如果钱包不存在则会返回0 */
  const getBalanceByType = (type: number) => {
    return getBalanceOrEmptyByType(type) ?? 0;
  };

  return {
    isLoading,
    getBalanceOrEmptyByType,
    getBalanceByType,
  };
};
