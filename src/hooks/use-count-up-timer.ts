import { useEffect, useState } from "react";

/**
 * 正计时处理器
 * @param maxTime 最大计时时间（单位秒），可选，不传则无限计时
 */
export const useCountUpTimer = (
  defaultMaxTime?: number,
  defaultRunning = false
) => {
  const [currentTime, setCurrentTime] = useState(0);
  const [isRunning, setIsRunning] = useState(defaultRunning);
  const [maxTime, setMaxTime] = useState(defaultMaxTime ?? 0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime((prevSeconds) => {
        // 如果有最大时间，且已到达最大时间，则停止计时
        if (
          maxTime !== undefined &&
          maxTime > 0 &&
          prevSeconds + 1 >= maxTime
        ) {
          setIsRunning(false);
          clearInterval(timer!);
          return maxTime;
        }
        return prevSeconds + 1;
      });
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [isRunning, maxTime]);

  const handleStart = (currentTime?: number, maxTime?: number) => {
    setCurrentTime(currentTime ?? 0);
    setMaxTime(maxTime ?? defaultMaxTime ?? 0);
    setIsRunning(true);
  };

  return {
    currentTime,
    isRunning,
    handleStart,
  };
};
