import { useEffect, useRef, useState } from "react";

export const useScrollOpacity = (height: number) => {
  const [opacity, setOpacity] = useState(0);

  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const scrollDom = scrollRef.current?.querySelector(
      "[data-radix-scroll-area-viewport]"
    );

    const handleScroll = () => {
      const scrollTop = scrollDom?.scrollTop ?? 0;
      const opacity = scrollTop / height;

      setOpacity(opacity);
    };

    if (scrollDom) {
      scrollDom.addEventListener("scroll", handleScroll);
    }

    return () => {
      scrollDom?.removeEventListener("scroll", handleScroll);
    };
  }, [height]);

  return { opacity, scrollRef };
};
