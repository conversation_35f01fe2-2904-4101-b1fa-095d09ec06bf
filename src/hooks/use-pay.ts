import {PayChannelResult} from "@/type/pay-channel-result";
import {usePayChannel} from "./use-pay-channel";
import {showPayModal} from "@/components/work/show-pay-modal";
import {PayBusinessType, PayType} from "@/utils/enums";
import {Toast} from "antd-mobile";
import {useMutation} from "@tanstack/react-query";
import {createOrder} from "@/lib/api/wallet";
import {v4 as uuidv4} from "uuid";

export const usePay = (props: {
    type: PayBusinessType;
    userId?: string,
    params: Record<string, any>;
}) => {
    const {type, userId, params} = props;

    const {refetch} = usePayChannel();

    const createOrderMutation = useMutation({
        mutationFn: createOrder,
    });

    const showPay = async () => {
        Toast.show({
            icon: "loading",
            content: "加载中...",
            duration: 0,
        });
        const {data} = await refetch();

        if (!data?.data?.length) {
            Toast.show({
                content: "支付通道加载失败",
            });
            return;
        }

        const id = uuidv4();
        const channel = await new Promise<PayChannelResult | undefined>(
            (resolve) => {
                showPayModal({
                    data: data?.data ?? [],
                    onSelect: (channel: PayChannelResult) => {
                        resolve(channel);
                    },
                    onClose: () => {
                        resolve(undefined);
                    },
                });
            }
        );

        if (channel) {
            // 选择支付渠道成功
            Toast.show({
                icon: "loading",
                content: "请稍后...",
                duration: 0,
            });

            const {ok, data} = await createOrderMutation.mutateAsync({
                id,
                payType: channel.type!,
                type,
                sign: channel.sign,
                userId: userId,
                params,
            });

            if (ok) {
                // 创建订单成功
                console.log(data);

                // 提交支付

                // 暂时只处理网页支付逻辑
                if(channel.type == PayType.Web){
                    location.href = data!.data;
                }

            }
        }
    };

    return {
        showPay,
    };
};
