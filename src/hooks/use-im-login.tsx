import { useEffect, useRef, useState } from "react";
import { imLoginUserAuth } from "@/lib/api/message";
import { useQuery } from "@tanstack/react-query";
import { useConfigList } from "./use-config-list";
import TUIChatEngine from "@tencentcloud/chat-uikit-engine";
import { useFullUserInfo } from "./use-full-user-info";
import rtcLogic from "@/utils/rtc-logic";
import { RtcCallModalRef } from "@/components/work/rtc-call-modal/rtc-call-modal";

export const useImLogin = (modalRef: React.RefObject<RtcCallModalRef>) => {
  const [loading, setLoading] = useState(true);

  const isLoggedRef = useRef(false);

  const { data: fullUserInfo } = useFullUserInfo();

  const { data } = useQuery({
    queryKey: ["logged", "im-login"],
    queryFn: imLoginUserAuth,
    staleTime: Infinity,
    enabled: !!fullUserInfo,
  });

  const { getSystemConfig } = useConfigList();
  const SDKAppID = Number(getSystemConfig("TENCENT_IM_APPID"));

  // 登录
  useEffect(() => {
    if (data?.data && SDKAppID) {
      const { userId, sign } = data.data;

      TUIChatEngine.login({
        SDKAppID,
        userID: userId,
        userSig: sign,
        useUploadPlugin: true,
      }).finally(() => {
        isLoggedRef.current = true;
        setLoading(false);
        rtcLogic.init(modalRef);
      });
    }

    return () => {
      if (isLoggedRef.current) {
        TUIChatEngine.logout();
        setLoading(true);
        isLoggedRef.current = false;
        rtcLogic.destroy();
      }
    };
  }, [data, SDKAppID, modalRef]);

  return { loading };
};
