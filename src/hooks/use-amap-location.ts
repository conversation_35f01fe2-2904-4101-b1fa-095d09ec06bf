import { useEffect } from "react";
import { useConfigList } from "./use-config-list";
import { useFullUserInfo } from "./use-full-user-info";
import { getCurrentPosition } from "@/utils/get-current-position";
import { useMutation } from "@tanstack/react-query";
import { reportLocation } from "@/lib/api/user";
import { setEnabledLocation, setLocation } from "@/utils/enabled-location";

export const useAmapLocation = () => {
  const { getSystemConfig } = useConfigList();
  const { data: userInfo } = useFullUserInfo();
  const { mutateAsync } = useMutation({
    mutationFn: reportLocation,
  });

  const amapKey = getSystemConfig("AMAP_WEB_KEY");

  const [key, secret] = (amapKey?.split("@") ?? []) as [string, string];

  useEffect(() => {
    if (key && secret) {
      getCurrentPosition(key, secret).then((data) => {
        setLocation(data);

        mutateAsync(data).then(() => {
          setEnabledLocation(true);
          console.log("成功上传定位", data);
        });
      });
    }

    return () => {
      setEnabledLocation(false);
      setLocation(null);
    };
  }, [mutateAsync, userInfo?.id, key, secret]);
};
