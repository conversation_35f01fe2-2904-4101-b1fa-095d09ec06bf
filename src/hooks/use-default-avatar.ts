import {
  defaultAvatar,
  firstRechargeBanner,
  recommendUserBanner,
  returnUserBanner,
} from "@/lib/api/system";
import { Resource } from "@/type/resource";
import { useQueries } from "@tanstack/react-query";

export const useDefaultResource = () => {
  const [
    { isLoading: defaultAvatarLoading, data: defaultAvatarData },
    { isLoading: firstRechargeBannerLoading, data: firstRechargeBannerData },
    { isLoading: returnUserBannerLoading, data: returnUserBannerData },
    { isLoading: recommendUserBannerLoading, data: recommendUserBannerData },
  ] = useQueries({
    queries: [
      {
        queryKey: ["default-avatar"],
        queryFn: () => defaultAvatar(),
        staleTime: Infinity,
      },
      {
        queryKey: ["first-recharge-banner"],
        queryFn: () => firstRechargeBanner(),
        staleTime: Infinity,
      },
      {
        queryKey: ["return-user-banner"],
        queryFn: () => returnUserBanner(),
        staleTime: Infinity,
      },
      {
        queryKey: ["recommend-user-banner"],
        queryFn: () => recommendUserBanner(),
        staleTime: Infinity,
      },
    ],
  });

  const getDefaultAvatar = (gender?: number): Resource | undefined => {
    const avatars =
      defaultAvatarData?.data?.filter((item) => {
        if (gender === undefined) {
          return true;
        }

        return item.sign === gender.toString();
      }) ?? [];

    const random = Math.floor(Math.random() * avatars.length);

    const avatar = avatars[random];

    return avatar
      ? {
          id: avatar.resource,
          images: {
            Thumbnail: {
              url: avatar.resourceUrl,
            },
            Original: {
              url: avatar.resourceUrl,
            },
          },
        }
      : undefined;
  };

  return {
    isLoading:
      defaultAvatarLoading ||
      firstRechargeBannerLoading ||
      returnUserBannerLoading ||
      recommendUserBannerLoading,
    getDefaultAvatar,
    firstRechargeBanner: firstRechargeBannerData?.data,
    returnUserBanner: returnUserBannerData?.data,
    recommendUserBanner: recommendUserBannerData?.data,
  };
};
