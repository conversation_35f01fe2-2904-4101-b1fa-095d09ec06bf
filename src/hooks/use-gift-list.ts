import { giftList } from "@/lib/api/system";
import { GiftType } from "@/utils/enums";
import { useQuery } from "@tanstack/react-query";

export const giftListKey = ["logged", "gift-list"];

export const useGiftList = () => {
  const { data, isFetching: isLoading } = useQuery({
    queryKey: giftListKey,
    queryFn: giftList,
    staleTime: Infinity,
  });

  const getByType = (type: GiftType) => {
    return data?.data?.filter((item) => item.type === type);
  };

  return {
    isLoading,
    getByType,
    data: data?.data,
  };
};
