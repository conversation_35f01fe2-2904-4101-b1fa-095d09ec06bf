import { useQuery } from "@tanstack/react-query";
import { getPreviewById } from "@/lib/api/user";
import { ResourcePreviewData } from "@/type/resource-preview-data";

export const usePreviewUrl = (data?: ResourcePreviewData) => {
  const { id, url, preview } = data ?? {};

  const { data: previewData, isLoading } = useQuery({
    queryKey: ["resource", "preview", id],
    queryFn: () => getPreviewById(id!),
    staleTime: Infinity,
    enabled: !!id && !preview,
  });

  if (!id || preview) {
    return { url: url! };
  }

  return { url: previewData?.data?.url, isLoading };
};
