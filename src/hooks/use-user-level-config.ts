import { userConfigList } from "@/lib/api/user";
import { useQuery } from "@tanstack/react-query";

export const useUserLevelConfig = () => {
  const { isLoading, data } = useQuery({
    queryKey: ["user-level-config"],
    queryFn: () => userConfigList(),
    staleTime: Infinity,
  });

  /** 根据等级和类型获取配置结果对象 */
  const getByLevelAndType = (level: number, type: number) => {
    return data?.data?.find(
      (item) => item.level === level && item.type === type
    );
  };

  /** 根据等级类型获得等级列表 */
  const getType = (type: number) => {
    return data?.data?.filter((item) => item.type === type);
  };

  return { isLoading, getByLevelAndType, getType };
};
