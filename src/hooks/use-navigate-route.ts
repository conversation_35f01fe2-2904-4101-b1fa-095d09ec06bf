import { generateRoute } from "@/utils/generate-route";
import { useCallback } from "react";
import { NavigateOptions, useNavigate } from "react-router-dom";

export const useNavigateRoute = () => {
  const navigate = useNavigate();

  const navigateRoute = useCallback(
    (
      route: string,
      params?: Record<string, any>,
      options?: NavigateOptions
    ) => {
      navigate(generateRoute(route, params), options);
    },
    [navigate]
  );

  const navigateBack = useCallback(
    (delta = -1) => {
      navigate(delta);
    },
    [navigate]
  );

  return {
    navigateBack,
    navigateRoute,
  };
};
