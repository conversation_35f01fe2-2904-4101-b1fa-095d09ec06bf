import { useQueryClient } from "@tanstack/react-query";

export const usePageListRefresh = (queryKey: any[]) => {
  const queryClient = useQueryClient();

  /**
   * 只保留第一页数据，保证 refetch 时只请求第一页
   */
  const handleRefresh = async () => {
    queryClient.setQueryData(
      queryKey,
      (data: { pages: any; pageParams: any }) => ({
        pages: data.pages.slice(0, 1),
        pageParams: data.pageParams.slice(0, 1),
      })
    );
    await queryClient.refetchQueries({ queryKey });
  };

  return { handleRefresh };
};
