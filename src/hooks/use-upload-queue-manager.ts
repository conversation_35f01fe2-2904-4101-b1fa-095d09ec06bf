import { UploadQueueItem, useUploadQueue } from "@/store/upload-queue";
import { produce } from "immer";
import { v4 as uuidv4 } from "uuid";
import { useUploadInfo } from "./use-upload-info";
import { uploadFile } from "@/utils/upload-file";
import { fetch } from "@/lib/request";
import {
  getAudioDuration,
  getVideoDuration,
  isAudio,
  isVideo,
} from "@/utils/common";

export const useUploadQueueManager = () => {
  const { uploadQueue, setUploadQueue } = useUploadQueue();

  const { imageUploadInfo, videoUploadInfo } = useUploadInfo();

  const addToQueue = async (item: UploadQueueItem, onFinish?: () => void) => {
    setUploadQueue(
      produce(uploadQueue, (draft) => {
        draft.push({
          id: uuidv4(),
          date: +new Date(),
          subtitle: "上传中...",
          ...item,
        });
      })
    );

    const res = await uploadAll(item);

    await publish(res, onFinish);
  };

  const uploadAll = async (data: UploadQueueItem): Promise<UploadQueueItem> => {
    const checkAndUpload = async (item: any): Promise<any> => {
      if (item instanceof File) {
        let duration: number | undefined = undefined;

        if (isVideo(item)) {
          duration = await getVideoDuration(item);
        }

        if (isAudio(item)) {
          duration = await getAudioDuration(item);
        }

        const res = await uploadFile(
          item,
          isVideo(item) ? videoUploadInfo! : imageUploadInfo!,
          {
            duration,
          }
        );

        return res?.id ?? ""; // 返回URL而不是File对象
      } else if (Array.isArray(item)) {
        return Promise.all(item.map(checkAndUpload));
      } else if (typeof item === "object" && item !== null) {
        const result: Record<string, any> = {};
        for (const [key, value] of Object.entries(item)) {
          result[key] = await checkAndUpload(value);
        }
        return result;
      }
      return item; // 如果不是File、数组或对象，直接返回原值
    };

    if (data.params) {
      if (Array.isArray(data.params)) {
        data.params = await Promise.all(data.params.map(checkAndUpload));
      } else if (typeof data.params === "object") {
        const result: Record<string, any> = {};
        for (const [key, value] of Object.entries(data.params)) {
          result[key] = await checkAndUpload(value);
        }
        data.params = result;
      }
    }

    return data; // 返回处理后的UploadQueueItem
  };

  const publish = async (data: UploadQueueItem, onFinish?: () => void) => {
    const { ok, msg } = await fetch.post(data.url, data.params);

    if (ok) {
      onFinish?.();

      setUploadQueue(
        produce(uploadQueue, (draft) => {
          draft.splice(
            draft.findIndex((item) => item.id === data.id),
            1
          );
        })
      );
    } else {
      setUploadQueue(
        produce(uploadQueue, (draft) => {
          const item = draft.find((item) => item.id === data.id);

          if (item) {
            item.error = msg ?? "上传失败，请重试";
          }
        })
      );
    }
  };

  return {
    addToQueue,
  };
};
