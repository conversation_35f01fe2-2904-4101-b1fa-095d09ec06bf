import { userVip } from "@/lib/api/user";
import { useQuery } from "@tanstack/react-query";

export const userVipQueryKey = ["logged", "user-vip"];

export const useUserVip = () => {
  const { data, isFetching: isLoading } = useQuery({
    queryKey: userVipQueryKey,
    queryFn: userVip,
    staleTime: Infinity,
  });

  const hasPermission = (sign: string) => {
    return !!data?.data?.marks.includes(sign);
  };

  return {
    data: data?.data,
    isLoading,
    hasPermission,
  };
};
