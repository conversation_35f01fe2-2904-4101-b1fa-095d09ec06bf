import { LayoutConfigShare } from "@/type/layout-config";
import { LayoutConfigKey } from "@/utils/layout-config-key";
import { layoutConfigOptions } from "@/utils/query-options";
import { useQuery } from "@tanstack/react-query";

export const useShareList = () => {
  const { data, isLoading } = useQuery(
    layoutConfigOptions<LayoutConfigShare>(LayoutConfigKey.Share)
  );

  return { data: data?.data?.items ?? [], isLoading };
};
