import { useEffect, useState } from "react";

/**
 * 倒计时处理器
 * @param time 单位秒
 */
export const useCountDownTimer = (time: number, defaultRunning = false) => {
  const [seconds, setSeconds] = useState(time);
  const [isRunning, setIsRunning] = useState(defaultRunning);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isRunning && seconds > 0) {
      timer = setInterval(() => {
        setSeconds((prevSeconds) => prevSeconds - 1);
      }, 1000);
    } else if (seconds === 0) {
      setIsRunning(false);
    }
    return () => clearInterval(timer);
  }, [isRunning, seconds]);

  const handleStart = () => {
    setSeconds(time);
    setIsRunning(true);
  };

  return {
    seconds,
    isRunning,
    handleStart,
  };
};
