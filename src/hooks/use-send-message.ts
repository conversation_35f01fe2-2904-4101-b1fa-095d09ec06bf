import { TUIChatService, TUIChatEngine } from "@tencentcloud/chat-uikit-engine";

export const useSendMessage = (targetId?: string) => {
  const sendTextMessage = (text: string) => {
    TUIChatService.sendTextMessage({
      payload: {
        text,
      },
    });
  };

  const sendImageMessage = (file: File) => {
    TUIChatService.sendImageMessage({
      payload: {
        file,
      },
    });
  };

  const sendVideoMessage = (file: File) => {
    TUIChatService.sendVideoMessage({
      payload: {
        file,
      },
    });
  };

  const sendAudioMessage = async (file: File, second: number) => {
    const audioMessage = TUIChatEngine.chat.createAudioMessage({
      payload: {
        file,
      },
      to: targetId!,
      conversationType: TUIChatEngine.TYPES.CONV_C2C,
    });

    audioMessage.payload.second = second;

    await TUIChatEngine.chat.sendMessage(audioMessage);

    TUIChatService.updateMessageList([audioMessage], "push");
  };

  return {
    sendTextMessage,
    sendImageMessage,
    sendVideoMessage,
    sendAudioMessage,
  };
};
