import { configList } from "@/lib/api/system";
import { SystemConfig } from "@/type/system-config";
import { detectMobileOS } from "@/utils/detect-mobile-os";
import { useQuery } from "@tanstack/react-query";

export const useConfigList = () => {
  const { isLoading, data } = useQuery({
    queryKey: ["config-list"],
    queryFn: () => configList(),
    staleTime: Infinity,
  });

  const getSystemConfig = (key: keyof SystemConfig) => {
    const value = data?.data?.[key];

    if (!value) return null;

    const platform = detectMobileOS();
    const regexp = new RegExp(`\\[@${platform}=.*@\\]`);

    if (regexp.test(value)) {
      //  具有平台参数
      const match = value.match(regexp)?.[0];

      if (match) {
        return match.replace(`[@${platform}=`, "").replace("@]", "");
      }
    }

    return value;
  };

  return { isLoading, getSystemConfig };
};
