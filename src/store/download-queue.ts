import { atom, useAtom } from "jotai";

export interface DownloadQueueItem {
  title: string;
  url: string;
  params?: Record<string, any>;
  id?: string;
  subtitle?: string;
  error?: string;
  date?: number;
}

const downloadQueueAtom = atom<DownloadQueueItem[]>([]);

export const useDownloadQueue = () => {
  const [downloadQueue, setDownloadQueue] = useAtom(downloadQueueAtom);

  return {
    downloadQueue,
    setDownloadQueue,
  };
};
