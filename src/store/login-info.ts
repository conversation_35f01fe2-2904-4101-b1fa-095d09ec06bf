import { UserLoginResult } from "@/type/user-login-result";
import { atom, useAtom } from "jotai";

interface LoginInfoAtom {
  loginData?: UserLoginResult;
  // 性别
  gender?: number;
  // 角色
  role?: number;
  // 性取向
  sexualOrientation?: number;
  // 程度
  extent?: number;
}

const loginInfoAtom = atom<LoginInfoAtom>({});

export const useLoginInfo = () => {
  const [loginInfo, setInfo] = useAtom(loginInfoAtom);

  const setLoginInfo = (data: LoginInfoAtom) => {
    // 只传入需要更新的数据
    setInfo({
      ...loginInfo,
      ...data,
    });
  };

  return {
    loginInfo,
    setLoginInfo,
  };
};
