import { atom, useAtom } from "jotai";

export interface UploadQueueItem {
  title: string;
  url: string;
  params: Record<string, any>;
  id?: string;
  subtitle?: string;
  error?: string;
  date?: number;
}

const uploadQueueAtom = atom<UploadQueueItem[]>([]);

export const useUploadQueue = () => {
  const [uploadQueue, setUploadQueue] = useAtom(uploadQueueAtom);

  return {
    uploadQueue,
    setUploadQueue,
  };
};
