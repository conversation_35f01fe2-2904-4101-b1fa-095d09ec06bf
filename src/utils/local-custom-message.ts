import { LOCAL_CUSTOM_MESSAGE } from "@/type/cache";

class LocalCustomMessage {
  private userId?: string;

  init(userId: string) {
    this.userId = userId;
  }

  getKey() {
    return `${this.userId}_${LOCAL_CUSTOM_MESSAGE}`;
  }

  getMessageList() {
    try {
      const data = localStorage.getItem(this.getKey());
      return data ? JSON.parse(data) : {};
    } catch (error) {
      console.error(error);
    }
  }

  setMessage(id: string) {
    const data = this.getMessageList();
    data[id] = true;
    localStorage.setItem(this.getKey(), JSON.stringify(data));
  }

  getMessageStatus(id: string) {
    const data = this.getMessageList();
    return data[id] ?? false;
  }
}

export const localCustomMessage = new LocalCustomMessage();
