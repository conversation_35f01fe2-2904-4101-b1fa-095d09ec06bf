import { v4 as uuidv4 } from "uuid";
import { defaultCol, defaultGap, defaultRatio } from "./constant";
import { DEVICE_ID } from "@/type/cache";

/**
 * 获取字符串开头的数字
 */
export const extractLeadingNumbers = (value = "") => {
  const regex = /^\d+/;
  const match = value.match(regex);

  return match ? match[0] : "";
};

/**
 * 根据 size 计算资源展示的布局
 */
export const calcResourceLayout = (size: number) => {
  let col = defaultCol;
  let ratio = defaultRatio;
  let gap = defaultGap;

  if (size === 1) {
    col = 1;
    ratio = 2;
  }

  if (size === 4) {
    col = 2;
    ratio = 1.5;
    gap = 10;
  }

  return {
    col,
    ratio,
    gap,
  };
};

/**
 * 32 位有符号整数颜色值转换为 rgba
 * 可强制设置透明度
 */
export const colorToRgba = (color: number, alpha?: number) => {
  const unsignedNumber = color >>> 0; // 无符号右移，将负数转换为无符号整数
  const hexString = unsignedNumber.toString(16).padStart(8, "0"); // 转换为16进制字符串并补全为8位

  // 提取 ARGB 各个分量
  const alphaValue = parseInt(hexString.substring(0, 2), 16);
  const red = parseInt(hexString.substring(2, 4), 16);
  const green = parseInt(hexString.substring(4, 6), 16);
  const blue = parseInt(hexString.substring(6, 8), 16);

  // 转换为 RGBA 格式
  return `rgba(${red}, ${green}, ${blue}, ${alpha ?? alphaValue / 255})`;
};

export const wakeupApp = (data?: Record<string, any>, channelCode?: string) => {
  window.wakeupOrInstall?.(data, channelCode);
};

/**
 * 将key为 {a.b.c:1}，转换为对象形式 {a:{b:{c:1}}}
 */
export const parseObject = (obj: Record<string, any>) => {
  return Object.keys(obj).reduce((acc, key) => {
    key.split(".").reduce((nestedObj, part, index, array) => {
      if (index === array.length - 1) {
        nestedObj[part] = obj[key];
      } else {
        nestedObj[part] = nestedObj[part] || {};
      }
      return nestedObj[part];
    }, acc);
    return acc;
  }, {} as Record<string, any>);
};

/**
 * 获取设备 ID
 * 存储在 localStorage 中
 */
export const getDeviceId = () => {
  const deviceId = localStorage.getItem(DEVICE_ID);
  if (!deviceId) {
    const newDeviceId = uuidv4();
    localStorage.setItem(DEVICE_ID, newDeviceId);

    return newDeviceId;
  }

  return deviceId;
};
