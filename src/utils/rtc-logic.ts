import TUIChatEngine from "@tencentcloud/chat-uikit-engine";
import { RtcCallState, RtcRoomScene, RtcRoomType, TSignaling } from "./enums";
import {
  rtcRoomHangUp,
  rtcRoomCreate,
  rtcRoomExpand,
  rtcRoomGetById,
  rtcRoom<PERSON>oin,
} from "@/lib/api/chat";
import { User } from "@/type/user";
import { RtcRoomResult } from "@/type/rtc-room-result";
import { RtcCallModalRef } from "@/components/work/rtc-call-modal/rtc-call-modal";
import { Toast } from "antd-mobile";
import { queryClient } from "@/provider/query-client";
import { fullUserInfoKey } from "@/hooks/use-full-user-info";
import { ApiResponse } from "@/lib/request";
import TRTC, { EnterRoomConfig } from "trtc-sdk-v5";
import React from "react";
import { mainVideoId, secondaryVideoId } from "./constant";
import rtcCallVoice from "@/assets/voice/rtc_call.wav";
import rtcCloseVoice from "@/assets/voice/rtc_close.wav";

const RTC_TYPE = 1;

class RtcLogic {
  private inviteId: string | null = null;
  private modalRef: React.RefObject<RtcCallModalRef> | null = null;
  private userInfo: User | undefined = undefined;
  private trtc: TRTC | null = null;
  private members = new Set<string>();
  private timer: NodeJS.Timeout | null = null;
  private expandInfoQueryTimer: NodeJS.Timeout | null = null;
  private localVideoId = mainVideoId;

  private state: RtcCallState = RtcCallState.Idle;
  private duration = 0;
  private expandInfo: { tips: string } | null = null;
  private room: RtcRoomResult | null = null;
  private sponsor: boolean = false;
  private enabledMicrophone = false;
  private enabledSpeaker = false;
  private frontCamera = false;

  private callAudio: HTMLAudioElement | null = null;
  private callAudioEnded = false;

  constructor() {
    // 绑定所有方法到当前实例
    this.onNewInvitationReceived = this.onNewInvitationReceived.bind(this);
    this.onInviteeAccepted = this.onInviteeAccepted.bind(this);
    this.onInviteeRejected = this.onInviteeRejected.bind(this);
    this.onInvitationCancelled = this.onInvitationCancelled.bind(this);
    this.onInvitationTimeout = this.onInvitationTimeout.bind(this);
    this.onKickedOut = this.onKickedOut.bind(this);
    this.onRemoteUserEnter = this.onRemoteUserEnter.bind(this);
    this.onRemoteUserExit = this.onRemoteUserExit.bind(this);
    this.onRemoteVideoAvailable = this.onRemoteVideoAvailable.bind(this);

    // 创建trtc对象
    this.trtc = TRTC.create();
  }

  init(modalRef: React.RefObject<RtcCallModalRef>) {
    this.modalRef = modalRef;
    this.state = RtcCallState.Idle;

    this.userInfo =
      queryClient.getQueryData<ApiResponse<User>>(fullUserInfoKey)?.data;

    this.modalRef?.current?.setState(this.state);
    this.signalingListener();
    this.rtcListener();
  }

  destroy() {
    this.state = RtcCallState.Idle;

    this.removeSignalingListener();
    this.removeRtcListener();
  }

  signalingListener() {
    // 收到新的邀请
    TUIChatEngine.chat.addSignalingListener(
      TSignaling.NEW_INVITATION_RECEIVED,
      this.onNewInvitationReceived
    );

    // 信令邀请被接受
    TUIChatEngine.chat.addSignalingListener(
      TSignaling.INVITEE_ACCEPTED,
      this.onInviteeAccepted
    );
    // 信令邀请被拒绝
    TUIChatEngine.chat.addSignalingListener(
      TSignaling.INVITEE_REJECTED,
      this.onInviteeRejected
    );
    // 信令邀请被取消
    TUIChatEngine.chat.addSignalingListener(
      TSignaling.INVITATION_CANCELLED,
      this.onInvitationCancelled
    );
    // 邀请超时
    TUIChatEngine.chat.addSignalingListener(
      TSignaling.INVITATION_TIMEOUT,
      this.onInvitationTimeout
    );
  }

  removeSignalingListener() {
    TUIChatEngine.chat.removeSignalingListener(
      TSignaling.NEW_INVITATION_RECEIVED,
      this.onNewInvitationReceived
    );
    TUIChatEngine.chat.removeSignalingListener(
      TSignaling.INVITEE_ACCEPTED,
      this.onInviteeAccepted
    );
    TUIChatEngine.chat.removeSignalingListener(
      TSignaling.INVITEE_REJECTED,
      this.onInviteeRejected
    );
    TUIChatEngine.chat.removeSignalingListener(
      TSignaling.INVITATION_CANCELLED,
      this.onInvitationCancelled
    );
    TUIChatEngine.chat.removeSignalingListener(
      TSignaling.INVITATION_TIMEOUT,
      this.onInvitationTimeout
    );
  }

  rtcListener() {
    // 被踢出房间
    this.trtc?.on(TRTC.EVENT.KICKED_OUT, this.onKickedOut);
    // 远端用户进入房间
    this.trtc?.on(TRTC.EVENT.REMOTE_USER_ENTER, this.onRemoteUserEnter);
    // 远端用户离开房间
    this.trtc?.on(TRTC.EVENT.REMOTE_USER_EXIT, this.onRemoteUserExit);
    // 远端用户视频流状态变化
    this.trtc?.on(
      TRTC.EVENT.REMOTE_VIDEO_AVAILABLE,
      this.onRemoteVideoAvailable
    );
  }

  removeRtcListener() {
    this.trtc?.off(TRTC.EVENT.KICKED_OUT, this.onKickedOut);
    this.trtc?.off(TRTC.EVENT.REMOTE_USER_ENTER, this.onRemoteUserEnter);
    this.trtc?.off(TRTC.EVENT.REMOTE_USER_EXIT, this.onRemoteUserExit);
    this.trtc?.off(
      TRTC.EVENT.REMOTE_VIDEO_AVAILABLE,
      this.onRemoteVideoAvailable
    );
  }

  private async onNewInvitationReceived(event: any) {
    const { data: invitationData, inviteID } = event.data;

    try {
      const { type, roomId } = JSON.parse(invitationData);

      if (type !== RTC_TYPE) {
        return;
      }

      if (!roomId) {
        return;
      }

      // 如果当前已在通话中，则直接拒绝
      if (this.state !== RtcCallState.Idle) {
        TUIChatEngine.chat.reject({
          inviteID,
          data: JSON.stringify({
            type: RTC_TYPE,
            roomId: roomId,
            desc: "通话已结束",
          }),
        });
        return;
      }

      // todo: 如果当前在直播中，则直接拒绝

      const { data: roomData } = await rtcRoomGetById(roomId);

      if (!roomData) {
        return;
      }

      this.joinC2CCall(roomData, inviteID);
    } catch (error) {
      console.error(error);
    }
  }

  private onInviteeAccepted(event: any) {
    const { inviteID } = event.data;

    if (
      inviteID !== this.inviteId ||
      this.state !== RtcCallState.Wait ||
      !this.sponsor
    ) {
      return;
    }

    this.accepted();
  }

  private onInviteeRejected(event: any) {
    const { inviteID } = event.data;

    if (
      inviteID !== this.inviteId ||
      this.state !== RtcCallState.Wait ||
      !this.sponsor
    ) {
      return;
    }

    this.hangUp("对方正忙");
  }

  private onInvitationCancelled(event: any) {
    const { inviteID } = event.data;

    if (
      inviteID !== this.inviteId ||
      this.state !== RtcCallState.Wait ||
      this.sponsor
    ) {
      return;
    }

    this.hangUp("对方取消通话");
  }

  private onInvitationTimeout(event: any) {
    const { inviteID } = event.data;

    if (inviteID !== this.inviteId || this.state !== RtcCallState.Wait) {
      return;
    }

    this.hangUp("未接听");
  }

  private onKickedOut() {
    // 被踢出房间
    this.hangUp();
  }

  private onRemoteUserEnter(event: any) {
    this.members.add(event.userId);

    this.checkAcceptedState();
  }

  private onRemoteUserExit(event: any) {
    this.members.delete(event.userId);

    if (
      !this.members.size ||
      (this.members.size === 1 && this.members.has(this.userInfo?.id!))
    ) {
      // 如果房间无人了或者房间里面只有自己则挂断
      this.hangUp();
    }
  }

  private onRemoteVideoAvailable(event: any) {
    const { userId } = event;

    this.trtc?.startRemoteVideo({
      userId,
      streamType: TRTC.TYPE.STREAM_TYPE_MAIN,
      view: "rtc-secondary-video",
    });

    this.switchVideoView();
  }

  async startC2CCall(
    roomType: RtcRoomType,
    {
      userInfo,
      targetUser,
    }: {
      userInfo: User;
      targetUser: User;
    }
  ) {
    const { ok, data: roomData } = await rtcRoomCreate({
      type: roomType,
      scene: RtcRoomScene.C2c,
      targetUserId: targetUser.id!,
    });

    if (ok && roomData) {
      const desc = `${userInfo.nickname}发起了${
        roomType === RtcRoomType.Audio ? "语音" : "视频"
      }通话`;

      const { inviteID } = await TUIChatEngine.chat.invite({
        timeout: 60,
        userID: roomData?.calledPartyUser?.id!,
        data: JSON.stringify({
          type: RTC_TYPE,
          roomId: roomData?.id,
          desc,
        }),
        offlinePushInfo: {
          title: "邀请你进入通话",
          description: desc,
        },
      });

      this.joinC2CCall(roomData, inviteID);
    }
  }

  async enterRoom(params: EnterRoomConfig) {
    await this.trtc?.enterRoom(params);

    this.members.add(this.userInfo?.id!);
  }

  async exitRoom() {
    await this.trtc?.exitRoom();

    this.members.delete(this.userInfo?.id!);
  }

  async accepted() {
    if (this.state !== RtcCallState.Wait) {
      return;
    }

    // 停止呼叫音频
    this.stopCallVoice();

    this.state = RtcCallState.Connecting;
    this.modalRef?.current?.setState(this.state);

    try {
      if (!this.sponsor) {
        TUIChatEngine.chat.accept({
          inviteID: this.inviteId!,
          data: JSON.stringify({
            type: RTC_TYPE,
            roomId: this.room?.id,
            desc: "通话已接听",
          }),
        });
      }

      const { ok, data: joinData } = await rtcRoomJoin({ id: this.room?.id! });

      if (ok && joinData) {
        const params = {
          sdkAppId: joinData.appid!,
          userId: joinData.userId!,
          userSig: joinData.sign!,
          roomId: joinData.roomNumber!,
        };

        await this.enterRoom(params);

        this.trtc?.startLocalAudio();
      }
    } catch {
      this.hangUp();
    }
  }

  reject() {
    if (this.state !== RtcCallState.Wait) {
      return;
    }

    TUIChatEngine.chat.reject({
      inviteID: this.inviteId!,
      data: JSON.stringify({
        type: RTC_TYPE,
        roomId: this.room?.id,
        desc: "通话已结束",
      }),
    });

    this.hangUp();
  }

  cancel() {
    if (this.state !== RtcCallState.Wait) {
      return;
    }

    TUIChatEngine.chat.cancel({
      inviteID: this.inviteId!,
      data: JSON.stringify({
        type: RTC_TYPE,
        roomId: this.room?.id,
        desc: "通话已结束",
      }),
    });

    this.hangUp();
  }

  hangUp(note?: string) {
    // 停止呼叫音频
    this.stopCallVoice();

    this.playCloseVoice();

    rtcRoomHangUp({
      id: this.room?.id!,
      note,
    }).finally(() => {
      if (note) {
        Toast.show({
          content: note,
        });
      }
    });

    // todo: 退出房间
    this.exitRoom();

    this.trtc?.stopLocalAudio();
    this.trtc?.stopLocalVideo();

    this.modalRef?.current?.close();
    this.state = RtcCallState.Idle;
  }

  joinC2CCall(room: RtcRoomResult, inviteId: string) {
    this.room = room;
    this.inviteId = inviteId;
    this.state = RtcCallState.Wait;
    this.sponsor = this.userInfo?.id === room.callingPartyUser?.id;

    this.modalRef?.current?.show();
    this.modalRef?.current?.setRoom(room);
    this.modalRef?.current?.setSponsor(this.sponsor);
    this.modalRef?.current?.setState(this.state);

    this.playCallVoice();
  }

  checkAcceptedState() {
    if (this.state !== RtcCallState.Connecting) {
      return;
    }

    if (this.members.size < 2) {
      return;
    }

    if (!this.userInfo?.id || !this.members.has(this.userInfo.id)) {
      return;
    }

    this.state = RtcCallState.Talking;

    this.modalRef?.current?.setState(this.state);
    this.startDurationTimer();
    this.startExpandInfoQueryTimer();

    this.switchMicrophone(true);
    this.switchSpeaker(true);
  }

  startDurationTimer() {
    this.duration = 0;
    this.modalRef?.current?.setDuration(this.duration);

    this.timer = setInterval(() => {
      this.duration++;
      // 通知 modal 更新时长
      this.modalRef?.current?.setDuration(this.duration);
    }, 1000);
  }

  stopDurationTimer() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }

  startExpandInfoQueryTimer() {
    this.expandInfoQueryTimer = setInterval(() => {
      rtcRoomExpand(this.room?.id!).then(({ ok, data }) => {
        if (ok && data) {
          this.expandInfo = data;

          this.modalRef?.current?.setExpandInfo(this.expandInfo);
        }
      });
    }, 10 * 1000);
  }

  stopExpandInfoQueryTimer() {
    if (this.expandInfoQueryTimer) {
      clearInterval(this.expandInfoQueryTimer);
      this.expandInfoQueryTimer = null;
    }
  }

  switchMicrophone(enabled: boolean) {
    this.enabledMicrophone = enabled;

    this.modalRef?.current?.setEnabledMicrophone(this.enabledMicrophone);

    this.trtc?.updateLocalAudio({ mute: !enabled });
  }

  async switchCamera(enabled: boolean) {
    this.frontCamera = enabled;

    this.modalRef?.current?.setFrontCamera(this.frontCamera);

    this.trtc?.updateLocalVideo({
      view: this.localVideoId,
      option: { useFrontCamera: this.frontCamera },
    });
  }

  switchSpeaker(enabled: boolean) {
    this.enabledSpeaker = enabled;

    this.modalRef?.current?.setEnabledSpeaker(this.enabledSpeaker);

    TRTC.setCurrentSpeaker(
      this.enabledSpeaker ? TRTC.TYPE.SPEAKER : TRTC.TYPE.HEADSET
    );
  }

  startLocalVideo() {
    this.trtc?.startLocalVideo({
      view: "rtc-main-video",
    });
  }

  switchVideoView() {
    if (this.localVideoId === mainVideoId) {
      this.updateVideoView(secondaryVideoId, mainVideoId);
    } else {
      this.updateVideoView(mainVideoId, secondaryVideoId);
    }
  }

  updateVideoView(local: string, remote: string) {
    this.localVideoId = local;

    const user = this.sponsor
      ? this.room?.calledPartyUser
      : this.room?.callingPartyUser;

    if (user) {
      this.trtc?.updateLocalVideo({ view: local });

      this.trtc?.updateRemoteVideo({
        userId: user.id!,
        streamType: TRTC.TYPE.STREAM_TYPE_MAIN,
        view: remote,
      });
    }
  }

  playCallVoice() {
    if (this.callAudio) {
      // 如果已经在播放，则不需要重新创建
      return;
    }

    this.callAudio = new Audio(rtcCallVoice);
    this.callAudioEnded = false;

    // 音频播放结束后的回调
    this.callAudio.onended = () => {
      this.callAudioEnded = true;
      this.callAudio = null;

      // 如果状态仍然是等待中，则继续播放
      if (this.state === RtcCallState.Wait) {
        setTimeout(() => {
          this.playCallVoice();
        }, 300);
      }
    };

    // 播放失败的处理
    this.callAudio.onerror = (error) => {
      console.error("播放呼叫音频失败:", error);
      this.callAudio = null;

      // 如果状态仍然是等待中，则尝试重新播放
      if (this.state === RtcCallState.Wait && !this.callAudioEnded) {
        setTimeout(() => {
          this.playCallVoice();
        }, 1000);
      }
    };

    // 尝试播放
    this.callAudio.play().catch((error) => {
      console.error("播放音频失败, 可能是用户交互限制:", error);

      // 直接延迟重试，不依赖modalRef的交互回调
      if (this.state === RtcCallState.Wait) {
        setTimeout(() => {
          this.playCallVoice();
        }, 2000);
      }
    });
  }

  playCloseVoice() {
    const audio = new Audio(rtcCloseVoice);
    audio.play();
  }

  // 停止呼叫音频
  stopCallVoice() {
    if (this.callAudio) {
      this.callAudio.pause();
      this.callAudio.currentTime = 0;
      this.callAudio = null;
    }
  }
}

export default new RtcLogic();
