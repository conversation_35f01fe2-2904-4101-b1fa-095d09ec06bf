export const defaultCol = 3;
export const defaultRatio = 1;
export const defaultGap = 3;
export const defaultImage =
  "data:image/png;base64,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";

/** 手机号码校验正则 */
export const phoneRegex = /^1[3-9]\d{9}$/;

/** 身份证号码校验正则 */
export const idcardRegex = /^\d{17}[\dX]$/;

export const loginWelcomeMsg = "您好，\n欢迎来到足趣社区！";
export const bindPhoneMsg = "您好，\n欢迎绑定手机！";

export const calendarHeader = [
  "周日",
  "周一",
  "周二",
  "周三",
  "周四",
  "周五",
  "周六",
];

/** 自定义消息类型 */
export enum CustomMessageType {
  Signaling = -1,
  None = 0,
  SystemNotice = 1,
  Tips = 2,
  ShareCard = 3,
  RedEnvelope = 4,
  Live = 10,
}

/// 自定义消息类型
export enum CustomNodeDataType {
  /// 信令消息：内置消息类型
  Signaling = -1,
  /// 未知
  None = 0,
  /// 系统通知
  SystemNotice = 1,
  /// 系统提示
  Tips = 2,
  /// 分享卡片
  ShareCard = 3,
  /// 红包节点
  RedEnvelope = 4,
  /// 直播节点
  Live = 10,
}

export const mainVideoId = "rtc-main-video";
export const secondaryVideoId = "rtc-secondary-video";
