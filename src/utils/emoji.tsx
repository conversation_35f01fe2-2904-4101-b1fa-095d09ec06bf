import { Image } from "antd-mobile";
import { ReactNode } from "react";

export const unicodeEmojiList = [
  128513, 128514, 128515, 128516, 128517, 128521, 128522, 128523, 128524,
  128525, 128527, 128530, 128531, 128532, 128534, 128536, 128538, 128540,
  128542, 128544, 128545, 128546, 128547, 128548, 128549, 128552, 128553,
  128554, 128555, 128557, 128560, 128561, 128562, 128563, 128565, 128567,
  128568, 128569, 128570, 128571, 128572, 128573, 128574, 128575, 128576,
  128581, 128582, 128583, 128584, 128585, 128586, 128587, 128588, 128589,
  128590, 128591, 9989, 9994, 9995, 10060, 10062, 10067, 10068, 10069, 10071,
  10133, 10134, 10135, 10160, 128640, 128643, 128644, 128645, 128647, 128649,
  128652, 128655, 128657, 128658, 128659, 128661, 128663, 128665, 128666,
  128674, 128676, 128677, 128679, 128680, 128681, 128682, 128683, 128684,
  128685, 128690, 128694, 128697, 128698, 128699, 128700, 128701, 128702,
  128704, 127374, 127377, 127378, 127379, 127380, 127381,
];

export const customTcc1EmojiConfig = {
  icon: "TUIEmoji_Smile.png",
  list: [
    "TUIEmoji_Like.png",
    "TUIEmoji_Ok.png",
    "TUIEmoji_Smile.png",
    "TUIEmoji_Expect.png",
    "TUIEmoji_Blink.png",
    "TUIEmoji_Guffaw.png",
    "TUIEmoji_KindSmile.png",
    "TUIEmoji_Haha.png",
    "TUIEmoji_Cheerful.png",
    "TUIEmoji_Speechless.png",
    "TUIEmoji_Amazed.png",
    "TUIEmoji_Sorrow.png",
    "TUIEmoji_Complacent.png",
    "TUIEmoji_Silly.png",
    "TUIEmoji_Lustful.png",
    "TUIEmoji_Giggle.png",
    "TUIEmoji_Kiss.png",
    "TUIEmoji_Wail.png",
    "TUIEmoji_TearsLaugh.png",
    "TUIEmoji_Trapped.png",
    "TUIEmoji_Mask.png",
    "TUIEmoji_Fear.png",
    "TUIEmoji_BareTeeth.png",
    "TUIEmoji_FlareUp.png",
    "TUIEmoji_Yawn.png",
    "TUIEmoji_Tact.png",
    "TUIEmoji_Stareyes.png",
    "TUIEmoji_ShutUp.png",
    "TUIEmoji_Sigh.png",
    "TUIEmoji_Hehe.png",
    "TUIEmoji_Silent.png",
    "TUIEmoji_Surprised.png",
    "TUIEmoji_Askance.png",
    "TUIEmoji_Shit.png",
    "TUIEmoji_Monster.png",
    "TUIEmoji_Daemon.png",
    "TUIEmoji_Rage.png",
    "TUIEmoji_Fool.png",
    "TUIEmoji_Pig.png",
    "TUIEmoji_Cow.png",
    "TUIEmoji_AI.png",
    "TUIEmoji_Skull.png",
    "TUIEmoji_Bombs.png",
    "TUIEmoji_Coffee.png",
    "TUIEmoji_Cake.png",
    "TUIEmoji_Beer.png",
    "TUIEmoji_Flower.png",
    "TUIEmoji_Watermelon.png",
    "TUIEmoji_Rich.png",
    "TUIEmoji_Heart.png",
    "TUIEmoji_Moon.png",
    "TUIEmoji_Sun.png",
    "TUIEmoji_Star.png",
    "TUIEmoji_RedPacket.png",
    "TUIEmoji_Celebrate.png",
    "TUIEmoji_Bless.png",
    "TUIEmoji_Fortune.png",
    "TUIEmoji_Convinced.png",
    "TUIEmoji_Prohibit.png",
    "TUIEmoji_666.png",
    "TUIEmoji_857.png",
    "TUIEmoji_Knife.png",
  ],
};

export const custom4349EmojiConfig = {
  icon: "naughty.png",
  list: [
    "loveYou.png",
    "love.png",
    "loveHeart.png",
    "arrogant.png",
    "superciliousLook.png",
    "lollipop.png",
    "embrace.png",
    "claspFist.png",
    "blastedRebar.png",
    "despise.png",
    "shutUp.png",
    "firecrackers.png",
    "shit.png",
    "wipeSweat.png",
    "ribbon.png",
    "coloredBall.png",
    "kitchenKnife.png",
    "bad.png",
    "bankNote.png",
    "carriage.png",
    "yawn.png",
    "soldier.png",
    "cry.png",
    "cake.png",
    "knife.png",
    "proud.png",
    "lightBulb.png",
    "fade.png",
    "naughty.png",
    "cloudy.png",
    "daze.png",
    "tremble.png",
    "aircraft.png",
    "blowKiss.png",
    "struggle.png",
    "windmill.png",
    "awkward.png",
    "seduce.png",
    "applause.png",
    "shy.png",
    "sillySmile.png",
    "lantern.png",
    "Xi.png",
    "snicker.png",
    "wave.png",
    "lookBack.png",
    "hunger.png",
    "excitement.png",
    "streetDance.png",
    "terrified.png",
    "surprised.png",
    "coffee.png",
    "kowtow.png",
    "lovely.png",
    "pitiful.png",
    "pickNose.png",
    "skeleton.png",
    "cool.png",
    "willCry.png",
    "sleepy.png",
    "candle.png",
    "basketball.png",
    "coldSweat.png",
    "giftBag.png",
    "gift.png",
    "sweat.png",
    "tear.png",
    "mahjong.png",
    "microphone.png",
    "cat.png",
    "kissYou.png",
    "rose.png",
    "rice.png",
    "noodle.png",
    "feedingBottle.png",
    "sad.png",
    "alarmClock.png",
    "anger.png",
    "sulk.png",
    "football.png",
    "beer.png",
    "ladybug.png",
    "curlLip.png",
    "tableTennis.png",
    "car.png",
    "great.png",
    "knock.png",
    "frog.png",
    "embarrassed.png",
    "fist.png",
    "weak.png",
    "flirting.png",
    "sofa.png",
    "delete.png",
    "lightning.png",
    "victory.png",
    "showLove.png",
    "pistol.png",
    "decline.png",
    "sleep.png",
    "sun.png",
    "ropeSkipping.png",
    "jump.png",
    "titter.png",
    "spit.png",
    "grievance.png",
    "handshake.png",
    "watermelon.png",
    "rain.png",
    "scare.png",
    "offerKiss.png",
    "banana.png",
    "chineseChess.png",
    "heartbreak.png",
    "envelope.png",
    "panda.png",
    "shh.png",
    "drug.png",
    "doubt.png",
    "insidious.png",
    "rightLocomotive.png",
    "rightHum.png",
    "rightTaiChi.png",
    "umbrella.png",
    "moon.png",
    "halo.png",
    "bye.png",
    "bomb.png",
    "torment.png",
    "tissue.png",
    "swearing.png",
    "pig.png",
    "crazy.png",
    "circle.png",
    "baredTeeth.png",
    "diamondRing.png",
    "leftLocomotive.png",
    "leftHum.png",
    "leftTaiChi.png",
    "NO.png",
    "OK.png",
  ],
};

export const getTcc1ImageUrl = (name: string) => {
  return new URL(
    `../assets/custom_face_resource/tcc1/${name}.png`,
    import.meta.url
  ).href;
};

export const get4349ImageUrl = (name: string) => {
  return new URL(
    `../assets/custom_face_resource/4349/${name}.png`,
    import.meta.url
  ).href;
};

export const checkEmoji = (value: string): "4349" | "tcc1" | null => {
  const emoji = `${value}.png`;

  if (custom4349EmojiConfig.list.includes(emoji)) {
    return "4349";
  }
  if (customTcc1EmojiConfig.list.includes(emoji)) {
    return "tcc1";
  }

  return null;
};

export const replaceEmoji = (text: string) => {
  const parts: ReactNode[] = [];
  let lastIndex = 0;
  const regex = /\[(.*?)\]/g;

  for (const match of text.matchAll(regex)) {
    const [fullMatch, emojiName] = match;
    const { index = 0 } = match;

    // 添加匹配之前的文本
    if (index > lastIndex) {
      parts.push(text.slice(lastIndex, index));
    }

    const emojiType = checkEmoji(emojiName);

    if (emojiType) {
      parts.push(
        <span
          key={`${emojiName}-${index}`}
          className="w-[16px] h-[16px] inline-flex"
        >
          <Image
            width="100%"
            height="100%"
            src={
              emojiType === "tcc1"
                ? getTcc1ImageUrl(emojiName)
                : get4349ImageUrl(emojiName)
            }
            placeholder={null}
            fallback={null}
          />
        </span>
      );
    } else {
      parts.push(fullMatch);
    }

    lastIndex = index + fullMatch.length;
  }

  // 添加剩余的文本
  if (lastIndex < text.length) {
    parts.push(text.slice(lastIndex));
  }

  return parts;
};
