export const isVideo = (file: File): boolean => {
  return file.type.startsWith("video/");
};

export const isAudio = (file: File): boolean => {
  return file.type.startsWith("audio/");
};

export const getVideoDuration = (file: File): Promise<number> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    video.preload = "metadata";

    video.onloadedmetadata = () => {
      window.URL.revokeObjectURL(video.src);
      resolve(Math.round(video.duration));
    };

    video.onerror = () => {
      reject("无法获取视频时长");
    };

    video.src = URL.createObjectURL(file);
  });
};

export const getAudioDuration = (file: File): Promise<number> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      const audioContext = new AudioContext();
      audioContext.decodeAudioData(
        event.target!.result as ArrayBuffer,
        (buffer) => {
          const duration = buffer.duration;
          audioContext.close();
          resolve(Math.round(duration));
        },
        (error) => {
          audioContext.close();
          reject("无法解析音频文件: " + error);
        }
      );
    };

    reader.onerror = (error) => {
      reject("无法读取文件: " + error);
    };

    reader.readAsArrayBuffer(file);
  });
};

export const getRandomParams = () => {
  return Math.floor(Math.random() * 10000000).toString();
};
