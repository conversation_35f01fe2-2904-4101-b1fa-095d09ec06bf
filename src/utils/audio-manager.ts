export enum AudioStatus {
  LOADING = "loading",
  PLAYING = "playing",
  STOPPED = "stopped",
}

class AudioManager {
  private audioMap: Map<string, HTMLAudioElement> = new Map();
  private currentAudio: HTMLAudioElement | null = null;
  private currentUrl: string | null = null;
  private stopTimer: NodeJS.Timeout | null = null;
  private endedHandler: (() => void) | null = null;
  private statusCallbacks: Set<(status: AudioStatus) => void> = new Set();

  async play({
    url,
    maxPlayerDuration,
    statusCallback,
  }: {
    url: string;
    maxPlayerDuration?: number;
    statusCallback?: (status: AudioStatus) => void;
  }): Promise<boolean> {
    if (this.currentUrl === url && this.currentAudio?.paused === false) {
      // 如果点击的是当前正在播放的音频，则停止播放
      this.stop();
      return false;
    }

    // 停止当前播放的音频
    this.stop();

    if (statusCallback) {
      this.statusCallbacks.add(statusCallback);
    }

    // 获取或创建音频对象
    let audio = this.audioMap.get(url);
    if (!audio) {
      audio = new Audio(url);
      this.audioMap.set(url, audio);
    }

    try {
      this.currentUrl = url;
      this.currentAudio = audio;
      this.updateStatus(AudioStatus.LOADING);

      // 等待音频加载完成
      await new Promise<void>((resolve, reject) => {
        const canplayHandler = () => {
          audio.removeEventListener("canplay", canplayHandler);
          resolve();
        };
        audio.addEventListener("canplay", canplayHandler);

        // 设置一个超时，以防音频无法加载
        const timeoutId = setTimeout(() => {
          audio.removeEventListener("canplay", canplayHandler);
          reject(new Error("音频加载超时"));
        }, 10000); // 10秒超时

        // 如果音频已经可以播放，立即解析
        if (audio.readyState >= 3) {
          clearTimeout(timeoutId);
          resolve();
        }
      });

      await audio.play();
      this.updateStatus(AudioStatus.PLAYING);

      // 创建并存储 ended 事件处理函数
      this.endedHandler = () => {
        this.stop();
      };
      audio.addEventListener("ended", this.endedHandler);

      // 如果指定了播放时长，设置定时器在指定时间后停止播放
      if (maxPlayerDuration) {
        this.stopTimer = setTimeout(() => {
          this.stop();
        }, maxPlayerDuration * 1000);
      }

      return true; // 播放成功
    } catch (error) {
      console.error("播放音频时出错:", error);
      this.currentAudio = null;
      this.currentUrl = null;
      this.updateStatus(AudioStatus.STOPPED);
      // 清除所有注册的回调
      this.statusCallbacks.clear();

      return false; // 播放失败
    }
  }

  stop() {
    if (this.stopTimer) {
      clearTimeout(this.stopTimer);
      this.stopTimer = null;
    }

    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      // 如果存在 ended 事件处理函数，则移除它
      if (this.endedHandler) {
        this.currentAudio.removeEventListener("ended", this.endedHandler);
        this.endedHandler = null;
      }

      this.currentAudio = null;
      this.currentUrl = null;
      this.updateStatus(AudioStatus.STOPPED);
      // 清除所有注册的回调
      this.statusCallbacks.clear();
    }
  }

  // 新增方法来更新所有注册的回调
  private updateStatus(status: AudioStatus) {
    this.statusCallbacks.forEach((callback) => callback(status));
  }
}

export const audioManager = new AudioManager();
