import { ErrorIcon } from "@/components/svg-icon";
import TUIChatEngine, { IMessageModel } from "@tencentcloud/chat-uikit-engine";
import { ReactNode } from "react";
import { CustomNodeDataType } from "./constant";

export const lastMessageBuild = (message: IMessageModel, isSelf?: boolean) => {
  const { status, isRevoked, cloudCustomData, revokerInfo, type, payload } =
    message;

  if (payload?.data) {
    // 自定义消息
    const data = JSON.parse(payload.data ?? "") as any;

    if (!data) {
      return "不支持的消息格式";
    }

    switch (data.type) {
      case CustomNodeDataType.SystemNotice:
        return `[系统通知] ${data.subtitle}`;
      case CustomNodeDataType.Tips:
        return data.content;
      case CustomNodeDataType.ShareCard:
        return data.title;
      case CustomNodeDataType.Signaling:
        return data.desc ?? "未知信令，请升级客户端版本!";
      case CustomNodeDataType.RedEnvelope:
        return "[红包消息]";
    }
  }

  let icon: ReactNode = null;

  if (status === "fail") {
    // 发送失败
    icon = <ErrorIcon size={16} className="text-[#FF584C]" />;
  }

  // todo：at 消息

  let msgContent: ReactNode = null;

  if (isRevoked) {
    // 撤回消息
    const { revokeByAdmin } = JSON.parse(cloudCustomData) ?? {};

    const name = revokeByAdmin ? "管理员" : isSelf ? "您" : revokerInfo.nick;

    msgContent = `${name}撤回了一条消息`;
  } else {
    switch (type) {
      case TUIChatEngine.TYPES.MSG_CUSTOM:
        {
          msgContent = "[自定义]";
        }
        break;
      case TUIChatEngine.TYPES.MSG_AUDIO:
        {
          msgContent = "[语音]";
        }
        break;
      case TUIChatEngine.TYPES.MSG_TEXT:
        {
          msgContent = payload.text;
        }
        break;
      case TUIChatEngine.TYPES.MSG_FACE:
        {
          msgContent = "[表情]";
        }
        break;
      case TUIChatEngine.TYPES.MSG_IMAGE:
        {
          msgContent = "[图片]";
        }
        break;
      case TUIChatEngine.TYPES.MSG_VIDEO:
        {
          msgContent = "[视频]";
        }
        break;
      case TUIChatEngine.TYPES.MSG_LOCATION:
        {
          msgContent = "[位置]";
        }
        break;
      case TUIChatEngine.TYPES.MSG_MERGER:
        {
          msgContent = "[聊天记录]";
        }
        break;
      case TUIChatEngine.TYPES.MSG_GRP_TIP:
        {
          // todo: 群消息
        }
        break;
      case TUIChatEngine.TYPES.MSG_FILE:
        {
          msgContent = `[文件] ${payload.name}`;
        }
        break;
    }
  }

  return (
    <div className="flex gap-[2px]">
      {icon}
      {msgContent}
    </div>
  );
};
