import {
  checkFileCloudTranscoding,
  uploadFileCloudTranscodingChunk,
} from "@/lib/api/resource";
import { fetch } from "@/lib/request";
import { Resource } from "@/type/resource";
import { ResourceUploadInfo } from "@/type/resource-upload-info";
import { Toast } from "antd-mobile";
import md5 from "md5";

const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;
const CHUNK_SIZE = 5 * 1024 * 1024; // 5MB

export const uploadFile = async (
  file: File,
  uploadFileInfo: ResourceUploadInfo,
  options?: {
    showDialog?: boolean;
    duration?: number;
    onProgress?: (progress: number) => void;
  }
): Promise<Resource | undefined> => {
  const { cloudTranscoding, url, params } = uploadFileInfo;
  const { showDialog = false, duration, onProgress } = options ?? {};

  if (showDialog) {
    Toast.show({
      icon: "loading",
      content: "上传中…",
      duration: 0,
    });
  }

  if (!cloudTranscoding) {
    // 如果不是云转码，就走普通资源上传方式
    const formData = new FormData();

    formData.append("file", file);

    Object.keys(params ?? {}).forEach((key) => {
      formData.append(key, params[key]);
    });

    formData.append("x:duration", duration ? duration.toString() : "");

    const { ok, data } = await fetch.post<Resource>(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total!
        );
        onProgress?.(percentCompleted);
      },
    });

    if (ok && data) {
      return data;
    }
  } else {
    // 如果是云转码，就走云转码上传方式
    return sliceUpload(file, uploadFileInfo, {
      showDialog,
      duration,
      onProgress,
    });
  }
};

const sliceUpload = async (
  file: File,
  uploadInfo: ResourceUploadInfo,
  options: {
    showDialog?: boolean;
    duration?: number;
    onProgress?: (progress: number) => void;
  }
): Promise<Resource | undefined> => {
  const { showDialog, duration, onProgress } = options;
  const filename = file.name;
  const fileSize = file.size;
  const chunks = Math.ceil(fileSize / CHUNK_SIZE);

  // 计算文件 MD5，替换掉 =
  const fileMd5 = (await calculateMD5(file)).replace(/=/g, "");
  const uuid = fileMd5;

  if (showDialog) {
    Toast.show({
      icon: "loading",
      content: "上传中…",
      duration: 0,
    });
  }

  const checkResult = await checkFileCloudTranscoding({
    id: uploadInfo.id!,
    md5: fileMd5,
    filename,
  });

  if (checkResult.ok && checkResult.data) {
    return checkResult.data as Resource;
  }

  let uploadedSize = 0;

  for (let chunkIndex = 0; chunkIndex < chunks; chunkIndex++) {
    const start = chunkIndex * CHUNK_SIZE;
    const end = Math.min(start + CHUNK_SIZE, fileSize);
    const chunk = file.slice(start, end);

    let retries = 0;
    let success = false;

    while (retries < MAX_RETRIES && !success) {
      try {
        success = await chunkUpload(uploadInfo, {
          chunk,
          chunkIndex,
          chunks,
          fileSize,
          fileMd5,
          uuid,
          filename,
          onProgress: (chunkProgress) => {
            const overallProgress =
              (uploadedSize + chunkProgress * (end - start)) / fileSize;
            onProgress?.(overallProgress * 100);
          },
        });

        if (!success) {
          throw new Error("分片上传失败");
        }
      } catch (error) {
        retries++;
        if (retries >= MAX_RETRIES) {
          throw new Error(`上传失败，已重试${MAX_RETRIES}次`);
        }
        await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
      }
    }

    if (!success) {
      throw new Error("上传失败，请重试");
    }

    uploadedSize += end - start;
  }

  if (showDialog) {
    Toast.show({
      icon: "loading",
      content: "上传中…",
      duration: 0,
    });
  }
  // 合并分片
  const mergeResult = await uploadFileCloudTranscodingChunk({
    id: uploadInfo.id!,
    uniqueFileName: uuid,
    filename,
    md5: fileMd5,
    chunks,
    duration,
  });

  if (mergeResult.ok && mergeResult.data) {
    return mergeResult.data as Resource;
  }
};

const chunkUpload = async (
  uploadInfo: ResourceUploadInfo,
  options: {
    chunk: Blob;
    chunkIndex: number;
    chunks: number;
    fileSize: number;
    fileMd5: string;
    uuid: string;
    filename: string;
    onProgress?: (progress: number) => void;
  }
): Promise<boolean> => {
  const { chunk, chunkIndex, chunks, fileMd5, uuid, filename, onProgress } =
    options;

  // 检查分片是否已上传
  const checkResult = await fetch.get(
    `${
      uploadInfo.url
    }?status=chunkCheck&name=${uuid}&chunkIndex=${chunkIndex}&size=${
      chunk.size
    }&_=${+new Date()}`
  );

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  if (checkResult?.ifExist === 1) {
    onProgress?.(1);
    return true;
  }

  // 上传分片
  const formData = new FormData();
  Object.entries(uploadInfo.params).forEach(([key, value]) => {
    formData.append(key, value);
  });
  formData.append("md5", fileMd5);
  formData.append("uniqueFileName", uuid);
  formData.append("name", filename);
  formData.append("chunk", chunkIndex.toString());
  formData.append("chunks", chunks.toString());
  formData.append("file", chunk, "blob");

  const uploadResult = await fetch.post(uploadInfo.url, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
    onUploadProgress: (progressEvent) => {
      const percentCompleted = progressEvent.total
        ? progressEvent.loaded / progressEvent.total
        : 0;
      onProgress?.(percentCompleted);
    },
  });

  return (uploadResult as unknown as string) === "ok";
};

const calculateMD5 = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result;
      if (result instanceof ArrayBuffer) {
        // 只取前 CHUNK_SIZE 大小的数据
        const chunk = new Uint8Array(result);
        const hash = md5(chunk);
        resolve(hash);
      } else {
        reject(new Error("Failed to read file"));
      }
    };
    reader.onerror = (e) => reject(e);
    // 只读取文件的前 CHUNK_SIZE 大小
    const blob = file.slice(0, Math.min(file.size, CHUNK_SIZE));
    reader.readAsArrayBuffer(blob);
  });
};
