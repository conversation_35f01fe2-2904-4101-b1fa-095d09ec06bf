import { commonIconList } from "@/components/image-list";

export enum UserGender {
  male = 0,
  female = 1,
  cdts = 2,
}

// 性别
export const genderConfig = [
  {
    value: UserGender.male,
    title: "男",
    url: commonIconList.gender0,
    label: "M",
    color: "195, 100%, 55%",
  },
  {
    value: UserGender.female,
    title: "女",
    url: commonIconList.gender1,
    label: "S",
    color: "330, 100%, 50%",
  },
  {
    value: UserGender.cdts,
    title: "CDTS",
    url: commonIconList.gender2,
    label: "C",
    color: "300, 100%, 50%",
  },
];

export enum UserRole {
  other = 0,
  s = 1,
  m = 2,
}

// 角色
export const roleConfig = [
  { value: UserRole.s, title: "S" },
  { value: UserRole.m, title: "M" },
  { value: UserRole.other, title: "其它" },
];

export enum UserSexualOrientation {
  opposite = 0,
  homosexual = 1,
  diversity = 2,
}

// 性取向
export const sexualConfig = [
  { value: UserSexualOrientation.opposite, title: "异性" },
  { value: UserSexualOrientation.homosexual, title: "同性" },
  { value: UserSexualOrientation.diversity, title: "多性" },
];

export enum UserExtent {
  light = 0,
  center = 1,
  heavy = 2,
}

// 程度
export const extentConfig = [
  { value: UserExtent.light, title: "轻" },
  { value: UserExtent.center, title: "中" },
  { value: UserExtent.heavy, title: "重" },
];

export enum UserLevelType {
  /** 积分 */
  integral = 1,
  /** 豪气 */
  heroism = 2,
  /** 魅力 */
  charm = 3,
}

export const userLevelTypeConfig = {
  [UserLevelType.integral]: {
    label: "积分等级",
    image: "integral",
  },
  [UserLevelType.heroism]: {
    label: "豪气等级",
    image: "heroism",
    colors: ["#F5E4A9", "#DA7D36"],
  },
  [UserLevelType.charm]: {
    label: "魅力等级",
    image: "charm",
    colors: ["#F2CFC8", "#EA95A2"],
  },
};

export enum UserOnlineState {
  /** 在线 */
  online = 0,
  /** 离线 */
  offline = 1,
  /** 隐身 */
  stealth = 2,
}

export const userOnlineStateConfig = {
  [UserOnlineState.online]: {
    label: "在线",
    color: "#46DE3F",
  },
  [UserOnlineState.offline]: {
    label: "离线",
    color: "#FF595D",
  },
  [UserOnlineState.stealth]: {
    label: "隐身",
    color: "#FF9800",
  },
};

export enum DynamicState {
  review = 0,
  normal = 1,
  violations = 2,
}

// 交易状态
export enum UserTransactionState {
  "等待支付保证金" = 1,
  "等待交易" = 2,
  "已下架" = 3,
  "交易中" = 4,
  "已完成" = 5,
  "已关闭" = 6,
}

// 交易类型枚举
export enum UserTransactionType {
  "求购" = 1,
  "出售" = 2,
}

export const userTransactionTypeConfig = {
  [UserTransactionType["求购"]]: "求购",
  [UserTransactionType["出售"]]: "出售",
};

export enum LoginMethod {
  "QQ登录" = 1,
  "微信登录" = 2,
  "密码登录" = 3,
  "手机号快捷登录" = 4,
  "手机短信验证码登录" = 5,
  "设备ID登录" = 6,
  "身份卡" = 7,
}

export enum UserAuthType {
  weChat = 0,
  qq = 1,
  deviceId = 3,
  identifyCard = 4,
}

export enum LiveRoomType {
  "视频" = 1,
  "语音" = 2,
  "多人语音" = 3,
}

// 直播间收费类型
export const liveRoomChargeType = {
  // 免费
  free: {
    value: 0,
    label: "免费",
    sign: "",
  },
  // 按时
  time: {
    value: 1,
    label: "按时",
    sign: "分",
  },
  // 按场
  sessions: {
    value: 2,
    label: "按场",
    sign: "场",
  },
};

// 用户钱包类型
export const userWalletType: { [x: string]: any } = {
  bonus: {
    value: 0,
    label: "代币",
  },
  currency: {
    value: 1,
    label: "足币",
  },
  bean: {
    value: 2,
    label: "足豆",
  },
  lifetimeChatQuota: {
    value: 3,
    label: "聊天额度(永久)",
  },
  todayChatQuota: {
    value: 4,
    label: "聊天额度(当日)",
  },
  turntableLotteryNumber: {
    value: 5,
    label: "转盘抽奖",
  },
  treasureLotteryNumber: {
    value: 6,
    label: "宝箱抽奖",
  },
};

/**
 * VIP权限
 */
export enum VipPermission {
  AccessRecords = "vip.access.records",
  VideoFree = "vip.video.free",
  Revoke = "vip.im.revoke",
}

/** 用户任务状态 */
export enum UserTaskState {
  /** 进行中 */
  Processing = 0,
  /** 待领取 */
  WaitReward = 1,
  /** 已完成 */
  Finish = 2,
}

export const userTaskStateConfig = {
  [UserTaskState.Processing]: "进行中",
  [UserTaskState.WaitReward]: "待领取",
  [UserTaskState.Finish]: "已完成",
};

/** 用户订单状态 */
export enum UserOrderState {
  /** 待支付 */
  WaitingPay = 0,
  /** 待发货 */
  WaitForDelivery = 1,
  /** 已发货 */
  Shipped = 2,
  /** 已完成 */
  Complete = 3,
  /** 已关闭 */
  Closed = 4,
}

export const userOrderStateConfig = {
  [UserOrderState.WaitingPay]: "待支付",
  [UserOrderState.WaitForDelivery]: "待发货",
  [UserOrderState.Shipped]: "已发货",
  [UserOrderState.Complete]: "已完成",
  [UserOrderState.Closed]: "已关闭",
};

/** 用户订单售后状态 */
export enum UserOrderRefundState {
  Wait = 0,
  Reject = 1,
  Pass = 2,
}

export const userOrderRefundStateConfig = {
  [UserOrderRefundState.Wait]: "待审核",
  [UserOrderRefundState.Reject]: "已拒绝",
  [UserOrderRefundState.Pass]: "已通过",
};

/** 用户联系方式状态 */
export enum UserContactState {
  Normal = 0,
  Violation = 1,
  Closed = 2,
}

export const userContactStateConfig = {
  [UserContactState.Normal]: "正常",
  [UserContactState.Violation]: "违规",
  [UserContactState.Closed]: "已关闭",
};

/** 礼物类型 */
export enum GiftType {
  General = 0,
  Heart = 1,
  LoveToken = 2,
  Live = 3,
}

export const giftTypeConfig = {
  [GiftType.General]: "普通",
  [GiftType.Heart]: "心意",
  [GiftType.LoveToken]: "定情信物",
  [GiftType.Live]: "直播礼物",
};

/** 分享类型 */
export enum ShareType {
  Friend = 0,
  Wechat = 1,
  Moment = 2,
  QQ = 3,
  Qzone = 4,
  Link = 5,
  Poster = 6,
  Community = 7,
}

export const shareTypeConfig = {
  [ShareType.Friend]: "Other",
  [ShareType.Wechat]: "WechatSession",
  [ShareType.Moment]: "WechatTimeline",
  [ShareType.QQ]: "QQ",
  [ShareType.Qzone]: "Qzone",
  [ShareType.Link]: "Copy",
  [ShareType.Poster]: "Other",
};

// 分享目标类型
export enum ShareTargetType {
  Dynamic = 0,
  Transaction = 3,
  Video = 4,
  ChatRoom = 6,
  ShortVideo = 7,
  Live = 8,
  Playback = 9,
}

// 支付类型
export enum PayType {
  AliPay = 1,
  Wechat = 2,
  Web = 3,
}

/** 支付业务类型 */
export enum PayBusinessType {
  // 开通 vip
  Vip = 1,
  // 充值
  Recharge = 2,
  // 解封
  Unban = 3,
  // 用户保证金
  UserMargin = 4,
  // 用户交易保证金
  UserTransactionDeposit = 5,
  // 用户订单
  UserOrder = 6,
  // 用户合伙人
  UserPartner = 7,
  // 折扣活动
  DiscountActivity = 8,
}

/** 用户互动权限状态 */
export enum UserChatPermissionState {
  Wait = 0,
  Normal = 1,
  Expired = 2,
}

export const userChatPermissionStateConfig = {
  [UserChatPermissionState.Wait]: "等待中",
  [UserChatPermissionState.Normal]: "已开通",
  [UserChatPermissionState.Expired]: "已失效",
};

export enum UserTransactionTenderState {
  未采纳 = 1,
  已采纳 = 2,
}

export enum CommonAuditState {
  // 审核中
  Reviewing = 0,
  // 已拒绝
  Reject = 1,
  // 已通过
  Pass = 2,
}

export const commonAuditStateConfig = [
  { value: CommonAuditState.Reviewing, image: commonIconList.state_0 },
  { value: CommonAuditState.Reject, image: commonIconList.state_2 },
  { value: CommonAuditState.Pass, image: commonIconList.state_1 },
];

export enum ReportTargetType {
  Video = 0,
  Dynamic = 1,
  User = 2,
  Transaction = 3,

  LivePlayback = 4,
  Contact = 5,
  ShortVideo = 6,
}

export enum RtcRoomType {
  Audio = 1,
  Video = 2,
}

export enum RtcRoomScene {
  C2c = 1,
  Group = 2,
}

export enum TSignaling {
  /**
   * Signaling event: Received an invitation
   */
  NEW_INVITATION_RECEIVED = "newInvitationReceived",
  /**
   * Signaling event: The invitee accepted the invitation
   */
  INVITEE_ACCEPTED = "ts_invitee_accepted",
  /**
   * Signaling event: The invitee declined the invitation
   */
  INVITEE_REJECTED = "ts_invitee_rejected",
  /**
   * Signaling event: The invitation was canceled
   */
  INVITATION_CANCELLED = "ts_invitation_cancelled",
  /**
   * Signaling event: The invitation timed out
   */
  INVITATION_TIMEOUT = "ts_invitation_timeout",
}

export enum RtcCallState {
  /// 空闲
  Idle = "idle",

  /// 等待中
  Wait = "wait",

  /// 连接中
  Connecting = "connecting",

  /// 通话中
  Talking = "talking",

  /// 挂断
  HangUp = "hangUp",
}

// 直播节点消息类型
export enum LiveNodeDataType {
  /** 提示消息 */
  tips = 0,

  /** 文本消息 */
  text = 1,

  /** 礼物消息 */
  gift = 2,

  /** 成员变更 */
  memberChange = 3,

  /** 关闭 */
  closed = 4,

  /** 入场名片 */
  joinSpecial = 5,

  /** 坐骑 */
  mounts = 6,

  /** 计费变更 */
  chargeChange = 7,

  /** 踢出直播间 */
  killUser = 8,
}
