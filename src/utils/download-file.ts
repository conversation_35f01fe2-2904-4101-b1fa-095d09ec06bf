export const downloadFile = async (
  url: string,
  filename: string,
  onProgress?: (progress: number) => void
) => {
  const response = await fetch(url, {
    mode: "cors", // 允许跨源请求
  });

  const contentLength = Number(response.headers.get("Content-Length")) || 0;
  let receivedLength = 0;

  // 创建一个新的 ReadableStream
  const stream = new ReadableStream({
    start(controller) {
      const reader = response.body!.getReader();

      const read = async () => {
        try {
          const { done, value } = await reader.read();
          if (done) {
            controller.close();
            return;
          }
          receivedLength += value.length;
          if (onProgress) {
            onProgress(contentLength > 0 ? receivedLength / contentLength : 0);
          }
          controller.enqueue(value);
          read();
        } catch (error) {
          controller.error(error);
        }
      };

      read();
    },
  });

  const blob = await new Response(stream).blob();
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
};
