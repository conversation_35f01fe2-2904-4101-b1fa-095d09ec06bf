import dayjs from "dayjs";

export const getCalendarDays = (date: Date | dayjs.Dayjs) => {
  const currentDate = dayjs(date);
  const startOfMonth = currentDate.startOf("month");
  const endOfMonth = currentDate.endOf("month");

  const startDay = startOfMonth.day();
  const daysInMonth = endOfMonth.date();

  const days: dayjs.Dayjs[] = [];

  // 添加上个月的日期
  for (let i = 0; i < startDay; i++) {
    const day = startOfMonth.subtract(startDay - i, "day");
    days.push(day);
  }

  // 添加当前月的日期
  for (let i = 1; i <= daysInMonth; i++) {
    const day = startOfMonth.date(i);
    days.push(day);
  }

  // 添加下个月的日期，使总天数为7的倍数
  const remainingDays = 7 - (days.length % 7);
  if (remainingDays < 7) {
    for (let i = 1; i <= remainingDays; i++) {
      const day = endOfMonth.add(i, "day");
      days.push(day);
    }
  }

  return days;
};
