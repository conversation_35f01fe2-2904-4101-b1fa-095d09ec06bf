// 星期和日期映射
const dayMap = ["昨天", "前天"];
const weekdayMap = [
  "星期一",
  "星期二",
  "星期三",
  "星期四",
  "星期五",
  "星期六",
  "星期天",
];

/**
 * 获取聊天界面显示的时间格式
 * @param timeStamp - Unix时间戳(秒)
 */
export const getTimeStringForChat = (timeStamp: number): string | null => {
  const date = new Date(timeStamp * 1000);
  const epochLimit = new Date(Date.UTC(1971, 0));

  if (date < epochLimit) {
    return null;
  }

  const now = new Date();
  const duration = now.getTime() - date.getTime();
  const diffMinutes = Math.floor(duration / (1000 * 60));
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const diffDays =
    Math.floor(duration / (1000 * 60 * 60 * 24)) +
    (diffMinutes >
    Math.floor((now.getTime() - todayStart.getTime()) / (1000 * 60))
      ? 1
      : 0);

  // 一周以内
  if (diffDays > 0 && diffDays < 7) {
    if (diffDays <= 2) {
      return dayMap[diffDays - 1];
    }
    return weekdayMap[date.getDay() - 1];
  }

  // 一周以外
  if (diffDays >= 7) {
    // 当年内
    if (date.getFullYear() === now.getFullYear()) {
      return `${String(date.getMonth() + 1).padStart(2, "0")}/${String(
        date.getDate()
      ).padStart(2, "0")}`;
    }
    return `${date.getFullYear()}/${String(date.getMonth() + 1).padStart(
      2,
      "0"
    )}/${String(date.getDate()).padStart(2, "0")}`;
  }

  // 当天内
  if (diffMinutes > 1) {
    if (diffMinutes < 60) {
      return `${diffMinutes} 分钟前`;
    }
    return `${date.getHours()}:${String(date.getMinutes()).padStart(2, "0")}`;
  }

  return "现在";
};

/**
 * 获取消息详情的时间格式
 * @param timeStamp - Unix时间戳(秒)
 */
export const getTimeForMessage = (timeStamp: number): string => {
  const now = new Date();
  const nowTime = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const ftime = new Date(timeStamp * 1000);
  const timeStr = `${String(ftime.getHours()).padStart(2, "0")}:${String(
    ftime.getMinutes()
  ).padStart(2, "0")}`;

  // 一年外
  if (nowTime.getFullYear() !== ftime.getFullYear()) {
    return `${ftime.getFullYear()}-${String(ftime.getMonth() + 1).padStart(
      2,
      "0"
    )}-${String(ftime.getDate()).padStart(2, "0")} ${timeStr}`;
  }

  // 一年内一周外
  if (ftime < new Date(nowTime.getTime() - 6 * 24 * 60 * 60 * 1000)) {
    return `${String(ftime.getMonth() + 1).padStart(2, "0")}-${String(
      ftime.getDate()
    ).padStart(2, "0")} ${timeStr}`;
  }

  // 一周内一天外
  if (ftime < new Date(nowTime.getTime() - 24 * 60 * 60 * 1000)) {
    return `${weekdayMap[ftime.getDay() - 1]} ${timeStr}`;
  }

  // 昨天
  if (nowTime.getDate() !== ftime.getDate()) {
    return `昨天 ${timeStr}`;
  }

  // 当天
  return timeStr;
};
