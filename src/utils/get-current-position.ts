import dayjs from "dayjs";
import AMapLoader from "@amap/amap-jsapi-loader";
import { AmapLocationResult } from "@/type/amap-location-result";

export const getCurrentPosition = async (
  key: string,
  securityJsCode: string
) => {
  // 先初始化
  (window as any)._AMapSecurityConfig = {
    securityJsCode,
  };

  const AMap = await AMapLoader.load({
    key,
    version: "2.0",
    plugins: ["AMap.Geolocation"],
  });

  const geolocation = new AMap.Geolocation({
    // 使用高精度定位
    enableHighAccuracy: true,
    timeout: 30 * 1000,
    needAddress: true,
    getCityWhenFail: true,
    extensions: "all",
  });

  return new Promise<AmapLocationResult>((resolve) => {
    geolocation.getCurrentPosition(function (status: string, result: any) {
      if (status === "complete") {
        const {
          accuracy,
          location_type,
          position = {},
          addressComponent = {},
          formattedAddress,
        } = result ?? {};

        const locationData: AmapLocationResult = {
          locTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
          callbackTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
          accuracy,
          longitude: position.lng,
          latitude: position.lat,
          adCode: addressComponent.adcode,
          province: addressComponent.province,
          city: addressComponent.city,
          district: addressComponent.district,
          street: addressComponent.street,
          streetNumber: addressComponent.streetNumber,
          locationType: location_type,
          address: formattedAddress,
        };

        resolve(locationData);
      }
    });
  });
};
