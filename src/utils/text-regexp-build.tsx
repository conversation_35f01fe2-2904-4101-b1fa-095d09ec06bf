// 邮箱匹配
const emailTextBuild = {
  regexp: /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/gi,
  render: (data: string) => {
    return (
      <span
        className="text-[#6712ff]"
        onClick={(e) => {
          e.stopPropagation();
          window.open(`mailto:${data}`);
        }}
      >
        {data}
      </span>
    );
  },
};

// @ 处理
const atTextBuild = {
  regexp: /@[^\s@]+\s/gi,
  render: (data: string, onClick?: (text: string) => void) => {
    return (
      <span
        className="text-[#6712ff]"
        onClick={(e) => {
          e.stopPropagation();
          onClick?.(data);
        }}
      >
        {data}
      </span>
    );
  },
};

// IP 处理
const ipTextBuild = {
  regexp:
    /(https?:\/\/)?((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/gi,
  render: (data: string) => {
    return (
      <span
        className="text-[#6712ff]"
        onClick={(e) => {
          e.stopPropagation();

          const reg = /^https?:\/\//;
          window.open(reg.test(data) ? data : `http://${data}`);
        }}
      >
        {data}
      </span>
    );
  },
};

// URL 处理
const urlTextBuild = {
  regexp:
    /(https?:\/\/)?[\w\-@]+(\.[\w\-@]+)*(\.[a-zA-Z]+)(\/[\w\-@]+)*(\?(&?[\w\-@]+=[\w%+-]*)*)?/gi,
  render: (data: string) => {
    return (
      <span
        className="text-[#6712ff]"
        onClick={() => {
          const reg = /^https?:\/\//;
          window.open(reg.test(data) ? data : `http://${data}`);
        }}
      >
        {data}
      </span>
    );
  },
};

// 手机号匹配
const phoneTextBuild = {
  regexp:
    /(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}/gi,
  render: (data: string) => {
    return (
      <span
        className="text-[#6712ff]"
        onClick={(e) => {
          e.stopPropagation();
          window.open(`tel:${data}`);
        }}
      >
        {data}
      </span>
    );
  },
};

// topic 匹配
const topicTextBuild = {
  regexp: /#[^#@\s]+#/gi,
  render: (data: string, onClick?: (text: string) => void) => {
    return (
      <span
        className="text-[#6712ff]"
        onClick={(e) => {
          e.stopPropagation();
          onClick?.(data);
        }}
      >
        {data}
      </span>
    );
  },
};

const builds: {
  [x: string]: TextBuild;
} = {
  emailTextBuild,
  atTextBuild,
  ipTextBuild,
  urlTextBuild,
  phoneTextBuild,
  topicTextBuild,
};

export type TextBuild = {
  regexp: RegExp;
  render: (data: string, onClick?: (text: string) => void) => JSX.Element;
};

export type BuildType =
  | "phoneTextBuild"
  | "topicTextBuild"
  | "atTextBuild"
  | "urlTextBuild"
  | "ipTextBuild"
  | "emailTextBuild";

/** 所有匹配规则，具有排序（降序） */
export const allBuildTypes: BuildType[] = [
  "emailTextBuild",
  "ipTextBuild",
  "urlTextBuild",
  "atTextBuild",
  "topicTextBuild",
  "phoneTextBuild",
];

export const getBuildByType = (type: string) => builds[type];
