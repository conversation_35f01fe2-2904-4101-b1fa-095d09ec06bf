import dayjs from "dayjs";
import isYesterday from "dayjs/plugin/isYesterday";

dayjs.extend(isYesterday);

export const formatTimeLine = (time: number) => {
  const currentTime = +new Date();
  const elapsed = currentTime - time;

  if (elapsed < 0) {
    return "刚刚";
  }

  // 是否同年
  const isEqualYear = dayjs(currentTime).year() === dayjs(time).year();

  if (!isEqualYear) {
    // 不是同一年
    return dayjs(time).format("YYYY-MM-DD");
  }

  // 是否昨天
  const isYesterday = dayjs(time).isYesterday();

  if (isYesterday) {
    return "昨天";
  }

  const seconds = elapsed / 1000;
  const minutes = seconds / 60;
  const hours = minutes / 60;
  const days = hours / 24;

  const suffix = "前";

  if (seconds < 90) {
    return `1分钟${suffix}`;
  }

  if (minutes < 60) {
    return `${Math.round(minutes)}分钟${suffix}`;
  }

  if (minutes < 90) {
    return `1小时${suffix}`;
  }

  if (hours < 24) {
    return `${Math.round(hours)}小时${suffix}`;
  }

  if ([1, 2].includes(Math.round(days))) {
    return `${Math.round(days)}天${suffix}`;
  }

  return dayjs(time).format("MM-DD");
};
