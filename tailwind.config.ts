import type { Config } from "tailwindcss";
import plugin from "tailwindcss/plugin";

const config = {
  darkMode: ["class"],
  content: ["./src/**/*.{ts,tsx}"],
  prefix: "",
  theme: {
    container: {
      center: true,
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: {
          DEFAULT: "hsl(var(--input))",
          placeholder: "hsl(var(--input-placeholder))",
        },
        ring: "hsl(var(--ring))",
        "scaffold-background": "hsl(var(--scaffold-background))",
        "hint-color": "hsl(var(--hint-color))",
        "disabled-color": "var(--disabled-color)",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        "bottom-navigation": "hsl(var(--bottom-navigation))",
        "bottom-navigation-foreground":
          "hsl(var(--bottom-navigation-foreground))",
        "bottom-navigation-selected-foreground":
          "hsl(var(--bottom-navigation-selected-foreground))",
        "filter-tab": "hsl(var(--filter-tab))",
        "filter-tab-foreground": "hsl(var(--filter-tab-foreground))",
        "filter-tab-active": "hsl(var(--filter-tab-active))",
        "filter-tab-active-foreground":
          "hsl(var(--filter-tab-active-foreground))",
        "app-bar-background": "hsl(var(--app-bar-background))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        divider: "var(--divider)",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    plugin(({ matchUtilities, theme }) => {
      matchUtilities(
        {
          "border-b-solid-1": (value) => ({
            borderStyle: "solid",
            borderBottomWidth: "1px",
            borderColor: value,
          }),
        },
        {
          values: theme("colors"),
          type: "color",
        }
      );
    }),
  ],
} satisfies Config;

export default config;
