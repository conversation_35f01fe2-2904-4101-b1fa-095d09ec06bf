pipeline {
    agent any

    tools {nodejs "23.3.0"}

    environment {
        VERSION = System.currentTimeMillis().toString()
    }

    stages {
        stage('Node Preparation') {
            steps {
                sh "node -v"
                sh "npm -v"
                sh "npm config ls"
                sh "npm --registry=https://registry.npmmirror.com install -g pnpm"
            }
        }

        stage('Node Install') {
            steps {
                sh "pnpm install"
            }
        }

        stage('Node Build') {
            steps {
                sh "pnpm run build"
                sh "rm -rf docker/dist"
                sh "cp -r dist docker/dist"
            }
        }

        // 构建Docker镜像
        stage('Docker Build') {
            steps {
              script {
                  dir('docker') {
                      sh "docker build -t registry.cn-chengdu.aliyuncs.com/huic/zqsq-h5:${VERSION} ."
                      sh "docker build -t registry.cn-chengdu.aliyuncs.com/huic/zqsq-h5:latest ."
                  }
              }
            }
        }

        // 推送Docker镜像
        stage('Docker Push') {
            steps {
                // 登录阿里云容器镜像中心
                sh "docker login --username=成都汇创意科技有限公司 --password=8o7RMtBAxLHNoJ registry.cn-chengdu.aliyuncs.com"

                sh "docker push registry.cn-chengdu.aliyuncs.com/huic/zqsq-h5:${VERSION}"
                sh "docker push registry.cn-chengdu.aliyuncs.com/huic/zqsq-h5:latest"
            }
        }

        // 清理Docker镜像
        stage('Docker Clean') {
            steps {
                sh "yes | docker image prune -a"
            }
        }
    }
}
