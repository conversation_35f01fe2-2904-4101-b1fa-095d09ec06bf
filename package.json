{"name": "zu-qu", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:tsc": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@amap/amap-jsapi-loader": "1.0.1", "@hookform/resolvers": "3.9.0", "@radix-ui/react-aspect-ratio": "1.0.3", "@radix-ui/react-avatar": "1.0.4", "@radix-ui/react-label": "2.0.2", "@radix-ui/react-scroll-area": "1.2.0-rc.7", "@radix-ui/react-slot": "1.0.2", "@react-spring/web": "9.7.4", "@tanstack/react-query": "5.49.2", "@tencentcloud/chat-uikit-engine": "2.4.2", "@tencentcloud/chat-uikit-react": "2.3.1", "@tencentcloud/uikit-base-component-react": "0.0.1", "@use-gesture/react": "10.3.1", "@vitejs/plugin-legacy": "5.4.1", "agora-rtc-react": "2.4.0", "ahooks": "3.8.0", "antd-mobile": "5.37.1", "axios": "1.7.2", "class-variance-authority": "0.7.0", "classnames": "2.5.1", "clsx": "2.1.1", "dayjs": "1.11.11", "dotenv": "16.4.5", "immer": "10.1.1", "jotai": "2.8.4", "js-cookie": "3.0.5", "less": "4.2.0", "lodash-es": "4.17.21", "lucide-react": "0.400.0", "md5": "2.3.0", "modern-screenshot": "4.4.39", "motion": "12.16.0", "postcss-preset-env": "9.6.0", "postcss-pxtorem": "6.1.0", "postcss-viewport-height-correction": "1.1.1", "react": "18.3.1", "react-activation": "0.13.0", "react-dom": "18.3.1", "react-hook-form": "7.52.1", "react-router-dom": "6.24.1", "sm-crypto": "0.3.13", "svga": "2.1.1", "tailwind-merge": "2.3.0", "tailwindcss-animate": "1.0.7", "trtc-sdk-v5": "5.9.1", "uuid": "10.0.0", "vconsole": "3.15.1", "video.js": "8.17.3", "vite-plugin-html": "3.2.2", "zod": "3.23.8"}, "resolutions": {"@tencentcloud/chat-uikit-engine": "2.4.2"}, "devDependencies": {"@types/js-cookie": "3.0.6", "@types/lodash-es": "4.17.12", "@types/md5": "2.3.5", "@types/node": "20.14.9", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/sm-crypto": "0.3.4", "@types/uuid": "10.0.0", "@typescript-eslint/eslint-plugin": "7.13.1", "@typescript-eslint/parser": "7.13.1", "@vitejs/plugin-react": "4.3.1", "autoprefixer": "10.4.19", "eslint": "8.57.0", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-react-refresh": "0.4.7", "postcss": "8.4.39", "tailwindcss": "3.4.4", "terser": "5.31.3", "typescript": "5.5.2", "vite": "5.3.1"}, "packageManager": "pnpm@9.4.0"}